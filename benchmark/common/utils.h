#pragma once

#include <cmath>
#include <cstdio>
#include <cstdlib>
#include <fstream>
#include <random>
#include <string>
#include <vector>
#include <unordered_set>
#include <algorithm>
#include <unordered_map>
#include <fcntl.h>

#include "core/src/vindex/common/data_set.h"

namespace mochow::benchmark {
struct FileIOWriter {
    std::fstream fs;
    std::string name;

    explicit FileIOWriter(const std::string& fname) {
        name = fname;
        fs = std::fstream(name, std::ios::out | std::ios::binary);
    }

    ~FileIOWriter() {
        fs.close();
    }

    size_t operator()(void* ptr, size_t size) {
        fs.write(reinterpret_cast<char*>(ptr), size);
        return size;
    }
};

struct FileIOReader {
    std::fstream fs;
    std::string name;

    explicit FileIOReader(const std::string& fname) {
        name = fname;
        fs = std::fstream(name, std::ios::in | std::ios::binary);
    }

    ~FileIOReader() {
        fs.close();
    }

    size_t operator()(void* ptr, size_t size) {
        fs.read(reinterpret_cast<char*>(ptr), size);
        return size;
    }

    size_t size() {
        fs.seekg(0, fs.end);
        size_t len = fs.tellg();
        fs.seekg(0, fs.beg);
        return len;
    }
};


template<typename metadataT = int64_t, typename dataT = float>
inline void serialize_dataset_to_file(const mochow::vindex::DataSetPtr ds, const std::string& filename) {
    std::ofstream output(filename, std::ios::binary);
    metadataT rows = ds->get_rows();
    metadataT dim = ds->get_dim();
    dataT* ts = ds->get_tensor();
    output.write((char*)(&rows), sizeof(metadataT));
    output.write((char*)(&dim), sizeof(metadataT));
    for (int i=0; i<rows * dim; i++) {
        output.write((char*)(&ts[i]), sizeof(dataT));
    }
    output.close();
}

template<typename metadataT = int64_t, typename dataT = float>
inline void puck_serialize_dataset_to_file(const mochow::vindex::DataSetPtr ds, const std::string& filename) {
    std::ofstream output(filename, std::ios::binary);
    metadataT rows = ds->get_rows();
    metadataT dim = ds->get_dim();
    dataT* ts = ds->get_tensor();
    for (int i=0; i<rows * dim; i++) {
        if (i % dim == 0) {
            output.write((char*)(&dim), sizeof(metadataT));
        }
        output.write((char*)(&ts[i]), sizeof(dataT));
    }
    output.close();
}

// Return a n-bits bitset data with random t bits set to true
inline std::vector<uint8_t> gen_random_bitset(size_t n, size_t t) {
    assert(t <= n);
    std::vector<bool> bits_shuffle(n, false);
    for (size_t i = 0; i < t; ++i) {
        bits_shuffle[i] = true;
    }
    std::mt19937 g(42);
    std::shuffle(bits_shuffle.begin(), bits_shuffle.end(), g);
    std::vector<uint8_t> data((n + 8 - 1) / 8, 0);
    for (size_t i = 0; i < n; ++i) {
        if (bits_shuffle[i]) {
            data[i >> 3] |= (0x1 << (i & 0x7));
        }
    }
    return data;
}

}
