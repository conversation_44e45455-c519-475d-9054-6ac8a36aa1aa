#pragma once

#include <assert.h>
#include <hdf5.h>

#include <unordered_set>
#include <vector>

#include "benchmark_base.h"

#include "core/src/vindex/common/index_param.h"

namespace mochow::benchmark {
/*****************************************************
 * To run this test, please download the HDF5 from
 *  https://support.hdfgroup.org/ftp/HDF5/releases/
 * and install it to /usr/local/hdf5 .
 *****************************************************/
static const char* HDF5_POSTFIX = ".hdf5";
static const char* HDF5_DATASET_TRAIN = "train";
static const char* HDF5_DATASET_TEST = "test";
static const char* HDF5_DATASET_NEIGHBORS = "neighbors";
static const char* HDF5_DATASET_DISTANCES = "distances";
static const char* HDF5_DATASET_LIMS = "lims";
static const char* HDF5_DATASET_RADIUS = "radius";

/*
static const char* METRIC_IP_STR = "angular";
static const char* METRIC_L2_STR = "euclidean";
static const char* METRIC_HAM_STR = "hamming";
static const char* METRIC_JAC_STR = "jaccard";
static const char* METRIC_TAN_STR = "tanimoto";
*/

/************************************************************************************
 * https://github.com/erikbern/ann-benchmarks
 *
 * Dataset  Dimensions  Train_size  Test_size   Neighbors   Distance    Download
 * Fashion-
    MNIST   784         60,000      10,000      100         Euclidean   HDF5 (217MB)
 * GIST     960         1,000,000   1,000       100         Euclidean   HDF5 (3.6GB)
 * GloVe    100         1,183,514   10,000      100         Angular     HDF5 (463MB)
 * GloVe    200         1,183,514   10,000      100         Angular     HDF5 (918MB)
 * MNIST    784         60,000 	    10,000      100         Euclidean   HDF5 (217MB)
 * NYTimes  256         290,000     10,000      100         Angular     HDF5 (301MB)
 * SIFT     128         1,000,000   10,000      100         Euclidean   HDF5 (501MB)
 *************************************************************************************/

// dataset, dimensions, train_size, test_size, neighbors, distance, file_name
class DatasetHDF5 {
public:
    DatasetHDF5(
            std::string dataset_name,
            int64_t dimensions,
            int64_t train_size,
            int64_t test_size,
            int64_t neighbors,
            std::string metric,
            std::string file_name) :
        dataset_name(dataset_name),
        dimensions(dimensions),
        train_size(train_size),
        test_size(test_size),
        neighbors(neighbors),
        metric(metric),
        file_name(file_name) {}

    std::string dataset_name;
    int64_t dimensions, train_size, test_size, neighbors;
    std::string metric, file_name;
};

class BenchmarkHDF5 : public BenchmarkBase {
public:
    // to be deleted
    void set_ann_test_name(const char* test_name) {
        _ann_test_name = test_name;
    }

    void set_ann_test_prefix(const char* prefix) {
        _ann_test_prefix = prefix;
    }

    void apply_benchmark(DatasetHDF5 dataset) {
        _dim = dataset.dimensions;
        _nb = dataset.train_size;
        _nq = dataset.test_size;
        _gt_k = dataset.neighbors;
        _metric_str = dataset.metric;
        set_ann_test_name((_ann_test_prefix + dataset.file_name).c_str());
        LOG(NOTICE) << "apply benchmark"
            << " dim:" << _dim
            << " nb:" << _nb
            << " nq:" << _nq
            << " metric:" << _metric_str;
        load_hdf5_data<false>();
    }

    template <bool is_binary>
    void load_hdf5_data() {
        const std::string ann_file_name = _ann_test_name + HDF5_POSTFIX;
        int32_t dim;

        printf("[%.3f s] Loading HDF5 file: %s\n", get_time_diff(), ann_file_name.c_str());

        /* load train data */
        printf("[%.3f s] Loading train data\n", get_time_diff());
        if (!is_binary) {
            _xb = hdf5_read(ann_file_name, HDF5_DATASET_TRAIN, H5T_FLOAT, dim, _nb);
            assert(dim == _dim || !"train dataset has incorrect dimension");
        } else {
            _xb = hdf5_read(ann_file_name, HDF5_DATASET_TRAIN, H5T_INTEGER, dim, _nb);
            assert(dim * 32 == _dim || !"train dataset has incorrect dimension");
        }

        if (_metric_str == vindex::MetricType::IP) {
            LOG(NOTICE) << "[" << get_time_diff() << "s]" 
                << " Normalizing train dataset";
            //printf("[%.3f s] Normalizing train dataset \n", get_time_diff());
            normalize((float*)_xb, _nb, _dim);
        }

        /* load test data */
        printf("[%.3f s] Loading test data\n", get_time_diff());
        if (!is_binary) {
            _xq = hdf5_read(ann_file_name, HDF5_DATASET_TEST, H5T_FLOAT, dim, _nq);
            assert(dim == _dim || !"test dataset has incorrect dimension");
        } else {
            _xq = hdf5_read(ann_file_name, HDF5_DATASET_TEST, H5T_INTEGER, dim, _nq);
            assert(dim * 32 == _dim || !"test dataset has incorrect dimension");
        }

        if (_metric_str == vindex::MetricType::IP) {
            LOG(NOTICE) << "[" << get_time_diff() << "s]" 
                << " Normalizing test dataset";
            //printf("[%.3f s] Normalizing test dataset \n", get_time_diff());
            normalize((float*)_xq, _nq, _dim);
        }

        /* load ground-truth data */
        int32_t gt_nq;
        printf("[%.3f s] Loading ground truth data\n", get_time_diff());
        _gt_ids = (int32_t*)hdf5_read(ann_file_name, HDF5_DATASET_NEIGHBORS, H5T_INTEGER, _gt_k, gt_nq);
        assert(gt_nq == _nq || !"incorrect nq of ground truth labels");
        
        // /* cohere has no distance data
        _gt_dist = (float*)hdf5_read(ann_file_name, HDF5_DATASET_DISTANCES, H5T_FLOAT, _gt_k, gt_nq);
        assert(gt_nq == _nq || !"incorrect nq of ground truth distance");
        // */
    }

    template <bool is_binary>
    void load_hdf5_data_range() {
        const std::string ann_file_name = _ann_test_name + HDF5_POSTFIX;
        int32_t dim;

        printf("[%.3f s] Loading HDF5 file: %s\n", get_time_diff(), ann_file_name.c_str());

        /* load train data */
        printf("[%.3f s] Loading train data\n", get_time_diff());
        if (!is_binary) {
            _xb = hdf5_read(ann_file_name, HDF5_DATASET_TRAIN, H5T_FLOAT, dim, _nb);
            assert(dim == _dim || !"train dataset has incorrect dimension");
        } else {
            _xb = hdf5_read(ann_file_name, HDF5_DATASET_TRAIN, H5T_INTEGER, dim, _nb);
            assert(dim * 32 == _dim || !"train dataset has incorrect dimension");
        }

        if (_metric_str == vindex::MetricType::IP) {
            printf("[%.3f s] Normalizing train dataset \n", get_time_diff());
            normalize((float*)_xb, _nb, _dim);
        }

        /* load test data */
        printf("[%.3f s] Loading test data\n", get_time_diff());
        if (!is_binary) {
            _xq = hdf5_read(ann_file_name, HDF5_DATASET_TEST, H5T_FLOAT, dim, _nq);
            assert(dim == _dim || !"test dataset has incorrect dimension");
        } else {
            _xq = hdf5_read(ann_file_name, HDF5_DATASET_TEST, H5T_INTEGER, dim, _nq);
            assert(dim * 32 == _dim || !"test dataset has incorrect dimension");
        }

        if (_metric_str == vindex::MetricType::IP) {
            printf("[%.3f s] Normalizing test dataset \n", get_time_diff());
            normalize((float*)_xq, _nq, _dim);
        }

        /* load ground-truth data */
        int32_t cols, rows;
        printf("[%.3f s] Loading ground truth data\n", get_time_diff());
        _gt_radius = (float*)hdf5_read(ann_file_name, HDF5_DATASET_RADIUS, H5T_FLOAT, cols, rows);
        assert((cols == 1 && rows == 1) || !"incorrect ground truth radius");

        _gt_lims = (int32_t*)hdf5_read(ann_file_name, HDF5_DATASET_LIMS, H5T_INTEGER, cols, rows);
        assert((cols == _nq + 1 && rows == 1) || !"incorrect dims of ground truth lims");

        _gt_ids = (int32_t*)hdf5_read(ann_file_name, HDF5_DATASET_NEIGHBORS, H5T_INTEGER, cols, rows);
        assert((cols == _gt_lims[_nq] && rows == 1) || !"incorrect dims of ground truth labels");

        _gt_dist = (float*)hdf5_read(ann_file_name, HDF5_DATASET_DISTANCES, H5T_FLOAT, cols, rows);
        assert((cols == _gt_lims[_nq] && rows == 1) || !"incorrect dims of ground truth distances");
    }

    template <bool is_binary>
    void load_hdf5_data_range_multi() {
        const std::string ann_file_name = _ann_test_name + HDF5_POSTFIX;
        int32_t dim;

        printf("[%.3f s] Loading HDF5 file: %s\n", get_time_diff(), ann_file_name.c_str());

        /* load train data */
        printf("[%.3f s] Loading train data\n", get_time_diff());
        if (!is_binary) {
            _xb = hdf5_read(ann_file_name, HDF5_DATASET_TRAIN, H5T_FLOAT, dim, _nb);
            assert(dim == _dim || !"train dataset has incorrect dimension");
        } else {
            _xb = hdf5_read(ann_file_name, HDF5_DATASET_TRAIN, H5T_INTEGER, dim, _nb);
            assert(dim * 32 == _dim || !"train dataset has incorrect dimension");
        }

        if (_metric_str == vindex::MetricType::IP) {
            printf("[%.3f s] Normalizing train dataset \n", get_time_diff());
            normalize((float*)_xb, _nb, _dim);
        }

        /* load test data */
        printf("[%.3f s] Loading test data\n", get_time_diff());
        if (!is_binary) {
            _xq = hdf5_read(ann_file_name, HDF5_DATASET_TEST, H5T_FLOAT, dim, _nq);
            assert(dim == _dim || !"test dataset has incorrect dimension");
        } else {
            _xq = hdf5_read(ann_file_name, HDF5_DATASET_TEST, H5T_INTEGER, dim, _nq);
            assert(dim * 32 == _dim || !"test dataset has incorrect dimension");
        }

        if (_metric_str == vindex::MetricType::IP) {
            printf("[%.3f s] Normalizing test dataset \n", get_time_diff());
            normalize((float*)_xq, _nq, _dim);
        }

        /* load ground-truth data */
        int32_t cols, rows;
        printf("[%.3f s] Loading ground truth data\n", get_time_diff());
        _gt_radius = (float*)hdf5_read(ann_file_name, HDF5_DATASET_RADIUS, H5T_FLOAT, cols, rows);
        assert((cols == _nq && rows == 1) || !"incorrect ground truth radius");

        _gt_lims = (int32_t*)hdf5_read(ann_file_name, HDF5_DATASET_LIMS, H5T_INTEGER, cols, rows);
        assert((cols == _nq + 1 && rows == 1) || !"incorrect dims of ground truth lims");

        _gt_ids = (int32_t*)hdf5_read(ann_file_name, HDF5_DATASET_NEIGHBORS, H5T_INTEGER, cols, rows);
        assert((cols == _gt_lims[_nq] && rows == 1) || !"incorrect dims of ground truth labels");

        _gt_dist = (float*)hdf5_read(ann_file_name, HDF5_DATASET_DISTANCES, H5T_FLOAT, cols, rows);
        assert((cols == _gt_lims[_nq] && rows == 1) || !"incorrect dims of ground truth distances");
    }

    std::string get_hdf5_name(const std::vector<int32_t>& params) {
        std::string params_str = "";
        for (size_t i = 0; i < params.size(); i++) {
            params_str += "_" + std::to_string(params[i]);
        }
        return _ann_test_name + params_str + HDF5_POSTFIX;
    }

    /*
    template <bool is_binary>
    void hdf5_write(const char* file_name,
            mochow::vindex::DataSetPtr& train_ds,
            mochow::vindex::DataSetPtr& query_ds,
            mochow::vindex::DataSetPtr& result_ds) {
        assert(train_ds != nullptr);
        assert(query_ds != nullptr);
        assert(result_ds != nullptr);

        // train dataset and query dataset should have same dim
        assert(train_ds->get_dim() == query_ds->get_dim());
        // query dataset and result dataset should have same rows
        assert(query_ds->get_rows() == result_ds->get_rows());

        int32_t nb = train_ds->get_rows();
        int32_t dim = train_ds->get_dim();
        int32_t nq = query_ds->get_rows();
        // in result dataset, dim is actually topk
        int32_t k = result_ds->get_dim();

        // int64_t to int32_t
        int32_t* gt_ids = new int32_t[nq * k];
        int64_t* result_ids = result_ds->get_ids();
        for (int i=0; i<nq*k; i++) {
            gt_ids[i] = result_ids[i];
        }

        this->hdf5_write<is_binary>(file_name, dim, k,
                train_ds->get_tensor(), nb,
                query_ds->get_tensor(), nq,
                gt_ids, result_ds->get_distance());
    }*/

protected:
    void* hdf5_read(const std::string& file_name, const std::string& dataset_name, H5T_class_t dataset_class, int32_t& d_out,
              int32_t& n_out) {
        hid_t file, dataset, datatype, dataspace, memspace;
        H5T_class_t t_class;      /* data type class */
        hsize_t dimsm[3];         /* memory space dimensions */
        hsize_t dims_out[2];      /* dataset dimensions */
        hsize_t count[2];         /* size of the hyperslab in the file */
        hsize_t offset[2];        /* hyperslab offset in the file */
        hsize_t count_out[3];     /* size of the hyperslab in memory */
        hsize_t offset_out[3];    /* hyperslab offset in memory */
        void* data_out = nullptr; /* output buffer */

        /* Open the file and the dataset. */
        file = H5Fopen(file_name.c_str(), H5F_ACC_RDONLY, H5P_DEFAULT);
        dataset = H5Dopen2(file, dataset_name.c_str(), H5P_DEFAULT);

        /* Get datatype and dataspace handles and then query
         * dataset class, order, size, rank and dimensions. */
        datatype = H5Dget_type(dataset); /* datatype handle */
        t_class = H5Tget_class(datatype);
        assert(t_class == dataset_class || !"Illegal dataset class type");

        dataspace = H5Dget_space(dataset); /* dataspace handle */
        H5Sget_simple_extent_dims(dataspace, dims_out, nullptr);
        n_out = dims_out[0];
        d_out = dims_out[1];

        /* Define hyperslab in the dataset. */
        offset[0] = offset[1] = 0;
        count[0] = dims_out[0];
        count[1] = dims_out[1];
        H5Sselect_hyperslab(dataspace, H5S_SELECT_SET, offset, nullptr, count, nullptr);

        /* Define the memory dataspace. */
        dimsm[0] = dims_out[0];
        dimsm[1] = dims_out[1];
        dimsm[2] = 1;
        memspace = H5Screate_simple(3, dimsm, nullptr);

        /* Define memory hyperslab. */
        offset_out[0] = offset_out[1] = offset_out[2] = 0;
        count_out[0] = dims_out[0];
        count_out[1] = dims_out[1];
        count_out[2] = 1;
        H5Sselect_hyperslab(memspace, H5S_SELECT_SET, offset_out, nullptr, count_out, nullptr);

        /* Read data from hyperslab in the file into the hyperslab in memory and display. */
        switch (t_class) {
            case H5T_INTEGER:
                data_out = new int32_t[dims_out[0] * dims_out[1]];
                H5Dread(dataset, H5T_NATIVE_INT32, memspace, dataspace, H5P_DEFAULT, data_out);
                break;
            case H5T_FLOAT:
                data_out = new float[dims_out[0] * dims_out[1]];
                H5Dread(dataset, H5T_NATIVE_FLOAT, memspace, dataspace, H5P_DEFAULT, data_out);
                break;
            default:
                printf("Illegal dataset class type\n");
                break;
        }

        /* Close/release resources. */
        H5Tclose(datatype);
        H5Dclose(dataset);
        H5Sclose(dataspace);
        H5Sclose(memspace);
        H5Fclose(file);

        return data_out;
    }

    // For binary vector, dim should be divided by 32, since we use int32 to store binary vector data */
    template <bool is_binary>
    void hdf5_write(const char* file_name, const int32_t dim, const int32_t k, const void* xb, const int32_t nb,
               const void* xq, const int32_t nq, const void* g_ids, const void* g_dist) {
        /* Open the file and the dataset. */
        hid_t file = H5Fcreate(file_name, H5F_ACC_TRUNC, H5P_DEFAULT, H5P_DEFAULT);

        auto write_hdf5_dataset = [](hid_t file, const char* dataset_name, hid_t type_id, int32_t rows, int32_t cols,
                                     const void* data) {
            hsize_t dims[2];
            dims[0] = rows;
            dims[1] = cols;
            auto dataspace = H5Screate_simple(2, dims, NULL);
            auto dataset = H5Dcreate2(file, dataset_name, type_id, dataspace, H5P_DEFAULT, H5P_DEFAULT, H5P_DEFAULT);
            auto err = H5Dwrite(dataset, type_id, H5S_ALL, H5S_ALL, H5P_DEFAULT, data);
            assert(err == 0);
            H5Dclose(dataset);
            H5Sclose(dataspace);
        };

        /* write train dataset */
        if (!is_binary) {
            write_hdf5_dataset(file, HDF5_DATASET_TRAIN, H5T_NATIVE_FLOAT, nb, dim, xb);
        } else {
            write_hdf5_dataset(file, HDF5_DATASET_TRAIN, H5T_NATIVE_INT32, nb, dim, xb);
        }

        /* write test dataset */
        if (!is_binary) {
            write_hdf5_dataset(file, HDF5_DATASET_TEST, H5T_NATIVE_FLOAT, nq, dim, xq);
        } else {
            write_hdf5_dataset(file, HDF5_DATASET_TEST, H5T_NATIVE_INT32, nq, dim, xq);
        }

        /* write ground-truth labels dataset */
        write_hdf5_dataset(file, HDF5_DATASET_NEIGHBORS, H5T_NATIVE_INT32, nq, k, g_ids);

        /* write ground-truth distance dataset */
        write_hdf5_dataset(file, HDF5_DATASET_DISTANCES, H5T_NATIVE_FLOAT, nq, k, g_dist);

        /* Close/release resources. */
        H5Fclose(file);
    }

    // For binary vector, dim should be divided by 32, since we use int32 to store binary vector data */
    // Write HDF5 file with following dataset:
    //    HDF5_DATASET_RADIUS    - H5T_NATIVE_FLOAT, [1, 1]
    //    HDF5_DATASET_LIMS      - H5T_NATIVE_INT32, [1, nq+1]
    //    HDF5_DATASET_NEIGHBORS - H5T_NATIVE_INT32, [1, lims[nq]]
    //    HDF5_DATASET_DISTANCES - H5T_NATIVE_FLOAT, [1, lims[nq]]
    template <bool is_binary>
    void hdf5_write_range(const char* file_name, const int32_t dim, const void* xb, const int32_t nb, const void* xq,
                     const int32_t nq, const float radius, const void* g_lims, const void* g_ids, const void* g_dist) {
        /* Open the file and the dataset. */
        hid_t file = H5Fcreate(file_name, H5F_ACC_TRUNC, H5P_DEFAULT, H5P_DEFAULT);

        auto write_hdf5_dataset = [](hid_t file, const char* dataset_name, hid_t type_id, int32_t rows, int32_t cols,
                                     const void* data) {
            hsize_t dims[2];
            dims[0] = rows;
            dims[1] = cols;
            auto dataspace = H5Screate_simple(2, dims, NULL);
            auto dataset = H5Dcreate2(file, dataset_name, type_id, dataspace, H5P_DEFAULT, H5P_DEFAULT, H5P_DEFAULT);
            auto err = H5Dwrite(dataset, type_id, H5S_ALL, H5S_ALL, H5P_DEFAULT, data);
            assert(err == 0);
            H5Dclose(dataset);
            H5Sclose(dataspace);
        };

        /* write train dataset */
        if (!is_binary) {
            write_hdf5_dataset(file, HDF5_DATASET_TRAIN, H5T_NATIVE_FLOAT, nb, dim, xb);
        } else {
            write_hdf5_dataset(file, HDF5_DATASET_TRAIN, H5T_NATIVE_INT32, nb, dim, xb);
        }

        /* write test dataset */
        if (!is_binary) {
            write_hdf5_dataset(file, HDF5_DATASET_TEST, H5T_NATIVE_FLOAT, nq, dim, xq);
        } else {
            write_hdf5_dataset(file, HDF5_DATASET_TEST, H5T_NATIVE_INT32, nq, dim, xq);
        }

        /* write ground-truth radius */
        write_hdf5_dataset(file, HDF5_DATASET_RADIUS, H5T_NATIVE_FLOAT, 1, 1, &radius);

        /* write ground-truth lims dataset */
        write_hdf5_dataset(file, HDF5_DATASET_LIMS, H5T_NATIVE_INT32, 1, nq + 1, g_lims);

        /* write ground-truth labels dataset */
        write_hdf5_dataset(file, HDF5_DATASET_NEIGHBORS, H5T_NATIVE_INT32, 1, ((int32_t*)g_lims)[nq], g_ids);

        /* write ground-truth distance dataset */
        write_hdf5_dataset(file, HDF5_DATASET_DISTANCES, H5T_NATIVE_FLOAT, 1, ((int32_t*)g_lims)[nq], g_dist);

        /* Close/release resources. */
        H5Fclose(file);
    }

    // For binary vector, dim should be divided by 32, since we use int32 to store binary vector data */
    // Write HDF5 file with following dataset:
    //    HDF5_DATASET_RADIUS    - H5T_NATIVE_FLOAT, [1, nq]
    //    HDF5_DATASET_LIMS      - H5T_NATIVE_INT32, [1, nq+1]
    //    HDF5_DATASET_NEIGHBORS - H5T_NATIVE_INT32, [1, lims[nq]]
    //    HDF5_DATASET_DISTANCES - H5T_NATIVE_FLOAT, [1, lims[nq]]
    template <bool is_binary>
    void hdf5_write_range(const char* file_name, const int32_t dim, const void* xb, const int32_t nb, const void* xq,
                     const int32_t nq, const float* g_radius, const void* g_lims, const void* g_ids,
                     const void* g_dist) {
        /* Open the file and the dataset. */
        hid_t file = H5Fcreate(file_name, H5F_ACC_TRUNC, H5P_DEFAULT, H5P_DEFAULT);

        auto write_hdf5_dataset = [](hid_t file, const char* dataset_name, hid_t type_id, int32_t rows, int32_t cols,
                                     const void* data) {
            hsize_t dims[2];
            dims[0] = rows;
            dims[1] = cols;
            auto dataspace = H5Screate_simple(2, dims, NULL);
            auto dataset = H5Dcreate2(file, dataset_name, type_id, dataspace, H5P_DEFAULT, H5P_DEFAULT, H5P_DEFAULT);
            auto err = H5Dwrite(dataset, type_id, H5S_ALL, H5S_ALL, H5P_DEFAULT, data);
            assert(err == 0);
            H5Dclose(dataset);
            H5Sclose(dataspace);
        };

        /* write train dataset */
        if (!is_binary) {
            write_hdf5_dataset(file, HDF5_DATASET_TRAIN, H5T_NATIVE_FLOAT, nb, dim, xb);
        } else {
            write_hdf5_dataset(file, HDF5_DATASET_TRAIN, H5T_NATIVE_INT32, nb, dim, xb);
        }

        /* write test dataset */
        if (!is_binary) {
            write_hdf5_dataset(file, HDF5_DATASET_TEST, H5T_NATIVE_FLOAT, nq, dim, xq);
        } else {
            write_hdf5_dataset(file, HDF5_DATASET_TEST, H5T_NATIVE_INT32, nq, dim, xq);
        }

        /* write ground-truth radius */
        write_hdf5_dataset(file, HDF5_DATASET_RADIUS, H5T_NATIVE_FLOAT, 1, nq, g_radius);

        /* write ground-truth lims dataset */
        write_hdf5_dataset(file, HDF5_DATASET_LIMS, H5T_NATIVE_INT32, 1, nq + 1, g_lims);

        /* write ground-truth labels dataset */
        write_hdf5_dataset(file, HDF5_DATASET_NEIGHBORS, H5T_NATIVE_INT32, 1, ((int32_t*)g_lims)[nq], g_ids);

        /* write ground-truth distance dataset */
        write_hdf5_dataset(file, HDF5_DATASET_DISTANCES, H5T_NATIVE_FLOAT, 1, ((int32_t*)g_lims)[nq], g_dist);

        /* Close/release resources. */
        H5Fclose(file);
    }

protected:
    std::string _ann_test_name = "";
    std::string _ann_test_prefix = "";
    std::string _metric_str = vindex::MetricType::INVALID;
};
}
