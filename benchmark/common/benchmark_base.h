#pragma once

#include <math.h>
#include <sys/time.h>
#include <iostream>
#include <string>
#include <sstream>
#include <base/time.h>
#include "utils.h"

#include "core/src/common/bthread_executor.h"

namespace mochow::benchmark {

#define CALC_TIME_SPAN(X)   \
    base::Timer timer;      \
    timer.start();          \
    X;                      \
    timer.stop();            \
    double t_diff = double(timer.u_elapsed()) / 1000000;

class BenchmarkBase {
public:
    BenchmarkBase() = default;
    ~BenchmarkBase() {
        free_all();
    }

    inline void normalize(float* arr, int32_t nq, int32_t dim) {
        common::BthreadCountdownExecutor executor(nq);
        for (int32_t i = 0; i < nq; i++) {
            auto fn = [&, i]() {
                double vecLen = 0.0, inv_vecLen = 0.0;
                for (int32_t j = 0; j < dim; j++) {
                    double val = arr[i * dim + j];
                    vecLen += val * val;
                }
                // to avoid vector.length() == 0
                if (vecLen == 0.0) {
                    vecLen = 1.0;
                }
                inv_vecLen = 1.0 / std::sqrt(vecLen);
                for (int32_t j = 0; j < dim; j++) {
                    arr[i * dim + j] = (float)(arr[i * dim + j] * inv_vecLen);
                }
            };
            executor.add(fn);
        }
        executor.wait();
    }

    inline double elapsed() {
        struct timeval tv;
        gettimeofday(&tv, nullptr);
        return tv.tv_sec + tv.tv_usec * 1e-6;
    }

    double get_time_diff() {
        return elapsed() - _T0;
    }
    
    float calc_recall(std::vector<uint64_t> ids, int32_t i, int32_t k) {
        int32_t min_k = std::min(_gt_k, k);
        int32_t hit = 0;
        std::vector<int32_t> ground(_gt_ids + i * _gt_k, _gt_ids + i * _gt_k + min_k);
        std::vector<float> ground_dist(_gt_dist + i * _gt_k, _gt_dist + i * _gt_k + min_k);
        for (int32_t j = 0; j < min_k; j++) {
            auto id = ids[j];
            if (std::find(ground.begin(), ground.end(), id) != ground.end()) {
                hit++;
            }
        }

        if (hit != min_k) {
            std::stringstream ss;
            ss << "[";
            for (auto& ground_item : ground) {
                ss << ground_item << ",";
            }
            ss << "]";

            std::stringstream dss;
            dss << "[";
            for (auto& ground_item : ground_dist) {
                dss << ground_item << ",";
            }
            dss << "]";
            LOG(NOTICE) << "search index:" << i;
            LOG(NOTICE) << "gt_neighbor:" << ss.str();
            LOG(NOTICE) << "gt_dist:" << dss.str();
        }
        return (hit * 1.0f / min_k);
    }
    
    float calc_recall(std::vector<uint64_t> ids, int32_t i, int32_t k, 
            std::function<bool(uint32_t)> filter) {
        int32_t min_k = std::min(_gt_k, k);
        int32_t hit = 0;
        std::unordered_set<int32_t> ground;
        for(int32_t* neighbor = _gt_ids + i * _gt_k; neighbor < _gt_ids + i * _gt_k + _gt_k; neighbor++) {
            if (filter(*neighbor)) {
                ground.insert(*neighbor);
            }
            if ((int32_t)ground.size() == min_k) {
                break;
            }
        }
        for (int32_t j = 0; j < min_k; j++) {
            auto id = ids[j];
            if (ground.count(id) > 0) {
                hit++;
            }
        }

        if (hit != min_k) {
            std::stringstream ss;
            ss << "[";
            for (auto& ground_item : ground) {
                ss << ground_item << ",";
            }
            ss << "]";
            LOG(NOTICE) << "search index:" << i
                << " neighbor:" << ss.str();
        }
        return (hit * 1.0f / min_k);
    }

    template <typename T>
    float calc_recall(const T* ids, int32_t nq, int32_t k) {
        int32_t min_k = std::min(_gt_k, k);
        int32_t hit = 0;
        for (int32_t i = 0; i < nq; i++) {
            std::unordered_set<int32_t> ground(_gt_ids + i * _gt_k, _gt_ids + i * _gt_k + min_k);
            for (int32_t j = 0; j < min_k; j++) {
                auto id = ids[i * k + j];
                if (ground.count(id) > 0) {
                    hit++;
                }
            }
        }
        return (hit * 1.0f / (nq * min_k));
    }

    template <typename T>
    std::pair<int32_t, int32_t> calc_hit(const T* ids, const size_t* lims, int32_t nq, float radius) {
        int32_t hit = 0;
        int32_t gt_lim = 0;
        for (int32_t i = 0; i < nq; i++) {
            std::unordered_set<int32_t> gt_ids_set{};
            for (int j = 0; j < _gt_k; j++) {
                 float dist = _gt_dist[i * _gt_k + j];
                 // if this vector is part of answer
                 if (dist * dist <= radius) {
                     gt_ids_set.insert(_gt_ids[i * _gt_k + j]);
                 }
            }
            gt_lim += gt_ids_set.size();

            std::unordered_set<int32_t> ids_set{};
            for (auto j = lims[i]; j < lims[i + 1]; j++) {
                ids_set.insert(ids[j]);
            }

            for (auto j = lims[i]; j < lims[i + 1]; j++) {
                auto id = ids[j];
                if (gt_ids_set.count(id) > 0) {
                    hit++;
                }
            }
        }

        return std::make_pair(hit, gt_lim);
    }

    template <typename T>
    float calc_recall(const T* ids, const size_t* lims, int32_t nq, float radius) {
        auto [hit, gt_lim] = calc_hit(ids, lims, nq, radius);
        // gt_lim is the total number of results in bf_result
        return (hit * 1.0f / gt_lim);
    }

    template <typename T>
    float calc_accuracy(const T* ids, const size_t* lims, int32_t nq, float radius) {
        auto [hit, gt_lim] = calc_hit(ids, lims, nq, radius);
        // lims[nq] is the total number of results of range search
        return (hit * 1.0f / lims[nq]);
    }
    
    /*
    float calc_recall(vdb::DataSetPtr ds_ptr, const std::string& bf_filename) {
        auto bf_ds_ptr = deserialize_result_dataset_from_file(bf_filename);
        // bf file does not exist
        if (bf_ds_ptr == nullptr) {
            LOG(ERROR) << "cannot find related bf file. filename: " << bf_filename;
            return 0;
        }

        // check if ds_ptr and bf_ds_ptr has the same size.
        int64_t nq = ds_ptr->get_rows();
        int64_t dim = ds_ptr->get_dim();
        int64_t bf_nq = bf_ds_ptr->get_rows();
        int64_t bf_dim = bf_ds_ptr->get_dim();
        if ((nq != bf_nq) || (dim != bf_dim)) {
            LOG(ERROR) << "dataset size mismatch. filename: " << bf_filename;
            return 0;
        }

        // compare ds_ptr and bf_ds_ptr
        int64_t* bf_ids = bf_ds_ptr->get_ids();
        int64_t* ids = ds_ptr->get_ids();
        int total_hit{0};
        for (int i=0; i<nq; i++) {
            std::unordered_set<int64_t> id_set{};
            int hit{0};
            for (int j=0; j<dim; j++) {
                id_set.insert(bf_ids[i * dim + j]);
            }
            for (int j=0; j<dim; j++) {
                if(id_set.find(ids[i * dim + j]) != id_set.end()) {
                    hit++;
                }
            }
            total_hit+=hit;
        }

        return float(total_hit) / (nq * dim);
    }*/

    void free_all() {
        if (_xb != nullptr) {
            delete[](float*) _xb;
        }
        if (_xq != nullptr) {
            delete[](float*) _xq;
        }
        if (_gt_radius != nullptr) {
            delete[] _gt_radius;
        }
        if (_gt_lims != nullptr) {
            delete[] _gt_lims;
        }
        if (_gt_ids != nullptr) {
            delete[] _gt_ids;
        }
        if (_gt_dist != nullptr) {
            delete[] _gt_dist;
        }
    }

protected:
    double _T0;
    int32_t _dim;
    void* _xb = nullptr;
    void* _xq = nullptr;
    int32_t _nb;
    int32_t _nq;
    float* _gt_radius = nullptr;  // ground-truth radius
    int32_t* _gt_lims = nullptr;  // ground-truth lims
    int32_t* _gt_ids = nullptr;   // ground-truth labels
    float* _gt_dist = nullptr;    // ground-truth distances
    int32_t _gt_k;
};
}
