#include <vector>
#include <filesystem>
#include <thread>
#include <string>
#include <iostream>
#include <fstream>
#include <sstream>
#include <algorithm>
#include <base/comlog_sink.h>
#include <base/files/file_path.h>
#include <base/logging.h>
#include <base/configure/Configure.h>
#include <base/time.h>
#include <gflags/gflags.h>
#include "nlohmann/json.hpp"
#include "comlogplugin.h"
#include "core/src/common/using.h"
#include "core/src/common/logging.h"
#include "baidu/vdb/mochow/core/src/common/config.h"
#include "core/src/inverted_index/analyzer.h"

namespace mochow::benchmark::invertedindex {

DEFINE_int32(analyzer_type, 2, "analyzer type: 1/2/3 for english/chinese/unicode");
DEFINE_int32(analyzer_parse_mode, 0, "parse mode: 0/1");
DEFINE_bool(analyzer_case_sensitive, true, "analyzer is case sensitive or not");
DEFINE_string(analyzer_conf_dir, "", "analyzer config directory");
DEFINE_string(tokenize_text, "", "the text which is hoped to be tokenized");
DEFINE_string(tokenize_file, "", "the text file path");
DEFINE_int32(repeat_num, 1, "benchmark running times");

class AnalyzerBenchmark final {
public:
    AnalyzerBenchmark() = default;

    ~AnalyzerBenchmark() = default;

    Status initialize(mochow::invertedindex::AnalyzerType analyzer_type,
                      mochow::invertedindex::AnalyzerParseMode mode) {

        auto analyzer = mochow::invertedindex::Analyzer::create(analyzer_type,
                                                                mode,
                                                                FLAGS_analyzer_case_sensitive,
                                                                FLAGS_analyzer_conf_dir);

        if (analyzer == nullptr) {
            LOG(WARNING) << "Failed to create analyzer with type:" << static_cast<uint8_t>(analyzer_type)
		       << ", mode:" << static_cast<uint8_t>(mode);
            return Status(ERR_INVALID_ARGUMENT, "invalid analyzer type");
        }

        _analyzer_type = analyzer_type;
        _analyzer = std::unique_ptr<mochow::invertedindex::Analyzer>(analyzer);
        return Status();
    }

    Status tokenize(const std::string& text) {
        base::Timer timer;
        timer.start();
        auto status =  _analyzer->tokenize("test_column", text, &_column_tokenize_result);
        timer.stop();
        LOG(NOTICE) << "Finish to tokenize text with size:"<< text.size()
                    << " term num:" << _column_tokenize_result.size()
                    << " time:"<< timer.u_elapsed() << "(us)";
        print_term_info();
        return status;
    }

    Status tokenize_file(const std::string& text_file) {
        auto file = boost::filesystem::ifstream(text_file, std::ios::binary | std::ios::in);

        if (!file.is_open()) {
            return Status(ERR_IO_ERROR, "failed to open text file");
        }

        std::string text;	
        while (true) {
            char read_buf[16384];
            file.read(read_buf, sizeof(read_buf));
            const int rc = file.gcount();

            if (rc > 0) {
                text.append(read_buf, rc);
            }

            if (rc == 0 || file.eof()) {
                break;
            }
        }

        file.close();
        LOG(NOTICE) << "read file:" << text_file << " is completed with size:" << text.size();
        return tokenize(text);
    }

    void print_term_info() {
        LOG(NOTICE) << "text tokenize result:";
        for (const auto& [term, term_info] : _column_tokenize_result) {
             auto term_info_str = to_string(term_info);
             LOG(NOTICE) << "term:"  << term << ", info: " << term_info_str; 
        }
    }

    std::string to_string(const mochow::invertedindex::TokenizeTermInfo & term_info) {
        std::stringstream oss;
        oss << "freq: " << term_info.freq << ", ";

        if (term_info.freq  > 0) {
            oss << "position:(";

            for (const auto& pos : term_info.positions) {
                  oss << pos << ", ";
            }

            oss << ")";

            //oss << " offset:(";

            //for (const auto& pair : term_info.offsets) {
            //    oss << "[" << pair.first << ", "  << pair.second << "]," ;
            //}

            //oss << ")";
        }

        return oss.str();
    }

    void run () {
        auto status = initialize(static_cast<mochow::invertedindex::AnalyzerType>(FLAGS_analyzer_type),
                static_cast<mochow::invertedindex::AnalyzerParseMode>(FLAGS_analyzer_parse_mode));

        if (!status.ok()) {
            return;
        }

        for (uint32_t index = 0; index < FLAGS_repeat_num; ++index) {

            if (!FLAGS_tokenize_text.empty()) {    
                tokenize(FLAGS_tokenize_text);
            }

            if (!FLAGS_tokenize_file.empty()) {
                tokenize_file(FLAGS_tokenize_file);
            }
        }
    }
private:
    mochow::invertedindex::AnalyzerType _analyzer_type;

    mochow::invertedindex::AnalyzerPtr _analyzer;

    mochow::invertedindex::ColumnValueTokenizeResult  _column_tokenize_result;
};

}

int main(int argc, char** argv) {
    if (::mochow::common::init_comlog() != 0) {
        LOG(FATAL) << "mochow benchmark startup failed due to init comlog failed";
        _exit(-1);
    }
    
    if (google::SetCommandLineOption("bvar_dump", "true").empty()) {
        LOG(FATAL) << "Fail to enable bvar dump";
    }

    google::ParseCommandLineFlags(&argc, &argv, false);
    mochow::benchmark::invertedindex::AnalyzerBenchmark bench;
    bench.run();
    sleep(1000);
    return 0;
}
