#include <vector>
#include <filesystem>
#include <thread>
#include <string>
#include <iostream>
#include <fstream>
#include <sstream>
#include <algorithm>
#include <base/comlog_sink.h>
#include <base/files/file_path.h>
#include <base/files/file_enumerator.h>
#include <base/logging.h>
#include <base/configure/Configure.h>
#include <base/time.h>
#include <gflags/gflags.h>
#include <baidu/rpc/server.h>
#include "nlohmann/json.hpp"
#include "comlogplugin.h"
#include "rocksdb/db.h"
#include "rapidjson/document.h"
#include "core/src/common/using.h"
#include "core/src/common/logging.h"
#include "core/src/common/config.h"
#include "core/src/inverted_index/document.h"
#include "core/src/inverted_index/analyzer.h"
#include "core/src/inverted_index/index_writer.h"
#include "core/src/inverted_index/impl/file_based_index_writer.h"
#include "core/src/inverted_index/impl/lsm_based_db_options.h"
#include "core/src/inverted_index/impl/lsm_based_index_writer.h"
#include "core/src/inverted_index/impl/lsm_based_index_reader.h"

namespace mochow::invertedindex::benchmark {

DEFINE_uint64(inverted_index_id, 1, "inverted index id and id will be unique within the mochow table");
DEFINE_uint64(index_writer_type, 1, "the inverted index writer type: 0 for file-based and 1 for lsm-based.");
DEFINE_string(index_path, "", "the index path which will be used to save inverted index data");
DEFINE_uint64(analyzer_type, 1, "analyzer type: 1/2/3 for english/chinese/unicode");
DEFINE_uint64(analyzer_parse_mode, 0, "parse mode: 0/1");
DEFINE_bool(analyzer_case_sensitive, true, "analyzer is case sensitive or not");
DEFINE_string(analyzer_conf_dir, "", "analyzer config directory");
DEFINE_bool(create_index, true, "create the inverted index from the new docs");
DEFINE_string(doc_path, "", "the documents path which will be added to inverted index");
DEFINE_uint64(max_index_doc_num, 0, "the max document num the benchmark will try to index");
DEFINE_bool(optimize_index, true, "whether to optimize the index after all documents are indexed");
DEFINE_string(query_text, "", "the query which the benchmark will try to serve");
DEFINE_string(query_text_file, "", "the file path which contains the query text");
DEFINE_bool(explain_query, false, "explain the query for indexing docs");
DEFINE_uint64(repeat_times, 1, "how many times the query will be executed repeatedly");
DEFINE_bool(use_and_operator, false, "use AND operator as the default operator for boolean query");

class Benchmark final {
public:
    Benchmark() :
        _db(nullptr),
        _default_cf_handle(nullptr),
        _cf_handle(nullptr),
        _doc_id(1) {  //let's start document id from 1

    }        

    ~Benchmark() {}

    Status initialize() {
        /*Document Schema will have two columns:
         *   1: doc_path, string, it tells where the document comes from
         *   2: content, string, it contains the content of the document
         */
        std::vector<Column> columns;
        ColumnID start_column_id = 1;
        Column column;
        //column 1
        column.column_name = "doc_path";
        column.column_id = start_column_id++;
        column.column_data_type = ColumnDataType::DATA_TYPE_STRING;
        column.column_index_attribute = IndexAttribute::INDEX_NOT_ANALYZED;
        columns.push_back(column);
        //column 2
        column.column_name = "title";
        column.column_id = start_column_id++;
        column.column_data_type = ColumnDataType::DATA_TYPE_STRING;
        column.column_index_attribute = IndexAttribute::INDEX_NOT_ANALYZED;
        columns.push_back(column);      
        //column 3
        column.column_name = "content";
        column.column_id = start_column_id++;
        column.column_data_type = ColumnDataType::DATA_TYPE_STRING;
        column.column_index_attribute = IndexAttribute::INDEX_ANALYZED;
        columns.push_back(column);
        auto doc_schema = DocumentSchema::create(columns);

        if (doc_schema == nullptr) {
            LOG(WARNING) << "Failed to create document schema from columns";
            return Status(ERR_INVALID_ARGUMENT, "Failed to create document schema");
        }

        _doc_schema.reset(doc_schema);

        _checker = std::make_unique<QueryTextIntegrityChecker>(_doc_schema);

        auto analyzer = mochow::invertedindex::Analyzer::create(static_cast<AnalyzerType>(FLAGS_analyzer_type),
                                                                static_cast<AnalyzerParseMode>(FLAGS_analyzer_parse_mode),
                                                                FLAGS_analyzer_case_sensitive,
                                                                FLAGS_analyzer_conf_dir);

        if (analyzer == nullptr) {
            LOG(WARNING) << "Failed to create analyzer with type:" << static_cast<uint8_t>(FLAGS_analyzer_type)
                         << ", mode:" << static_cast<uint8_t>(FLAGS_analyzer_parse_mode);
            return Status(ERR_INVALID_ARGUMENT, "invalid analyzer type");
        }

        AnalyzerPtr index_writer_analyzer;
        index_writer_analyzer.reset(analyzer);

        //initialize the index writer.
        IndexWriterConfigPtr index_writer_config;
        if (static_cast<IndexWriterType>(FLAGS_index_writer_type) == IndexWriterType::FILE_BASED_INDEX_WRITER) {
            auto config = new FileBasedIndexWriterConfig;
            config->index_writer_type = IndexWriterType::FILE_BASED_INDEX_WRITER;
            config->index_path = FLAGS_index_path;
            config->create = FLAGS_create_index;
            config->max_column_num_per_doc = MAX_COLUMN_NUM_IN_DOCUMENT;
            config->max_term_num_per_column = MAX_TERM_NUM_PER_COLUMN;
            config->max_buffer_document_num = DEFAULT_BUFFER_DOCUMENT_NUM_IN_INDEX_WRITER;
            config->max_raw_buffer_size = DEFAULT_RAW_BUFFER_SIZE_IN_INDEX_WRITER;
            config->use_compound_file = true;
            index_writer_config.reset(static_cast<IndexWriterConfig*>(config));
        } else if (static_cast<IndexWriterType>(FLAGS_index_writer_type) == IndexWriterType::LSM_BASED_INDEX_WRITER) {
            auto status = initialize_lsm_db();
            if (!status.ok()) {
                return status;
            }

            auto config = new LSMBasedIndexWriterConfig;
            config->index_writer_type = IndexWriterType::LSM_BASED_INDEX_WRITER;
            config->index_path = FLAGS_index_path;
            config->create = FLAGS_create_index;
            config->max_column_num_per_doc = MAX_COLUMN_NUM_IN_DOCUMENT;
            config->max_term_num_per_column = MAX_TERM_NUM_PER_COLUMN;
            config->index_id = FLAGS_inverted_index_id;
            config->db = _db;
            config->cf_handle = _cf_handle;
            index_writer_config.reset(static_cast<IndexWriterConfig*>(config));
        } else {
            LOG(WARNING) << "unknown index writer type:" << FLAGS_index_writer_type;
            return Status(ERR_INVALID_ARGUMENT, "unknown index writer type");
        }

        auto index_writer = IndexWriter::create(std::move(index_writer_config),
                                                std::move(index_writer_analyzer),
                                                _doc_schema);

        if (index_writer == nullptr) {
            LOG(WARNING) << "Failed to create index writer";
            return Status(ERR_INVALID_ARGUMENT, "Failed to create index writer");
        }

        _index_writer.reset(index_writer);
        LOG(NOTICE) << "Succeed to initialize the benchmark";
        return Status();
    }

    Status index_docs() {
        const std::string dir = FLAGS_doc_path;
        base::Timer timer;
        timer.start();
        uint64_t index_doc_num = 0;

        base::FileEnumerator f_enum(base::FilePath(dir), false, base::FileEnumerator::FileType::FILES);
        for (base::FilePath path = f_enum.Next(); !path.empty(); path = f_enum.Next())  {
            auto doc_filepath = path.value();
            Document doc(_doc_id++);
            auto status = load_doc_from_disk(doc_filepath, &doc);

            if (!status.ok()) {
                LOG(WARNING) << "Failed to load doc from disk,"
                             << " doc path:" << doc_filepath
                             << " doc_id:" << doc.id()
                             << " status:" << status;
                continue;
            }

            status = _index_writer->add_document(doc);

            if (!status.ok()) {
                LOG(WARNING) << "Failed to add document to index writer,"
                             << " doc path:" << doc_filepath
                             << " doc id:" << doc.id()
                             << " status:" << status;
                continue;
            }

            LOG(TRACE) << "Added document:" << path.value() << " doc_id:" << doc.id() << " to index writer successfully";
            ++index_doc_num;

            if (FLAGS_max_index_doc_num != 0 && index_doc_num >= FLAGS_max_index_doc_num) {
                break;
            }
        }

        //all docs had been added to index writer and it is time to flush/commit it.
        _index_writer->flush();
        _index_writer->commit();

        if (FLAGS_optimize_index) {
            _index_writer->try_compact();
        }

        timer.stop();
        LOG(NOTICE) << "Succeed to add document to index writer,"
                    << " total doc num:" << index_doc_num
                    << " cost time:" << timer.m_elapsed() << "(ms)";
        return Status();
    }

    Status query() {
        rocksdb::CompactRangeOptions compact_options;
        compact_options.exclusive_manual_compaction = true;
        compact_options.bottommost_level_compaction = rocksdb::BottommostLevelCompaction::kForce;
        _db->CompactRange(compact_options, _cf_handle, nullptr, nullptr);  /*compact the whole column family*/

        auto index_reader = _index_writer->create_reader();

        if (index_reader == nullptr) {
            LOG(WARNING) << "Failed to create index reader for search";
            return Status(ERR_FAIL, "Failed to create index reader");
        }

        //check how manys terms are stored in the reader;
        /*
        {
            auto term_iterator = index_reader->terms();
            size_t term_count = 0;
            while (term_iterator->next()) {
               ++term_count;
            }
            LOG(NOTICE) << "term iterator found term num:" << term_count;
        }
        */

        auto analyzer = mochow::invertedindex::Analyzer::create(static_cast<AnalyzerType>(FLAGS_analyzer_type),
                                                                static_cast<AnalyzerParseMode>(FLAGS_analyzer_parse_mode),
                                                                FLAGS_analyzer_case_sensitive,
                                                                FLAGS_analyzer_conf_dir);

        if (analyzer == nullptr) {
            LOG(WARNING) << "Failed to create analyzer with type:" << static_cast<uint8_t>(FLAGS_analyzer_type)
                         << ", mode:" << static_cast<uint8_t>(FLAGS_analyzer_parse_mode);
            return Status(ERR_INVALID_ARGUMENT, "invalid analyzer type");
        }

        AnalyzerPtr query_parser_analyzer;
        query_parser_analyzer.reset(analyzer);

        const Lucene::String search_field = L"content";
        Lucene::SearcherPtr searcher = nullptr;

        if (static_cast<IndexWriterType>(FLAGS_index_writer_type) == IndexWriterType::LSM_BASED_INDEX_WRITER) {
            searcher = Lucene::newLucene<Lucene::IndexSearcher>(index_reader,
                                                                true /*enable BM25*/);
            auto lsm_based_index_reader = dynamic_cast<mochow::invertedindex::LSMBasedIndexReader*>(index_reader.get());
            searcher->setSimilarity(lsm_based_index_reader->get_bm25_similarity());
        } else {
            searcher = Lucene::newLucene<Lucene::IndexSearcher>(index_reader,
                                                                false);
        }

        Lucene::QueryParserPtr parser = Lucene::newLucene<Lucene::QueryParser>(
                Lucene::LuceneVersion::LUCENE_CURRENT,
                search_field,
                query_parser_analyzer->impl());

        if (FLAGS_use_and_operator) {
            parser->setDefaultOperator(Lucene::QueryParser::AND_OPERATOR);
        }

        do {
            std::string query_str;

            std::cout << "Please input your query:" << std::endl;
            std::getline(std::cin, query_str);

            const auto& last_word = query_str.back();

            if (last_word == '\r' || last_word == '\n') {
                query_str.pop_back();
            }

            if (query_str.empty()) {
                break;
            }

            Lucene::QueryPtr query{nullptr};
            try {
                query = parser->parse(Lucene::StringUtils::toUnicode(query_str), _checker.get());
            }  catch (Lucene::LuceneException& e) { // catch all errors
                std::cout << "Illegal query, error:" << e.getType() << " " << Lucene::StringUtils::toUTF8(e.getError()) << std::endl;
                continue;
            }

            std::cout << "searching for query type:" << Lucene::StringUtils::toUTF8(query->getClassName()) << ","
                      << " str:" << Lucene::StringUtils::toUTF8(query->toString(L"")) << std::endl;

            LOG(NOTICE) << "searching for query:(type:" << Lucene::StringUtils::toUTF8(query->getClassName())
                        << ", str:"
                        << Lucene::StringUtils::toUTF8(query->toString(L""))
                        << ")";

            for (uint32_t index = 0; index < FLAGS_repeat_times; ++index) {
                do_query(searcher, query);
            }

        } while(true);

        return Status();
    }

    void do_query(Lucene::SearcherPtr searcher, Lucene::QueryPtr query) {
        base::Timer timer;
        timer.start();
        //Only need Top100 in our case
        Lucene::TopScoreDocCollectorPtr collector = Lucene::TopScoreDocCollector::create(64, false);
        searcher->search(query, collector);
        int32_t numTotalHits = collector->getTotalHits();       
        Lucene::Collection<Lucene::ScoreDocPtr> hits = collector->topDocs()->scoreDocs;

        timer.stop();
        LOG(NOTICE) << "Found " << numTotalHits << " matching docs within " << timer.u_elapsed() << "(us)"
                    << " and return Top " << hits.size() << " docs";

        std::cout << "Found " << numTotalHits << " matching docs within " << timer.u_elapsed() << "(us)"
                  << " and return Top " << hits.size() << " docs" << std::endl;

        for (int32_t index = 0; index < hits.size(); ++index) {
            DocumentID doc_id = hits[index]->doc;

            if (static_cast<IndexWriterType>(FLAGS_index_writer_type) == IndexWriterType::FILE_BASED_INDEX_WRITER) {
                Lucene::DocumentPtr doc = searcher->doc(hits[index]->doc);
                Lucene::String doc_id_str = doc->get(Lucene::StringUtils::toUnicode(INSIDE_RESERVED_COLUMN_NAME));
                doc_id = std::strtoll(Lucene::StringUtils::toUTF8(doc_id_str).c_str(), nullptr, 10);
            }

            if (FLAGS_explain_query) {
                //try to explain the the query and debug why score is different.
                auto explain_ptr = searcher->explain(query, hits[index]->doc);

                if (explain_ptr != nullptr) {
                    LOG(TRACE) << "query explain on doc_id:" << doc_id
                               << " is:" << Lucene::StringUtils::toUTF8(explain_ptr->toString());
                }
            }

            LOG(NOTICE) << "Returned doc:" << doc_id << ", score:" << hits[index]->score;
            std::cout << "Returned doc:" << doc_id << ", score:" << hits[index]->score << std::endl;
        }
    }

    void run() {
        auto status = initialize();

        if (!status.ok()) {
            return;
        }

        if (FLAGS_create_index) {
            index_docs();
        } else {
            query();
        }

        close();
    }

    DISABLE_COPY_AND_MOVE(Benchmark);

private:
    Status initialize_lsm_db() {
        rocksdb::DBOptions db_option;
        db_option.create_if_missing = true;
        db_option.create_missing_column_families = true;
        db_option.error_if_exists = false;

        std::vector<rocksdb::ColumnFamilyDescriptor> db_cf_descriptors;
        //cf0: default column family    
        db_cf_descriptors.push_back(rocksdb::ColumnFamilyDescriptor(rocksdb::kDefaultColumnFamilyName,
                                                                    rocksdb::ColumnFamilyOptions()));
        //cf1: column family will be for inverted index.
        rocksdb::ColumnFamilyDescriptor cf_descriptor;  
        cf_descriptor.name = "inverted_index_cf";
        initialize_column_family_option(&cf_descriptor.options);
        db_cf_descriptors.push_back(std::move(cf_descriptor));

        std::vector<rocksdb::ColumnFamilyHandle*> cf_handles;
        const auto status = rocksdb::DB::Open(db_option, FLAGS_index_path, db_cf_descriptors, &cf_handles, &_db);

        if (!status.ok()) {
            LOG(WARNING) << "Failed to create rocksdb instance";
            return status;
        }

        assert(cf_handles.size() == 2);
        _default_cf_handle = cf_handles[0];
        _cf_handle = cf_handles[1];
        LOG(NOTICE) << "Succeed to initialize LSM DB on path:" << FLAGS_index_path;
        return Status();
     }

    void close() {
        if (_index_writer != nullptr) {
            _index_writer->close();
            _index_writer.reset();
        }

        if (_db != nullptr) {
            _db->DestroyColumnFamilyHandle(_default_cf_handle);
            _db->DestroyColumnFamilyHandle(_cf_handle);
            _db->Close();
            delete _db;
        }
    }

    Status load_doc_from_disk(const std::string& doc_path, Document* doc) {
        std::string  doc_content;
        auto status = load_doc_content(doc_path, &doc_content);

        if (!status.ok()) {
            return status;
        }

        //Parse Json from the file content
        rapidjson::Document json_doc;
        const bool json_decode_error = json_doc.Parse<0>(doc_content.c_str()).HasParseError();

        if (json_decode_error) {
            LOG(WARNING) << "Failed to parse json from file content,"
                         << " file_path:" << doc_path;
            return Status(ERR_INVALID_ARGUMENT, "Failed to parse json");
        }

        if (!json_doc.IsObject()
                || !json_doc.HasMember("title")
                || !json_doc.HasMember("text")) {

            LOG(WARNING) << "missing title/text field in json doc,"
                         << " file_path:" << doc_path;
            return Status(ERR_INVALID_ARGUMENT, "Failed to parse json");            
        }

        std::vector<ColumnData>  columns;
        ColumnData column_data;
        column_data.column_id = 1;
        column_data.column_value = doc_path;
        columns.push_back(std::move(column_data));

        column_data.column_id = 2;
        column_data.column_value = json_doc["title"].GetString();
        columns.push_back(std::move(column_data));

        column_data.column_id = 3;
        column_data.column_value = json_doc["text"].GetString();
        columns.push_back(std::move(column_data));

        doc->set_data(std::move(columns));
        return Status();
    }

    Status load_doc_content(const std::string& doc_path, std::string* result) {
        auto file = boost::filesystem::ifstream(doc_path, std::ios::binary | std::ios::in);
        if (!file.is_open()) {
            return Status(ERR_IO_ERROR, "failed to open text file");
        }

        std::string content;    
        while (true) {
            char read_buf[16384];
            file.read(read_buf, sizeof(read_buf));
            const int rc = file.gcount();

            if (rc > 0) {
                content.append(read_buf, rc);
            }

            if (rc == 0 || file.eof()) {
                break;
            }
        }

        file.close();
        *result = std::move(content);
        return Status();
    }

    Status load_query_file(const std::string& text_file, std::string* query_text) {
        auto file = boost::filesystem::ifstream(text_file, std::ios::binary | std::ios::in);

        if (!file.is_open()) {
            return Status(ERR_IO_ERROR, "failed to open text file");
        }

        std::string text;       
        while (true) {
            char read_buf[16384];
            file.read(read_buf, sizeof(read_buf));
            const int rc = file.gcount();

            if (rc > 0) {
                text.append(read_buf, rc);
            }

            if (rc == 0 || file.eof()) {
                break;
            }
        }

        file.close();
        LOG(NOTICE) << "read file:" << text_file << " is completed with size:" << text.size();
        *query_text = text;
        return Status();
    }

private:
    rocksdb::DB* _db;
    rocksdb::ColumnFamilyHandle* _default_cf_handle;
    rocksdb::ColumnFamilyHandle* _cf_handle;
    IndexWriterPtr _index_writer;
    DocumentSchemaRef _doc_schema;  
    DocumentID _doc_id;
    std::unique_ptr<QueryTextIntegrityChecker> _checker;
};

}

int main(int argc, char** argv) {
    if (::mochow::common::init_comlog() != 0) {
        LOG(FATAL) << "mochow benchmark startup failed due to init comlog failed";
        _exit(-1);
    }
    
    if (google::SetCommandLineOption("bvar_dump", "true").empty()) {
        LOG(FATAL) << "Fail to enable bvar dump";
    }

    google::ParseCommandLineFlags(&argc, &argv, false);

    baidu::rpc::StartDummyServerAt(8888/*port*/);
    ::mochow::invertedindex::benchmark::Benchmark benchmark;
    benchmark.run();
    return 0;
}
