#include <vector>
#include <base/comlog_sink.h>
#include <base/configure/Configure.h>
#include <base/files/file_path.h>
#include <gflags/gflags.h>
#include "benchmark/common/benchmark_hdf5.h"
#include "comlogplugin.h"
#include "nlohmann/json.hpp"
#include "core/src/common/using.h"
#include "core/src/common/vindex_build_type.h"
#include "core/src/schema/schema_builder.h"
#include "core/src/schema/schema_cache.h"
#include "core/src/schema/id_allocator.h"
#include "core/src/zhuque/tablet.h"
#include "core/src/zhuque/zhuque.h"
#include "core/src/zhuque/filter.h"
#include "core/src/zhuque/projection.h"
#include "core/test/tools/row_generator.h"
#include "core/test/tools/test_helper.h"
#include "core/test/tools/filter_generator.h"

namespace mochow::benchmark::zhuque {
static const std::string TEST_ANN_RECALL = "ANN_RECALL";
static const std::string TEST_KNN_RECALL = "KNN_RECALL";
static const std::string TEST_ANN_KNN_RECALL = "ANN_KNN_RECALL";
static const std::string TEST_RANGE_RECALL = "RANGE_RECALL";
static const std::string TEST_RECALL_WITHOUT_VINDEX = "RECALL_WITHOUT_VINDEX";
static const std::string TEST_FILTER_RECALL = "FILTER_RECALL";

static const std::vector<DatasetHDF5> hdf5_datasets = {
    DatasetHDF5(
            "Fashion-MNIST",
            784,
            60000,
            10000,
            100,
            mochow::vindex::MetricType::L2,
            "fashion_mnist-784-euclidean"),
};

std::string to_upper(std::string str) {
    std::transform(str.begin(), str.end(), str.begin(), ::toupper);
    return str;
}

class LongTest : public benchmark::BenchmarkHDF5 {
public:
    LongTest() = default;
    ~LongTest() = default;
   
    schema::SchemaHierarchyRef generate_schema(const nlohmann::json& conf) {
        schema::IdAllocatorRef column_id_allocator = std::make_shared<schema::IdAllocator>();
        schema::IdAllocatorRef index_id_allocator = std::make_shared<schema::IdAllocator>();
        DBID db_id = 1;
        std::string db_name = "db_test";
        TBLID table_id = 1;
        std::string table_name = "perf";
        
        schema::SchemaBuilder* builder = new schema::SchemaBuilder(
                schema::SchemaLayer::EXTERNAL, column_id_allocator, index_id_allocator);
        uint64_t schema_version = 1;
        /******************************
         *
         * |-Row key-------------|-------External Column --------|
         * |-----------------------------------------------------|
         * | CF(id=0)            | CF(id=3)                      |
         * | id(id=2,type=UINT64)|vector(id=4, type=FLOAT_VECTOR)|
         * |-----------------------------------------------------|
         *
         *****************************/
        
        builder->initialize_schema(schema_version, db_id, db_name, table_id, table_name);
        auto cf_id = builder->next_cf_id();
        LOG_AND_ASSERT(cf_id == schema::DEFAULT_SCALAR_CF_ID);
        auto ok = builder->add_scalar_column_family(cf_id, schema::DEFAULT_SCALAR_CF_NAME);
        LOG_AND_ASSERT(ok);
        ok = builder->add_primary_key(builder->next_column_id(), "id",
                DataType::UINT64, SortType::ASCEND, 
                true, /* partition key*/
                false /* auto increment*/);
        LOG_AND_ASSERT(ok);
        cf_id = builder->next_cf_id();
        ok = builder->add_external_column(cf_id, "vector", builder->next_column_id(),
                "vector", DataType::FLOAT_VECTOR, false, false, _dim);
        LOG_AND_ASSERT(ok);
        vindex::VectorIndexParam index_param;
        index_param.index_type = vindex::IndexType::INDEX_HNSW;
        index_param.metric_type = vindex::MetricType::L2;
        mochow::vindex::HNSWParam hnsw_param;
        if (conf.contains("M")) {
             hnsw_param.M = conf["M"];
        } else {
            hnsw_param.M = 32;
        }
        
        if (conf.contains("efConstruction")) {
            hnsw_param.efConstruction = conf["efConstruction"];
        } else {
            hnsw_param.efConstruction = 200;
        }
        index_param.hnsw_param = hnsw_param;
        ok = builder->add_vector_index(builder->next_index_id(), "vector_index",
                "vector", index_param);
        LOG_AND_ASSERT(ok);
        auto external_schema = builder->move_and_get_schema();
        delete builder;
        LOG(NOTICE) << "external schema:" << external_schema->to_string();
        auto schema_hierarchy = ::mochow::datanode::g_schema_cache->generate_schema_hierarchy_from_external_schema(
                external_schema, column_id_allocator, index_id_allocator);
        LOG(NOTICE) << "internal schema:" << schema_hierarchy->internal_schema->to_string();
        LOG(NOTICE) << "scalar schema:" << schema_hierarchy->scalar_schema->to_string();
        LOG(NOTICE) << "vector schema:" << schema_hierarchy->vector_schema->to_string();
        ok = ::mochow::datanode::g_schema_cache->add_schema_hierarchy(schema_hierarchy);
        LOG_AND_ASSERT(ok);
        return schema_hierarchy;
    }

    schema::SchemaHierarchyRef generate_schema_without_vindex() {
        schema::IdAllocatorRef column_id_allocator = std::make_shared<schema::IdAllocator>();
        schema::IdAllocatorRef index_id_allocator = std::make_shared<schema::IdAllocator>();
        DBID db_id = 1;
        std::string db_name = "db_test";
        TBLID table_id = 1;
        std::string table_name = "perf";
        
        schema::SchemaBuilder* builder = new schema::SchemaBuilder(
                schema::SchemaLayer::EXTERNAL, column_id_allocator, index_id_allocator);
        uint64_t schema_version = 1;
        /******************************
         *
         * |-Row key-------------|-------External Column --------|
         * |-----------------------------------------------------|
         * | CF(id=0)            | CF(id=3)                      |
         * | id(id=2,type=UINT64)|vector(id=4, type=FLOAT_VECTOR)|
         * |-----------------------------------------------------|
         *
         *****************************/
        
        builder->initialize_schema(schema_version, db_id, db_name, table_id, table_name);
        auto cf_id = builder->next_cf_id();
        LOG_AND_ASSERT(cf_id == schema::DEFAULT_SCALAR_CF_ID);
        auto ok = builder->add_scalar_column_family(cf_id, schema::DEFAULT_SCALAR_CF_NAME);
        LOG_AND_ASSERT(ok);
        ok = builder->add_primary_key(builder->next_column_id(), "id",
                DataType::UINT64, SortType::ASCEND, 
                true, /* partition key*/
                false /* auto increment*/);
        LOG_AND_ASSERT(ok);
        cf_id = builder->next_cf_id();
        ok = builder->add_external_column(cf_id, "vector", builder->next_column_id(),
                "vector", DataType::FLOAT_VECTOR, false, false, _dim);
        LOG_AND_ASSERT(ok);
        auto external_schema = builder->move_and_get_schema();
        delete builder;
        LOG(NOTICE) << "external schema:" << external_schema->to_string();
        auto schema_hierarchy = ::mochow::datanode::g_schema_cache->generate_schema_hierarchy_from_external_schema(
                external_schema, column_id_allocator, index_id_allocator);
        LOG(NOTICE) << "internal schema:" << schema_hierarchy->internal_schema->to_string();
        LOG(NOTICE) << "scalar schema:" << schema_hierarchy->scalar_schema->to_string();
        LOG(NOTICE) << "vector schema:" << schema_hierarchy->vector_schema->to_string();
        ok = ::mochow::datanode::g_schema_cache->add_schema_hierarchy(schema_hierarchy);
        LOG_AND_ASSERT(ok);
        return schema_hierarchy;
    }
    
    schema::SchemaHierarchyRef generate_schema_for_filter(const nlohmann::json& conf) {
        schema::IdAllocatorRef column_id_allocator = std::make_shared<schema::IdAllocator>();
        schema::IdAllocatorRef index_id_allocator = std::make_shared<schema::IdAllocator>();
        DBID db_id = 1;
        std::string db_name = "db_test";
        TBLID table_id = 1;
        std::string table_name = "filter_tbl";
        
        schema::SchemaBuilder* builder = new schema::SchemaBuilder(
                schema::SchemaLayer::EXTERNAL, column_id_allocator, index_id_allocator);
        uint64_t schema_version = 1;
        /******************************
         *
         * |-Row key-------------|-------External Column------------------------------------------|
         * |--------------------------------------------------------------------------------------|
         * | CF(id=0)            | CF(id=2)                       |CF(id=4)                       |
         * | id(id=1,type=UINT64)|filter_column(id=3, type=UINT64)|vector(id=5, type=FLOAT_VECTOR)|
         * |--------------------------------------------------------------------------------------|
         *
         *****************************/
        
        builder->initialize_schema(schema_version, db_id, db_name, table_id, table_name);
        //auto cf_id = builder->next_cf_id();
        //LOG_AND_ASSERT(cf_id == schema::DEFAULT_SCALAR_CF_ID);
        //auto ok = builder->add_scalar_column_family(cf_id, schema::DEFAULT_SCALAR_CF_NAME);
        //LOG_AND_ASSERT(ok);
        auto ok = builder->add_primary_key(builder->next_column_id(), "id",
                DataType::UINT64, SortType::ASCEND, 
                true, /* partition key*/
                false /* auto increment*/);
        LOG_AND_ASSERT(ok);
        auto cf_id = builder->next_cf_id();
        auto scalar_filter_cid = builder->next_column_id();
        ok = builder->add_external_column(cf_id, "scalar", scalar_filter_cid,
                "filter_column", DataType::UINT64, false, false);
        cf_id = builder->next_cf_id();
        ok = builder->add_external_column(cf_id, "vector", builder->next_column_id(),
                "vector", DataType::FLOAT_VECTOR, false, false, _dim);
        LOG_AND_ASSERT(ok);
        
        // add secondary index
        std::vector<COLID> scalar_index_key_ids;
        scalar_index_key_ids.push_back(scalar_filter_cid);
        std::vector<schema::ScalarIndexKeyAttribute> index_key_attributes;
        auto& index_key_attribute = index_key_attributes.emplace_back();
        index_key_attribute.sort_type = get_sort_type_string(SortType::ASCEND);
        index_key_attribute.index_structure_type = schema::ScalarIndexStructureType::DEFAULT;
        schema::ScalarIndexParam scalar_index_param;
        scalar_index_param.index_type = schema::ScalarIndexType::SECONDARY;
        ok = builder->add_scalar_index(builder->next_index_id(), "filter_column_idx",
                                       scalar_index_param, false, false,
                                       scalar_index_key_ids, index_key_attributes);
        LOG_AND_ASSERT(ok);
        // add vector index
        vindex::VectorIndexParam index_param;
        index_param.index_type = vindex::IndexType::INDEX_HNSW;
        index_param.metric_type = vindex::MetricType::L2;
        mochow::vindex::HNSWParam hnsw_param;
        if (conf.contains("M")) {
             hnsw_param.M = conf["M"];
        } else {
            hnsw_param.M = 32;
        }
        
        if (conf.contains("efConstruction")) {
            hnsw_param.efConstruction = conf["efConstruction"];
        } else {
            hnsw_param.efConstruction = 200;
        }
        index_param.hnsw_param = hnsw_param;
        ok = builder->add_vector_index(builder->next_index_id(), "vector_index",
                "vector", index_param);
        LOG_AND_ASSERT(ok);
        auto external_schema = builder->move_and_get_schema();
        delete builder;
        LOG(NOTICE) << "external schema:" << external_schema->to_string();
        auto schema_hierarchy = ::mochow::datanode::g_schema_cache->generate_schema_hierarchy_from_external_schema(
                external_schema, column_id_allocator, index_id_allocator);
        LOG(NOTICE) << "internal schema:" << schema_hierarchy->internal_schema->to_string();
        LOG(NOTICE) << "scalar schema:" << schema_hierarchy->scalar_schema->to_string();
        LOG(NOTICE) << "vector schema:" << schema_hierarchy->vector_schema->to_string();
        ok = ::mochow::datanode::g_schema_cache->add_schema_hierarchy(schema_hierarchy);
        LOG_AND_ASSERT(ok);
        return schema_hierarchy;
    }
    
    zq::RowRef generate_row(schema::SchemaHierarchyRef schema_hierarchy, uint64_t row_count) {
        common::RowGenerator row_generator(::mochow::datanode::g_schema_cache);
        auto internal_schema = schema_hierarchy->internal_schema;
        LOG_AND_ASSERT(internal_schema != nullptr);
        std::vector<zq::Field> fields;
        for (auto & [_, pk] : internal_schema->get_primary_keys()) {
            auto data_type = pk->get_data_type();
            auto row_key = row_count;
            Variant variant;
            variant.set_value(row_key);
            zq::Field field(pk->get_id(), variant);
            fields.push_back(field);
        }
        for (auto & [_, sk] : internal_schema->get_system_keys()) {
            if (sk->get_name() == schema::OPTYPE_COLUMN_NAME) {
                Variant variant;
                variant.set_value((uint8_t)zq::OpType::UPSERT);
                zq::Field field(sk->get_id(), variant);
                fields.push_back(field);
            } else {
                auto data_type = sk->get_data_type();
                zq::Field field(sk->get_id(), row_generator.generate_data(data_type));
                fields.push_back(field);
            }
        }
        zq::AllKeyRef allkey = std::make_shared<zq::AllKey>(fields);
        fields.clear();
        std::map<CFID, std::vector<zq::Field>> column_families_map;
        std::map<CFID, std::vector<zq::Field>> scalar_column_families_map;
        // generate float vecotr
        for (auto & column_schema : internal_schema->get_all_columns()) {
            if (column_schema->is_primary_key() || column_schema->is_system_key()) {
                continue;
            }
            auto colid = column_schema->get_id();

            auto data_type = column_schema->get_data_type();
            if (data_type == DataType::FLOAT_VECTOR) {
                LOG_AND_ASSERT(data_type == DataType::FLOAT_VECTOR);
                float* data_pointer = (float*)_xb + _dim * row_count;
                PointerAndLength data = {data_pointer, (uint64_t)_dim << 2};
                Variant variant;
                variant.set_value(data, DataType::FLOAT_VECTOR);
                zq::Field field(colid, variant);
                if (column_families_map.count(column_schema->get_cf_id())) {
                    column_families_map[column_schema->get_cf_id()].push_back(field);
                } else {
                    column_families_map.emplace(column_schema->get_cf_id(), std::vector<zq::Field>{field});
                }
            } else if (data_type == DataType::UINT64) {
                // secondary index
                Variant variant;
                uint64_t secondary_index = row_count + 1;
                variant.set_value(secondary_index);
                zq::Field field(colid, variant);
                if (scalar_column_families_map.count(column_schema->get_cf_id())) {
                    scalar_column_families_map[column_schema->get_cf_id()].push_back(field);
                } else {
                    scalar_column_families_map.emplace(column_schema->get_cf_id(), std::vector<zq::Field>{field});
                }
            } else {
                LOG_AND_ASSERT(false);
            }
        }

        std::vector<zq::ColumnFamilyRef> column_families;
        for (auto & [cf_id, fields] : column_families_map) {
            zq::ColumnFamilyRef cf = std::make_shared<zq::ColumnFamily>(cf_id, true, fields);
            column_families.push_back(cf);
        }
        for (auto & [cf_id, fields] : scalar_column_families_map) {
            zq::ColumnFamilyRef cf = std::make_shared<zq::ColumnFamily>(cf_id, false, fields);
            column_families.push_back(cf);
        }

        zq::RowRef row = std::make_shared<zq::Row>(allkey, column_families);
        return row;
    }

    void generate_dataset(schema::SchemaHierarchyRef schema_hierarchy, 
            std::vector<zq::RowRef>& ds) {
        LOG(NOTICE) << "generate dataset nb:" << _nb << " dim:" << _dim;
        for (int32_t i = 0; i < _nb; ++i) {
            auto row = generate_row(schema_hierarchy, i);
            if (i == 0) {
                LOG(NOTICE) << "row:" << row->to_string();
            }
            ds.push_back(row);
        }
    }
    
    void test_recall(zq::TabletRef tablet, 
            schema::SchemaHierarchyRef schema_hierarchy, 
            const nlohmann::json& conf) {
        
        auto k = conf[vindex::IndexMeta::TOPK].get<int64_t>();
        
        moss::VectorSearchParam search_params;
        search_params.topk = k;
        if (_index_type == vindex::IndexType::INDEX_HNSW) {
            moss::HNSWSearchParam hnsw_search_params;
            if (conf.contains("ef")) {
                hnsw_search_params.ef = conf["ef"];
            } else {
                hnsw_search_params.ef = 200;
            }
            search_params.hnsw_params = hnsw_search_params;
        } else {
            LOG(ERROR) << "unsupported index type:" << _index_type;
            return;
        }
        
        int32_t search_cnt = 100;
        search_cnt = std::min(search_cnt, _nq);
        LOG(NOTICE) << "[" << get_time_diff() << " s]" << _ann_test_name
            << " | " << _index_type << " | search_cnt=" << std::to_string(search_cnt)
            << ", k=" << std::to_string(k);
        LOG(NOTICE) << "--------------------------------------------------------------------------------";
        
        float recall = 0;
        zq::Filter filter = zq::pass_through_filter();
        zq::Projection projection = zq::full_projection();
        for(int i = 0; i < search_cnt; ++i) {
            float loop_recall = 0;
            VariantArray search_vectors;
            float* data_pointer = (float*)_xq + _dim * i;
            PointerAndLength data = {data_pointer, (uint64_t)_dim << 2};
            Variant variant;
            variant.set_value(data, DataType::FLOAT_VECTOR);
            search_vectors.push_back(variant);
            std::vector<zq::SearchResponseVector>  results;
            IDXVERSION index_version = 1;
            CALC_TIME_SPAN(auto st = tablet->Search(
                               schema_hierarchy, filter, projection,
                               "vector", search_vectors,
                               std::make_shared<moss::VectorSearchParam>(search_params),
                               false, 100000, 0, &results));
            LOG_AND_ASSERT(st.ok());
            std::vector<uint64_t> keys;
            for (auto& result : results) {
                for (int j = 0; j < k ; ++j) {
                    auto row = result[j].get_row();
                    auto row_key = row->get_allkey();
                    uint64_t key; 
                    auto ok = row_key->get_fields()[0].get_variant().get_value(key);
                    LOG_AND_ASSERT(ok);
                    keys.push_back(key);
                }
            }
            loop_recall = calc_recall(keys, i, k);
            recall += loop_recall;
            if (loop_recall < 1) {
                std::stringstream ss;
                ss << "[";
                for (auto key : keys) {
                    ss << key << ",";
                }
                ss << "]";
                LOG(NOTICE) << "recall:" << loop_recall
                    << " result:" << ss.str();
            }
        }

        recall = recall / search_cnt;

        LOG(NOTICE) << "search cnt=" << std::to_string(search_cnt)
            << ", k=" << std::to_string(k)  << ", recall=" << recall;
        LOG(NOTICE) << "--------------------------------------------------------------------------------";
    }

    void calc_range_recall(zq::TabletRef tablet, 
            schema::SchemaHierarchyRef schema_hierarchy, 
            const nlohmann::json& conf) {
        
        auto k = conf[vindex::IndexMeta::TOPK].get<int64_t>();
        float distance_far = 100000000.0;
        float distance_near = 0.0;
        
        moss::VectorSearchParam search_params;
        search_params.topk = k;
        search_params.distance_far = distance_far;
        search_params.distance_near = distance_near;
        if (_index_type == vindex::IndexType::INDEX_HNSW) {
            moss::HNSWSearchParam hnsw_search_params;
            if (conf.contains("ef")) {
                hnsw_search_params.ef = conf["ef"];
            } else {
                hnsw_search_params.ef = 200;
            }
            search_params.hnsw_params = hnsw_search_params;
        } else {
            LOG(ERROR) << "unsupported index type:" << _index_type;
            return;
        }
        
        int32_t search_cnt = 100;
        search_cnt = std::min(search_cnt, _nq);
        LOG(NOTICE) << "[" << get_time_diff() << " s]" << _ann_test_name
            << " | " << _index_type << " | search_cnt=" << std::to_string(search_cnt)
            << ", k=" << std::to_string(k);
        LOG(NOTICE) << "--------------------------------------------------------------------------------";
        
        float recall = 0;
        zq::Filter filter = zq::pass_through_filter();
        zq::Projection projection = zq::full_projection();
        for(int i = 0; i < search_cnt; ++i) {
            float loop_recall = 0;
            VariantArray search_vectors;
            float* data_pointer = (float*)_xq + _dim * i;
            PointerAndLength data = {data_pointer, (uint64_t)_dim << 2};
            Variant variant;
            variant.set_value(data, DataType::FLOAT_VECTOR);
            search_vectors.push_back(variant);
            std::vector<zq::SearchResponseVector> results;
            IDXVERSION index_version = 1;
            CALC_TIME_SPAN(auto st = tablet->Search(
                               schema_hierarchy, filter, projection,
                               "vector", search_vectors,
                               std::make_shared<moss::VectorSearchParam>(search_params),			       
                               false, 10000, 0, &results));
            LOG_AND_ASSERT(st.ok());
            std::vector<uint64_t> keys;
            for (auto& result : results) {
                for (int j = 0; j < k ; ++j) {
                    auto row = result[j].get_row();
                    auto row_key = row->get_allkey();
                    uint64_t key; 
                    auto ok = row_key->get_fields()[0].get_variant().get_value(key);
                    LOG_AND_ASSERT(ok);
                    keys.push_back(key);
                }
            }
            loop_recall = calc_recall(keys, i, k);
            recall += loop_recall;
            if (loop_recall < 1) {
                std::stringstream ss;
                ss << "[";
                for (auto key : keys) {
                    ss << key << ",";
                }
                ss << "]";
                LOG(NOTICE) << "recall:" << loop_recall
                    << " result:" << ss.str();
            }
        }

        recall = recall / search_cnt;

        LOG(NOTICE) << "search cnt=" << std::to_string(search_cnt)
            << ", k=" << std::to_string(k)  << ", recall=" << recall;
        LOG(NOTICE) << "--------------------------------------------------------------------------------";
    }
    
    void calc_filter_recall(zq::TabletRef tablet, 
            schema::SchemaHierarchyRef schema_hierarchy, 
            const nlohmann::json& conf) {
        
        auto k = conf[vindex::IndexMeta::TOPK].get<int64_t>();
        
        moss::VectorSearchParam search_params;
        search_params.topk = k;
        if (_index_type == vindex::IndexType::INDEX_HNSW) {
            moss::HNSWSearchParam hnsw_search_params;
            if (conf.contains("ef")) {
                hnsw_search_params.ef = conf["ef"];
            } else {
                hnsw_search_params.ef = 200;
            }
            search_params.hnsw_params = hnsw_search_params;
        } else {
            LOG(ERROR) << "unsupported index type:" << _index_type;
            return;
        }
        
        int32_t search_cnt = 100;
        search_cnt = std::min(search_cnt, _nq);
        LOG(NOTICE) << "[" << get_time_diff() << " s]" << _ann_test_name
            << " | " << _index_type << " | search_cnt=" << std::to_string(search_cnt)
            << ", k=" << std::to_string(k);
        LOG(NOTICE) << "--------------------------------------------------------------------------------";
        
        float recall = 0;
        auto external_schema = schema_hierarchy->external_schema;
        auto db_name = external_schema->get_db_name();
        auto table_name = external_schema->get_table_name();
        auto scalar_indexes = external_schema->get_scalar_indexes();
        LOG_AND_ASSERT(scalar_indexes.size() == 1);
        auto scalar_index_key_ids = scalar_indexes[0]->get_index_key_ids();
        LOG_AND_ASSERT(scalar_index_key_ids.size() == 1);
        auto scalar_column_cid = scalar_index_key_ids[0];

        auto filter_generator = std::make_shared<mochow::zq::FilterGenerator>(
                ::mochow::datanode::g_schema_cache, db_name);
        std::string sql = "SELECT * FROM " + table_name + " WHERE filter_column <= 30000";
        LOG(NOTICE) << "filter sql:" << sql
            << " on " << db_name << ":" << table_name;
        auto filter = filter_generator->generate_filter(sql);
        zq::Projection projection = zq::full_projection();
        for(int i = 0; i < search_cnt; ++i) {
            float loop_recall = 0;
            VariantArray search_vectors;
            float* data_pointer = (float*)_xq + _dim * i;
            PointerAndLength data = {data_pointer, (uint64_t)_dim << 2};
            Variant variant;
            variant.set_value(data, DataType::FLOAT_VECTOR);
            search_vectors.push_back(variant);
            std::vector<zq::SearchResponseVector> results;
            IDXVERSION index_version = 1;
            CALC_TIME_SPAN(auto st = tablet->Search(
                               schema_hierarchy, *filter, projection,
                               "vector", search_vectors,
                               std::make_shared<moss::VectorSearchParam>(search_params),
                               false, 10000, 0, &results));
            LOG_AND_ASSERT(st.ok());
            std::vector<uint64_t> keys;
            for (auto& result : results) {
                for (int j = 0; j < k ; ++j) {
                    auto row = result[j].get_row();
                    auto row_key = row->get_allkey();
                    uint64_t key; 
                    auto ok = row_key->get_fields()[0].get_variant().get_value(key);
                    LOG_AND_ASSERT(ok);
                    keys.push_back(key);
                    
                    uint64_t filter_column_value;
                    const auto * scalar_field = row->get_field(scalar_column_cid);
                    LOG_AND_ASSERT(scalar_field != nullptr);
                    ok = scalar_field->get_variant().get_value(filter_column_value);
                    LOG_AND_ASSERT(filter_column_value <= 30000);
                }
            }
            auto recall_filter = [](uint32_t id) -> bool {
                if(id < 30000) {
                    return true;
                } 
                return false;
            };
            loop_recall = calc_recall(keys, i, k, std::move(recall_filter));
            recall += loop_recall;
            if (loop_recall < 1) {
                std::stringstream ss;
                ss << "[";
                for (auto key : keys) {
                    ss << key << ",";
                }
                ss << "]";
                LOG(NOTICE) << "recall:" << loop_recall
                    << " result:" << ss.str();
            }
        }

        recall = recall / search_cnt;

        LOG(NOTICE) << "search cnt=" << std::to_string(search_cnt)
            << ", k=" << std::to_string(k)  << ", recall=" << recall;
        LOG(NOTICE) << "--------------------------------------------------------------------------------";
    }

    void test_ann_recall(const nlohmann::json& conf) {
        auto schema_hierarchy = generate_schema(conf);
        std::vector<zq::RowRef> dataset;
        generate_dataset(schema_hierarchy, dataset);
        // init tablet
        zq::TabletOptions tablet_options;
        tablet_options.db_id = 1;
        TBLID table_id = 1;
        TPID tp_id = 0;
        auto tablet_id = make_tablet_id(table_id, tp_id);
        tablet_options.tablet_id = tablet_id;
        tablet_options.tablet_dir = "./data/";
        zq::TabletRef tablet = std::make_shared<zq::Tablet>(tablet_options);
        common::AutoRemoveFile auto_remove_file("./data");
        auto st = tablet->initialize();
        LOG_AND_ASSERT(st.ok());
        // insert data
        uint64_t row_cnt = 0;
        uint64_t applied_log_index = 0;
        for (auto& row : dataset) {
            st = tablet->Insert(schema_hierarchy, row, applied_log_index);
            LOG_AND_ASSERT(st.ok());
            if (row_cnt % 10000 == 0) {
                LOG(NOTICE) << "insert 1w row";
            }
            row_cnt++;
            applied_log_index++;
        }
        // build vector index
        auto vector_indexes = schema_hierarchy->internal_schema->get_vector_indexes();
        LOG_AND_ASSERT(vector_indexes.size() == 1);
        auto index_id = vector_indexes[0]->get_index_id();
        IDXMAJOR index_major_version = 1;
        st = tablet->build_vindex(schema_hierarchy, index_id, 
                index_major_version, common::VIndexBuildType::STABLE, false);
        LOG_AND_ASSERT(st.ok());
        // search
        test_recall(tablet, schema_hierarchy, conf);
        
        // clear
        tablet->destroy();
        while(true) {
            if (tablet->could_be_gc()) {
                break;
            }
            std::this_thread::sleep_for(std::chrono::seconds(1));
        }
        ::mochow::datanode::g_schema_cache->clear();
    }
    
    void test_knn_recall(const nlohmann::json& conf) {
        auto schema_hierarchy = generate_schema(conf);
        std::vector<zq::RowRef> dataset;
        generate_dataset(schema_hierarchy, dataset);
        // init tablet
        zq::TabletOptions tablet_options;
        tablet_options.db_id = 1;
        TBLID table_id = 1;
        TPID tp_id = 0;
        auto tablet_id = make_tablet_id(table_id, tp_id);
        tablet_options.tablet_id = tablet_id;
        tablet_options.tablet_dir = "./data/";
        zq::TabletRef tablet = std::make_shared<zq::Tablet>(tablet_options);
        common::AutoRemoveFile auto_remove_file("./data");
        auto st = tablet->initialize();
        LOG_AND_ASSERT(st.ok());
        // insert data
        uint64_t row_cnt = 0;
        uint64_t applied_log_index = 0;
        for (auto& row : dataset) {
            st = tablet->Insert(schema_hierarchy, row, applied_log_index);
            LOG_AND_ASSERT(st.ok());
            if (row_cnt % 10000 == 0) {
                LOG(NOTICE) << "insert 1w row";
            }
            row_cnt++;
            applied_log_index++;
        }
        // search
        test_recall(tablet, schema_hierarchy, conf);
        
        // clear
        tablet->destroy();
        while(true) {
            if (tablet->could_be_gc()) {
                break;
            }
            std::this_thread::sleep_for(std::chrono::seconds(1));
        }
        ::mochow::datanode::g_schema_cache->clear();
    }
    
    void test_ann_knn_recall(const nlohmann::json& conf) {
        auto schema_hierarchy = generate_schema(conf);
        std::vector<zq::RowRef> dataset;
        generate_dataset(schema_hierarchy, dataset);
        // init tablet
        zq::TabletOptions tablet_options;
        tablet_options.db_id = 1;
        TBLID table_id = 1;
        TPID tp_id = 0;
        auto tablet_id = make_tablet_id(table_id, tp_id);
        tablet_options.tablet_id = tablet_id;
        tablet_options.tablet_dir = "./data/";
        zq::TabletRef tablet = std::make_shared<zq::Tablet>(tablet_options);
        common::AutoRemoveFile auto_remove_file("./data");
        auto st = tablet->initialize();
        LOG_AND_ASSERT(st.ok());
        // insert ann data
        uint64_t applied_log_index = 0;
        int32_t ann_record_cnt = _nb / 2;
        for (int32_t i = 0; i < ann_record_cnt; i++) {
            auto row = dataset[i];
            st = tablet->Insert(schema_hierarchy, row, applied_log_index);
            LOG_AND_ASSERT(st.ok());
            if (i % 10000 == 0) {
                LOG(NOTICE) << "insert 1w row";
            }
            applied_log_index++;
        }
        // build vector index
        auto vector_indexes = schema_hierarchy->internal_schema->get_vector_indexes();
        LOG_AND_ASSERT(vector_indexes.size() == 1);
        auto index_id = vector_indexes[0]->get_index_id();
        IDXMAJOR index_major_version = 1;
        st = tablet->build_vindex(schema_hierarchy, index_id, 
                index_major_version, common::VIndexBuildType::STABLE, false);
        LOG_AND_ASSERT(st.ok());
        for (int32_t i = ann_record_cnt; i < _nb; i++) {
            auto row = dataset[i];
            st = tablet->Insert(schema_hierarchy, row, applied_log_index);
            LOG_AND_ASSERT(st.ok());
            if (i % 10000 == 0) {
                LOG(NOTICE) << "insert 1w row";
            }
            applied_log_index++;
        }
        // search
        test_recall(tablet, schema_hierarchy, conf);
        
        // clear
        tablet->destroy();
        while(true) {
            if (tablet->could_be_gc()) {
                break;
            }
            std::this_thread::sleep_for(std::chrono::seconds(1));
        }
        ::mochow::datanode::g_schema_cache->clear();
    }

    void test_range_recall(const nlohmann::json& conf) {
        auto schema_hierarchy = generate_schema(conf);
        std::vector<zq::RowRef> dataset;
        generate_dataset(schema_hierarchy, dataset);
        // init tablet
        zq::TabletOptions tablet_options;
        tablet_options.db_id = 1;
        TBLID table_id = 1;
        TPID tp_id = 0;
        auto tablet_id = make_tablet_id(table_id, tp_id);
        tablet_options.tablet_id = tablet_id;
        tablet_options.tablet_dir = "./data/";
        zq::TabletRef tablet = std::make_shared<zq::Tablet>(tablet_options);
        common::AutoRemoveFile auto_remove_file("./data");
        auto st = tablet->initialize();
        LOG_AND_ASSERT(st.ok());
        // insert ann data
        uint64_t applied_log_index = 0;
        int32_t ann_record_cnt = _nb / 2;
        for (int32_t i = 0; i < ann_record_cnt; i++) {
            auto row = dataset[i];
            st = tablet->Insert(schema_hierarchy, row, applied_log_index);
            LOG_AND_ASSERT(st.ok());
            if (i % 10000 == 0) {
                LOG(NOTICE) << "insert 1w row";
            }
            applied_log_index++;
        }
        // build vector index
        auto vector_indexes = schema_hierarchy->internal_schema->get_vector_indexes();
        LOG_AND_ASSERT(vector_indexes.size() == 1);
        auto index_id = vector_indexes[0]->get_index_id();
        IDXMAJOR index_major_version = 1;
        st = tablet->build_vindex(schema_hierarchy, index_id, 
                index_major_version, common::VIndexBuildType::STABLE, false);
        LOG_AND_ASSERT(st.ok());
        for (int32_t i = ann_record_cnt; i < _nb; i++) {
            auto row = dataset[i];
            st = tablet->Insert(schema_hierarchy, row, applied_log_index);
            LOG_AND_ASSERT(st.ok());
            if (i % 10000 == 0) {
                LOG(NOTICE) << "insert 1w row";
            }
            applied_log_index++;
        }
        // search
        calc_range_recall(tablet, schema_hierarchy, conf);
        
        // clear
        tablet->destroy();
        while(true) {
            if (tablet->could_be_gc()) {
                break;
            }
            std::this_thread::sleep_for(std::chrono::seconds(1));
        }
        ::mochow::datanode::g_schema_cache->clear();
    }

    void test_recall_without_vindex(const nlohmann::json& conf) {
        auto schema_hierarchy = generate_schema_without_vindex();
        std::vector<zq::RowRef> dataset;
        generate_dataset(schema_hierarchy, dataset);
        // init tablet
        zq::TabletOptions tablet_options;
        tablet_options.db_id = 1;
        TBLID table_id = 1;
        TPID tp_id = 0;
        auto tablet_id = make_tablet_id(table_id, tp_id);
        tablet_options.tablet_id = tablet_id;
        tablet_options.tablet_dir = "./data/";
        zq::TabletRef tablet = std::make_shared<zq::Tablet>(tablet_options);
        common::AutoRemoveFile auto_remove_file("./data");
        auto st = tablet->initialize();
        LOG_AND_ASSERT(st.ok());
        // insert data
        uint64_t applied_log_index = 0;
        for (int32_t i = 0; i < _nb; i++) {
            auto row = dataset[i];
            st = tablet->Insert(schema_hierarchy, row, applied_log_index);
            LOG_AND_ASSERT(st.ok());
            if (i % 10000 == 0) {
                LOG(NOTICE) << "insert 1w row";
            }
            applied_log_index++;
        }

        // search
        test_recall(tablet, schema_hierarchy, conf);
        
        // clear
        tablet->destroy();
        while(true) {
            if (tablet->could_be_gc()) {
                break;
            }
            std::this_thread::sleep_for(std::chrono::seconds(1));
        }
        ::mochow::datanode::g_schema_cache->clear();
    }
    
    void test_filter_recall(const nlohmann::json& conf) {
        auto schema_hierarchy = generate_schema_for_filter(conf);
        std::vector<zq::RowRef> dataset;
        generate_dataset(schema_hierarchy, dataset);
        // init tablet
        zq::TabletOptions tablet_options;
        tablet_options.db_id = 1;
        TBLID table_id = 1;
        TPID tp_id = 0;
        auto tablet_id = make_tablet_id(table_id, tp_id);
        tablet_options.tablet_id = tablet_id;
        tablet_options.tablet_dir = "./data/";
        zq::TabletRef tablet = std::make_shared<zq::Tablet>(tablet_options);
        common::AutoRemoveFile auto_remove_file("./data");
        auto st = tablet->initialize();
        LOG_AND_ASSERT(st.ok());
        // insert data
        uint64_t row_cnt = 0;
        uint64_t applied_log_index = 0;
        for (auto& row : dataset) {
            st = tablet->Insert(schema_hierarchy, row, applied_log_index);
            LOG_AND_ASSERT(st.ok());
            if (row_cnt % 10000 == 0) {
                LOG(NOTICE) << "insert 1w row";
            }
            row_cnt++;
            applied_log_index++;
        }
        // build vector index
        auto vector_indexes = schema_hierarchy->internal_schema->get_vector_indexes();
        LOG_AND_ASSERT(vector_indexes.size() == 1);
        auto index_id = vector_indexes[0]->get_index_id();
        IDXMAJOR index_major_version = 1;
        st = tablet->build_vindex(schema_hierarchy, index_id, 
                index_major_version, common::VIndexBuildType::STABLE, false);
        LOG_AND_ASSERT(st.ok());
        // search
        calc_filter_recall(tablet, schema_hierarchy, conf);
        // clear
        tablet->destroy();
        while(true) {
            if (tablet->could_be_gc()) {
                break;
            }
            std::this_thread::sleep_for(std::chrono::seconds(1));
        }
        ::mochow::datanode::g_schema_cache->clear();
    }

    void test_process(nlohmann::json& conf, const std::string& step) {
        auto re = zq::initialize_zhuque_environment();
        LOG_AND_ASSERT(re == 0);

        if ((step == "") || (step == TEST_ANN_RECALL)) {
            CALC_TIME_SPAN(test_ann_recall(conf));
            LOG(NOTICE) << "[" << get_time_diff() << " s] " << _ann_test_name
                << " | " << _index_type << " | Test ann recall finished. elapse ="
                << t_diff << "s";
        }
        if ((step == "") || (step == TEST_KNN_RECALL)) {
            CALC_TIME_SPAN(test_knn_recall(conf));
            LOG(NOTICE) << "[" << get_time_diff() << " s] " << _ann_test_name
                << " | " << _index_type << " | Test knn recall finished. elapse ="
                << t_diff << "s";
        }
        if ((step == "") || (step == TEST_ANN_KNN_RECALL)) {
            CALC_TIME_SPAN(test_ann_knn_recall(conf));
            LOG(NOTICE) << "[" << get_time_diff() << " s] " << _ann_test_name
                << " | " << _index_type << " | Test ann&knn recall finished. elapse ="
                << t_diff << "s";
        }
        if ((step == "") || (step == TEST_RANGE_RECALL)) {
            CALC_TIME_SPAN(test_range_recall(conf));
            LOG(NOTICE) << "[" << get_time_diff() << " s] " << _ann_test_name
                << " | " << _index_type << " | Test ann&knn recall finished. elapse ="
                << t_diff << "s";
        }
        if ((step == "") || (step == TEST_RECALL_WITHOUT_VINDEX)) {
            CALC_TIME_SPAN(test_recall_without_vindex(conf));
            LOG(NOTICE) << "[" << get_time_diff() << " s] " << _ann_test_name
                << " | " << _index_type << " | Test recall without index finished. elapse ="
                << t_diff << "s";
        }
        if ((step == "") || (step == TEST_FILTER_RECALL)) {
            CALC_TIME_SPAN(test_filter_recall(conf));
            LOG(NOTICE) << "[" << get_time_diff() << " s] " << _ann_test_name
                << " | " << _index_type << " | Test filter recall finished. elapse ="
                << t_diff << "s";
        }
    }

    int test(int argc, char** argv) {
        // start test
        _T0 = elapsed();

        // args
        std::string dataset_path_prefix = argv[1];
        std::string dataset_name = argv[2];
        std::string conf_file = argv[3];
        std::string index_type = to_upper(argv[4]);
        std::string step_name = "";
        if (argc >= 6) {
            step_name = to_upper(argv[5]);
        }

        // dataset init
        int dataset_index = -1;
        for (size_t i = 0; i < hdf5_datasets.size(); i++) {
            if (to_upper(hdf5_datasets[i].dataset_name) == to_upper(dataset_name)) {
                dataset_index = i;
            }
        }
        if (dataset_index == -1) {
            LOG(NOTICE) << "Invalid dataset name " << dataset_name;
            return 1;
        }
        set_ann_test_prefix(dataset_path_prefix.c_str());
        // load hdf5 file
        apply_benchmark(hdf5_datasets[dataset_index]);

        // index type init
        if (index_type == vindex::IndexType::INDEX_HNSW) {
             _index_type = index_type;
        } else {
            LOG(NOTICE) << std::string("Invalid index type ") << index_type;
            return -1;
        }

        // step init
        if ((step_name == "") ||
            (step_name == TEST_ANN_RECALL) ||
            (step_name == TEST_KNN_RECALL) ||
            (step_name == TEST_ANN_KNN_RECALL) ||
            (step_name == TEST_RANGE_RECALL) ||
            (step_name == TEST_RECALL_WITHOUT_VINDEX) ||
            (step_name == TEST_FILTER_RECALL)) {
            // do nothing
        } else {
            LOG(NOTICE) << std::string("Invalid benchmark step ") << step_name;
            return -1;
        }

        // conf init
        std::ifstream file(conf_file, std::ios::in);
        std::stringstream buffer;
        buffer << file.rdbuf();
        std::string cfg_str = buffer.str();
        nlohmann::json cfg = nlohmann::json::parse(cfg_str);

        cfg[vindex::IndexMeta::DIM] = _dim;
        cfg[vindex::IndexMeta::METRIC_TYPE] = _metric_str;
        LOG(NOTICE) << "confg:" << cfg;
        // test body
        test_process(cfg, step_name);
        // finish test
        //free_all();
        //std::this_thread::sleep_for(std::chrono::seconds(10));
        return 0;
    }
private:
    std::string _index_type;
};

}

void helper() {
    LOG(NOTICE) << "argv[0] = execution path";
    LOG(NOTICE) << "argv[1] = {dataset_path_prefix} e.g. ./test_dataset/";
    LOG(NOTICE) << "argv[2] = {hdf5_name} e.g. Fashion-MNIST";
    LOG(NOTICE) << "argv[3] = {conf_file} e.g. ./test_dataset/hnsw.cfg";
    LOG(NOTICE) << "argv[4] = index_type e.g. HNSW";
    LOG(NOTICE) << "argv[5](optional) = step_name, e.g. recall";
}

void init_common_log() {
    std::string path_str = "/tmp/.log.conf";
    try {
        std::ofstream ofs(path_str);
        ofs << "[comlog]" << std::endl;
        ofs << "level: " << 4 << std::endl;
        ofs << "procname: " << "perf" << std::endl;
        ofs << "time_format: %Y-%m-%dT%H:%M:%S" << std::endl;
        ofs << std::endl;
        ofs.close();
    } catch (const std::exception& error) {
        LOG(FATAL) << "load com_log config failed, error:" << error.what();
        return;
    }

    comcfg::Configure conf;
    base::FilePath path(path_str);
    int ret =conf.load(path.DirName().value().c_str(), path.BaseName().value().c_str());
    assert(ret == 0);

    ret = comlog_init(conf["comlog"]);
    assert(ret == 0);
    ::unlink(path_str.c_str());
}

// argv[0] = execution path
// argv[1] = {dataset_path_prefix} e.g. ./test_dataset/
// argv[2] = {hdf5_name} e.g. Fashion-MNIST
// argv[3] = {conf_file} e.g. ./test_dataset/hnsw.cfg
// argv[4] = index_type e.g. HNSW
// argv[5](optional) = step_name, e.g. recall
int main(int argc, char** argv) {
    init_common_log();
    
    if (google::SetCommandLineOption("bvar_dump", "true").empty()) {
        LOG(FATAL) << "Fail to enable bvar dump";
    }

    mochow::benchmark::zhuque::LongTest long_test;
    if (argc < 5 || argc > 7) {
        helper();
        return -1;
    }
    return long_test.test(argc, argv);
}
