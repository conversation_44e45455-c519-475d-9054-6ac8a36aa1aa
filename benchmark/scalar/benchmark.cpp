#include <chrono>
#include <vector>
#include <filesystem>
#include <thread>
#include <string>
#include <iostream>
#include <fstream>
#include <sstream>
#include <algorithm>
#include <base/comlog_sink.h>
#include <base/files/file_path.h>
#include <base/logging.h>
#include <base/configure/Configure.h>
#include <gflags/gflags.h>
#include "nlohmann/json.hpp"

#include "comlogplugin.h"
#include "core/src/common/using.h"
#include "core/src/io/file_system.h"
#include "core/src/scalar/scalar.h"
#include "core/src/scalar/flags.h"
#include "core/src/scalar/rocksdb/rocksdb.h"
#include "baidu/vdb/mochow/core/src/common/config.h"
#include "core/src/schema/id_allocator.h"
#include "core/src/schema/schema_builder.h"
#include "core/src/schema/schema_cache.h"
#include "core/test/tools/random_generator.h"
#include "core/test/tools/schema_generator.h"
#include "core/test/tools/row_generator.h"
#include "core/src/scalar/stopwatch.h"

namespace mochow::benchmark::scalar {
class Benchmark {
public:
    Benchmark(const TBLID table_id, const std::string& table_name) 
        : _table_id(table_id), _table_name(table_name) {
        _column_id_allocator = std::make_shared<schema::IdAllocator>();
        _index_id_allocator = std::make_shared<schema::IdAllocator>();
    }

    Status create_dir(const std::string& path) {
        bool found = false;

        do {
            auto br = io::get_file_system()->exist(
                    path.c_str(), 60*1000*1000, nullptr, found);
            if (br != BRESULT_OK) {
                break;
            }

            if (!found) {
                break;
            }

            struct stat s;
            if (::stat(path.c_str(), &s) != 0) {
                return Status(ERR_FAIL);
            }

            if(s.st_mode & S_IFDIR){
                return Status(OK);
            }
        } while (0);
        
        auto br = io::get_file_system()->mkdir(
            path.c_str(), 0, 60*1000*1000, nullptr);
        if (baidu::sfl::is_failed(br)) {
            LOG(WARNING) << "mkdir: " << path << " failed: " << baidu::sfl::bresult_to_string(br);
            return Status(ERR_FAIL);
        }

        return Status(OK);
    }

    void generate_schema() {
        // generate external schema
        LOG(NOTICE) << "======generator schema==========";
         DBID db_id = 1;
        std::string db_name = "db_test";
        std::string table_name = _table_name;
        if (table_name.empty()) {
            table_name = "book_vector_" + std::to_string(_table_id);
        }
        
        schema::SchemaBuilder builder(
                schema::SchemaLayer::EXTERNAL, _column_id_allocator, _index_id_allocator);
        uint64_t schema_version = 1;
    
        std::map<std::string, DataType> map = {
            {"bookName", DataType::STRING},
            {"author", DataType::STRING},
            {"serialNumber1", DataType::BINARY},
            {"serialNumber2", DataType::UUID},
            {"serialNumber3", DataType::TEXT},
            {"serialNumber4", DataType::TEXT_GBK},
            {"serialNumber5", DataType::TEXT_GB18030},
            {"authorAge", DataType::INT8},
            {"serial1", DataType::INT16},
            {"serial2", DataType::INT32},
            {"serial3", DataType::INT64},
            {"serial4", DataType::UINT8},
            {"serial5", DataType::UINT16},
            {"serial6", DataType::UINT32},
            {"serial7", DataType::UINT64},
            {"publishTime1", DataType::TIME},
            {"publishTime2", DataType::TIMESTAMP},
            {"publishTime3", DataType::DATE},
            {"publishTime4", DataType::DATETIME},
            {"publishTime5", DataType::HLC},
            {"income1", DataType::FLOAT},
            {"income2", DataType::DOUBLE}
        };
       
        builder.initialize_schema(schema_version, db_id, db_name, _table_id, table_name);
        auto cf_id = builder.next_cf_id();
        LOG_AND_ASSERT(cf_id == schema::DEFAULT_SCALAR_CF_ID);
        auto ok = builder.add_scalar_column_family(cf_id, schema::DEFAULT_SCALAR_CF_NAME);
        LOG_AND_ASSERT(ok);
        ok = builder.add_primary_key(builder.next_column_id(), 
            "id", DataType::UINT64, SortType::ASCEND, true, true);
        LOG_AND_ASSERT(ok);
        
        COLID col_id_book = 0;
        COLID col_id_author = 0;
        for (const auto& [col_name, type] : map) {
            COLID col_id = builder.next_column_id();
            if (col_name == "bookName") {
                col_id_book = col_id;
            }

            if (col_name == "author") {
                col_id_author = col_id;
            }

            ok = builder.add_external_column(cf_id, "scalar", 
                col_id, col_name, type, false, false);
            LOG_AND_ASSERT(ok);
        }

        cf_id = builder.next_cf_id();
        COLID col_id_vector = builder.next_column_id();
        ok = builder.add_external_column(cf_id, "vector", col_id_vector, "vector", DataType::FLOAT_VECTOR, false, false, 768);
        LOG_AND_ASSERT(ok);

        //add index key
        std::vector<COLID> index_key_ids;
        index_key_ids.emplace_back(col_id_book);
        index_key_ids.emplace_back(col_id_author);
        std::vector<schema::ScalarIndexKeyAttribute> index_key_attributes;
        {
            auto& index_key_attribute = index_key_attributes.emplace_back();
            index_key_attribute.sort_type = get_sort_type_string(SortType::ASCEND);
            index_key_attribute.index_structure_type = schema::ScalarIndexStructureType::DEFAULT;
        }
        {
            auto& index_key_attribute = index_key_attributes.emplace_back();
            index_key_attribute.sort_type = get_sort_type_string(SortType::ASCEND);
            index_key_attribute.index_structure_type = schema::ScalarIndexStructureType::DEFAULT;
        }

        schema::ScalarIndexParam scalar_index_param;
        scalar_index_param.index_type = schema::ScalarIndexType::SECONDARY;
        ok = builder.add_scalar_index(builder.next_index_id(), "scalar_index", scalar_index_param, false, false,
                                      index_key_ids, index_key_attributes);
        LOG_AND_ASSERT(ok);

        auto external_schema = builder.move_and_get_schema();
        LOG(NOTICE) << "external schema:" << external_schema->to_string();
        // generate schema cache
        auto schema_hierarchy = ::mochow::datanode::g_schema_cache->generate_schema_hierarchy_from_external_schema(
            external_schema, _column_id_allocator, _index_id_allocator);
        ok = ::mochow::datanode::g_schema_cache->add_schema_hierarchy(schema_hierarchy);
        LOG_AND_ASSERT(ok);
    }

    void generate_dataset(std::vector<zq::RowRef>& ds, int32_t nums) {
        LOG(NOTICE) << "generate dataset row nums:" << nums;
        common::RowGenerator row_generator(::mochow::datanode::g_schema_cache);

        int64_t ds_size = 0;
        for (int32_t i = 0; i < nums; ++i) {
            auto row = row_generator.generate_scalar_row(_table_id);
            ds_size += row->get_size();
            ds.emplace_back(row);
        }

        LOG(NOTICE) << "finish generate dataset row nums:" << nums << " size: " << ds_size;
    }

    void prepare_dataset(std::vector<zq::RowRef>& dts,
        std::map<int32_t, std::vector<zq::RowRef>>& batch_dts,
        std::map<int32_t, std::vector<zq::RowKey>>& batch_dts_rowkey,
        int32_t ds_nums) {
        generate_dataset(dts, ds_nums);
        int32_t ds_nums_per = 10;
        if (ds_nums / 1000 > 0) {
            ds_nums_per = ds_nums / 1000;
        }

        for (int32_t i = 0; i < 1000; i++) {
            std::vector<zq::RowRef> rows;
            generate_dataset(rows, ds_nums_per);
            batch_dts[i] = rows;
            std::vector<zq::RowKey> keys;
            for (auto& row : rows) {
                keys.push_back(*row->get_allkey().get());
            }

            batch_dts_rowkey[i] = std::move(keys);
        }
    }

    void do_work(uint64_t s_id, uint64_t e_id, const std::string& datanode_dir, int32_t cycle) {
        auto schema_hierarchy  = ::mochow::datanode::g_schema_cache->get_schema_hierarchy(_table_id);
        CHECK(schema_hierarchy != nullptr);
        schema::SchemaRef scalar_schema = schema_hierarchy->scalar_schema;
        CHECK(scalar_schema != nullptr);
        DBID db_id = scalar_schema->get_db_id();

        //open dbs
        std::vector<mochow::scalar::ScalarRef> scalars;
        std::vector<rocksdb::DB*>  DBs;
        std::vector<rocksdb::ColumnFamilyHandle*> cf_handles;
        for (uint64_t id = s_id; id < e_id; id++) {
            TPID tp_id = (TPID)id + 1;
            TabletID tablet_id = make_tablet_id(_table_id, tp_id);
            std::string tmp_datanode_dir = datanode_dir +  "/" + std::to_string(tablet_id);

            auto data_dir = tmp_datanode_dir + "/" + mochow::scalar::ScalarOptions::SCALAR_DATA;
            rocksdb::DB* db = nullptr;
            rocksdb::ColumnFamilyHandle* cf_handle = nullptr;
            auto status = initialize_rocksdb(data_dir, &db, &cf_handle);
            CHECK(status.ok());
            mochow::scalar::ScalarOptions opts(mochow::scalar::ROCKSDB, tmp_datanode_dir, db_id, tablet_id, db, cf_handle);

            mochow::scalar::Scalar* scalar = mochow::scalar::Scalar::open(opts);
            CHECK(scalar != nullptr);
            if (scalar == nullptr) {
                LOG(NOTICE) << "open tablet: " << tablet_id << " failed.";
                return;
            }
            auto scalarref = std::shared_ptr<mochow::scalar::Scalar>(scalar);
            scalars.emplace_back(scalarref);
            DBs.push_back(db);
            cf_handles.push_back(cf_handle);
        }

        //test
        int round = 0;
        while (true) {
            int64_t r = common::random_int32();
            auto scalar = scalars[r % scalars.size()];
            auto tp_id = scalar->get_options()->get_tp_id();
            auto tablet_id = make_tablet_id(_table_id, tp_id);
            round++;

            if (cycle >0 && round >= cycle) {
                break;
            }

            LOG(NOTICE) << "round: " << round << "  tablet: " << tablet_id << " start to test.";

            //step1 insert rows
            LOG(NOTICE) << "begin insert row: " << _dts.size() << " into tablet:" << tablet_id;
            mochow::scalar::MonotonicStopWatch sw;
            sw.start();
            for (const auto& row : _dts) {
                auto rt = scalar->Insert(schema_hierarchy, row);
                CHECK(rt.ok());
            }
            auto insert_elapsed = sw.elapsed_us();
            LOG(NOTICE) << "finish insert row: " << _dts.size() << " into tablet: " 
                << tablet_id << " elapsed: " << insert_elapsed << "us";

            //step2 batch insert rows
            LOG(NOTICE) << "begin batch insert row: " << _batch_dts.size() * _batch_dts[0].size() << " into tablet:" << tablet_id;
            for (const auto& [i, dset] : _batch_dts) {
                size_t affect_cnt = 0;
                auto rt = scalar->BatchInsert(schema_hierarchy, dset, &affect_cnt);
                CHECK(rt.ok());
                CHECK(affect_cnt == dset.size());
            }
            auto batchinsert_elapsed = sw.elapsed_us();
            LOG(NOTICE) << "finish batchinsert row: " << _batch_dts.size() * _batch_dts[0].size() << " into tablet: " 
                << tablet_id << " elapsed: " << batchinsert_elapsed - insert_elapsed << "us";

            //step3 query row
            std::vector<zq::RowRef> q_rows;
            LOG(NOTICE) << "begin query row: " << _dts.size() << " from tablet:" << tablet_id;
            for (const auto& row : _dts) {
                zq::RowRef q_row;
                auto rt = scalar->Query(schema_hierarchy, *row->get_allkey().get(), zq::full_projection(), q_row);
                CHECK(rt.ok());
                q_rows.emplace_back(q_row);
            }
            auto query_elapsed = sw.elapsed_us();
            LOG(NOTICE) << "finish query row: " << q_rows.size() << " into tablet: " 
                << tablet_id << " elapsed: " << query_elapsed - batchinsert_elapsed << "us";

            //step4 iterator row
            LOG(NOTICE) << "begin iterator " << " tablet:" << tablet_id;
            std::shared_ptr<zq::InitArgs> args = std::make_shared<zq::InitArgs>();
            auto iter = scalar->create_iterator(args);
            Status rt;
            rt = iter->seek_to_first();
            CHECK(rt.ok());
            int count = 0;
            do {
                zq::RowRef value;
                rt = iter->get(value);
                if (rt.failed()) {
                    LOG(WARNING) << "tablet: " << tablet_id 
                        << "has scan count: " << count 
                        << " iterator failed: " << rt.msg_str();
                    return;
                }

                count++;
                rt = iter->next();
                //LOG(NOTICE) << "query row: " << value->to_string();
            } while (rt.ok());
            auto iter_elapsed = sw.elapsed_us();
            LOG(NOTICE) << "finish iterator row: " << count << " from tablet: " 
                << tablet_id << " elapsed: " << iter_elapsed - query_elapsed << "us";

            //step4 update row
            LOG(NOTICE) << "begin update row: " << _dts.size() << " from tablet:" << tablet_id;
            for (const auto& row : _dts) {
                auto rt = scalar->Update(schema_hierarchy, row);
                CHECK(rt.ok());
            }
            auto update_elapsed = sw.elapsed_us();
            LOG(NOTICE) << "finish query row: " << _dts.size() << " into tablet: " 
                << tablet_id << " elapsed: " << update_elapsed - iter_elapsed << "us";

            //step5 upsert row
            LOG(NOTICE) << "begin upsert row: " << _dts.size() << " from tablet:" << tablet_id;
            for (const auto& row : _dts) {
                auto rt = scalar->Update(schema_hierarchy, row);
                CHECK(rt.ok());
            }
            auto upsert_elapsed = sw.elapsed_us();
            LOG(NOTICE) << "finish upsert row: " << _dts.size() << " into tablet: " 
                << tablet_id << " elapsed: " << upsert_elapsed - update_elapsed << "us";

            //step6 delete row
            LOG(NOTICE) << "begin delete row: " << _dts.size() << " from tablet:" << tablet_id;
            for (const auto& row : _dts) {
                auto rt = scalar->Delete(schema_hierarchy, *row->get_allkey().get());
                CHECK(rt.ok());
            }
            auto delete_elapsed = sw.elapsed_us();
            LOG(NOTICE) << "finish delete row: " << _dts.size() << " into tablet: " 
                << tablet_id << " elapsed: " << delete_elapsed - upsert_elapsed << "us";

            //step7 batchdelete row
            LOG(NOTICE) << "begin batchdelete row: " << _dts.size() << " from tablet:" << tablet_id;
            for (const auto& [_, v] : _batch_dts_rowkey) {
                auto rt = scalar->BatchDelete(schema_hierarchy, v);
                CHECK(rt.ok());
            }
            auto batchdelete_elapsed = sw.elapsed_us();
            LOG(NOTICE) << "finish batchdelete row: " << _dts.size() << " into tablet: " 
                << tablet_id << " elapsed: " << batchdelete_elapsed - delete_elapsed << "us";
        }

        for (const auto& scalar : scalars) {
            scalar->close();
        }

        for (size_t index = 0; index < DBs.size(); ++index) {
            DBs[index]->DestroyColumnFamilyHandle(cf_handles[index]);
            delete DBs[index];
        }
    }

    Status initialize_rocksdb(const std::string& db_path,
                              rocksdb::DB** db,
                              rocksdb::ColumnFamilyHandle** cf_handle) {
        rocksdb::DBOptions db_option;
        db_option.max_open_files = mochow::scalar::FLAGS_scalar_db_max_open_files;
        db_option.create_if_missing = true;
        db_option.create_missing_column_families = true;

        rocksdb::ColumnFamilyDescriptor cf_descriptor;
        mochow::scalar::rocks::RocksDB::initialize_column_family_option(&cf_descriptor.options);

        std::vector<rocksdb::ColumnFamilyDescriptor> cf_descriptors;
        cf_descriptors.push_back(cf_descriptor);
        std::vector<rocksdb::ColumnFamilyHandle*> cf_handles;

        auto status = rocksdb::DB::Open(db_option, db_path, cf_descriptors, &cf_handles, db);
        CHECK(status.ok());
        CHECK(cf_handles.size() == 1);
        *cf_handle = cf_handles[0];
        return Status();
    }

public:
    void test_process(const std::string& data_path_prefix, int32_t db_num, 
        int32_t thread_num, int32_t row_nums, int32_t cycle) {
        //generate schema
        generate_schema();

         //init filesystem operator
        baidu::sfl::PosixFileSystemOption option("posix_file_system",
                std::thread::hardware_concurrency() * 2);
        auto ok = io::initialize_posix_file_system(std::move(option));
        CHECK(baidu::sfl::is_succeeded(ok));

        //mkdir prefix path
        auto rt = create_dir(data_path_prefix);
        CHECK(rt.ok());

        //prepare dataset
        prepare_dataset(_dts, _batch_dts, _batch_dts_rowkey, row_nums);

        std::vector<std::thread> pool;
        int32_t db_per_t = 1;
        if (thread_num > 0) {
            db_per_t = db_num / thread_num + 1;
        }
        
        for (int i = 0; i < thread_num; i++) {
            pool.emplace_back([this, i, data_path_prefix, db_per_t, cycle] 
                { do_work(db_per_t*i, db_per_t*(i+1), data_path_prefix, cycle); });
        }

        for (size_t i = 0; i < pool.size(); i++) {
            pool[i].join();
        }
    }

    int test(int argc, char** argv) {
        // args
        //more dbs will consume too much memory,
        //because write/delete will first be written to memtable 
        //and reached max_buffer_size can flush to disk
        //and as the same query operator will cache in block cache.
        std::string data_path_prefix = argv[1];
        int32_t  db_num = 20;
        if (argc >= 2) {
            db_num = std::stoi(argv[2]);
        }

        int32_t thread_num = 16;
        if (argc >= 3) {
            thread_num = std::stoi(argv[3]);
        }

        int32_t row_nums = 100000;
        if (argc >= 4) {
            row_nums = std::stoi(argv[4]);
        }

        int32_t cycle = -1;
        if (argc >= 5) {
            cycle = std::stoi(argv[5]);
        }

        LOG(NOTICE) << "thread num:" << thread_num << " db num: " << db_num;

        // test body
        test_process(data_path_prefix, db_num, thread_num, row_nums, cycle);
        
        std::this_thread::sleep_for(std::chrono::seconds(10));
        return 0;
    }
private:
    TBLID _table_id;
    std::string _table_name;
    schema::IdAllocatorRef _column_id_allocator;
    schema::IdAllocatorRef _index_id_allocator;
    std::vector<zq::RowRef> _dts;
    std::map<int32_t, std::vector<zq::RowRef>> _batch_dts;
    std::map<int32_t, std::vector<zq::RowKey>> _batch_dts_rowkey;
};
}

void helper() {
    LOG(NOTICE) << "argv[0] = execution path";
    LOG(NOTICE) << "argv[1] = {data_path_prefix} e.g. ./test_data/";
    LOG(NOTICE) << "argv[2] = db_num e.g. 10";
    LOG(NOTICE) << "argv[3] = thread_num e.g. 8";
    LOG(NOTICE) << "argv[4] = dataset size e.g. 100000";
    LOG(NOTICE) << "argv[5] = cycle e.g. -1: loop running, xx: execute xx rounds";
}

void init_common_log() {
std::string path_str = "/tmp/.log.conf";
    try {
        std::ofstream ofs(path_str);
        ofs << "[comlog]" << std::endl;
        ofs << "level: " << 8 << std::endl;
        ofs << "procname: " << "perf" << std::endl;
        ofs << "time_format: %Y-%m-%dT%H:%M:%S" << std::endl;
        ofs << std::endl;
        ofs.close();
    } catch (const std::exception& error) {
        LOG(FATAL) << "load com_log config failed, error:" << error.what();
        return;
    }

    comcfg::Configure conf;
    base::FilePath path(path_str);
    int ret =conf.load(path.DirName().value().c_str(), path.BaseName().value().c_str());
    assert(ret == 0);

    ret = comlog_init(conf["comlog"]);
    assert(ret == 0);
    ::unlink(path_str.c_str());
}

// argv[0] = execution path
// argv[1] = {dataset_path_prefix} e.g. ./test_dataset/
// argv[2] = db_num e.g. 10
// argv[3] = thread_num e.g. 8
// argv[4] = dataset nums e.g. 100000"
// argv[5] = cycle e.g. -1: loop running, xx: execute xx rounds
int main(int argc, char** argv) {
    //init_common_log();
    // Init comlog
    if (::mochow::common::init_comlog() != 0) {
        LOG(FATAL) << "mochow-standalone startup failed due to init comlog failed";
        _exit(-1);
    }
    
    if (google::SetCommandLineOption("bvar_dump", "true").empty()) {
        LOG(FATAL) << "Fail to enable bvar dump";
    }



    mochow::TBLID table_id = 1;
    std::string table_name = "perf_test";
    mochow::benchmark::scalar::Benchmark benchmark(table_id, table_name);
    if (argc < 2 || argc > 6) {
        helper();
        return -1;
    }

    return benchmark.test(argc, argv);
}
