#include <chrono>
#include <vector>
#include <filesystem>
#include <thread>
#include <string>
#include <iostream>
#include <fstream>
#include <sstream>
#include <algorithm>
#include <unordered_map>
#include <base/comlog_sink.h>
#include <base/files/file_path.h>
#include <base/logging.h>
#include <base/configure/Configure.h>
#include <gflags/gflags.h>
#include "nlohmann/json.hpp"
#include "benchmark/common/benchmark_hdf5.h"

#include "comlogplugin.h"
#include "core/src/common/using.h"
#include "core/src/io/file_system.h"
#include "core/src/moss/moss.h"
#include "core/src/sstable/libmain.h"
#include "core/src/schema/id_allocator.h"
#include "core/src/schema/schema_builder.h"
#include "core/src/schema/schema_cache.h"
#include "core/test/tools/random_generator.h"
#include "core/test/tools/row_generator.h"
#include "third-party/hnswlib/src/simd/hook.h"

namespace mochow::benchmark {
static const std::string TEST_INCREMENTAL_RECALL = "INCREMENTAL_RECALL";
static const std::string TEST_RECALL = "RECALL";
static const std::string TEST_QPS = "QPS";
static const std::string TEST_LATENCY = "LATENCY";
static const std::string TEST_RANGE = "RANGE";

static const std::vector<DatasetHDF5> hdf5_datasets = {
        DatasetHDF5(
                "Fashion-MNIST",
                784,
                60000,
                10000,
                100,
                mochow::vindex::MetricType::L2,
                "fashion_mnist-784-euclidean"),
        DatasetHDF5(
                "GIST",
                960,
                1000000,
                1000,
                100,
                mochow::vindex::MetricType::L2,
                "gist-960-euclidean"),
        DatasetHDF5(
                "MNIST",
                784,
                60000,
                10000,
                100,
                mochow::vindex::MetricType::L2,
                "mnist-784-euclidean"),
        DatasetHDF5(
                "SIFT",
                128,
                1000000,
                10000,
                100,
                mochow::vindex::MetricType::L2,
                "sift-128-euclidean"),
        DatasetHDF5(
                "COHERE-1M",
                768,
                1000000,
                1000,
                100,
                mochow::vindex::MetricType::IP,
                "Cohere-1M-768-euclidean"),
        DatasetHDF5(
                "COHERE-350K",
                768,
                350000,
                1000,
                100,
                mochow::vindex::MetricType::IP,
                "Cohere-350K-768-euclidean"),
        DatasetHDF5(
                "GLOVE-25",
                25,
                1183514,
                10000,
                100,
                mochow::vindex::MetricType::IP,
                "glove-25-angular"),
        DatasetHDF5(
                "NYTIMES",
                256,
                290000,
                10000,
                100,
                mochow::vindex::MetricType::IP,
                "nytimes-256-angular"),
        DatasetHDF5(
                "Last.fm",
                65,
                292385,
                50000,
                100,
                mochow::vindex::MetricType::IP,
                "lastfm-64-dot"),
};

std::string to_upper(std::string str) {
    std::transform(str.begin(), str.end(), str.begin(), ::toupper);
    return str;
}

int get_used_mem_kb() {
    std::ifstream file("/proc/self/status");
    std::string line;

    while (std::getline(file, line)) {
        if (line.substr(0, 6) == "VmRSS:") {
            int vmRSS = 0;

            // Extract the number from the string
            if (std::sscanf(line.c_str(), "VmRSS: %d", &vmRSS) == 1) {
                // Return the extracted value
                return vmRSS;
            }
        }
    }

    // Return 0 if VmRSS is not found or cannot be parsed
    return 0;
}

static std::unordered_map<std::string, int> index_map = {

    {mochow::vindex::IndexType::INDEX_HNSW, 0},
    {mochow::vindex::IndexType::INDEX_FLAT, 1},
    {mochow::vindex::IndexType::INDEX_DISKANN, 2},
    {mochow::vindex::IndexType::INDEX_PUCK, 3},
    {mochow::vindex::IndexType::INDEX_TINKER, 4},
    {mochow::vindex::IndexType::INDEX_PUCKPQ, 5},
    {mochow::vindex::IndexType::INDEX_HNSWPQ, 6},
    {mochow::vindex::IndexType::INDEX_HNSWSQ, 7}
    /*
    more index type
    */
};

void init_hnsw_index_param(
        mochow::vindex::VectorIndexParam& index_param,
        const nlohmann::json& conf) {
    index_param.index_type = vindex::IndexType::INDEX_HNSW;
    index_param.metric_type = vindex::MetricType::L2;
    mochow::vindex::HNSWParam hnsw_param;
    if (conf.contains("M")) {
        hnsw_param.M = conf["M"];
    } else {
        hnsw_param.M = 32;
    }
    if (conf.contains("efConstruction")) {
        hnsw_param.efConstruction = conf["efConstruction"];
    } else {
        hnsw_param.efConstruction = 200;
    }
    index_param.hnsw_param = std::make_optional(hnsw_param);
}

void init_puck_index_param(
        mochow::vindex::VectorIndexParam& index_param,
        const nlohmann::json& conf) {
    index_param.index_type = vindex::IndexType::INDEX_PUCK;
    index_param.metric_type = vindex::MetricType::L2;
    mochow::vindex::PuckParam puck_param;
    if (conf.contains("coarse_cluster_count")) {
        puck_param.coarse_cluster_count = conf["coarse_cluster_count"];
    } else {
        puck_param.coarse_cluster_count = 2000;
    }
    if (conf.contains("fine_cluster_count")) {
        puck_param.fine_cluster_count = conf["fine_cluster_count"];
    } else {
        puck_param.fine_cluster_count = 2000;
    }
    index_param.puck_param = std::make_optional(puck_param);
}

void init_flat_index_param(
        mochow::vindex::VectorIndexParam& index_param,
        const nlohmann::json& conf) {}
void init_diskann_index_param(
        mochow::vindex::VectorIndexParam& index_param,
        const nlohmann::json& conf) {}
void init_tinker_index_param(
        mochow::vindex::VectorIndexParam& index_param,
        const nlohmann::json& conf) {}
void init_puckpq_index_param(
        mochow::vindex::VectorIndexParam& index_param,
        const nlohmann::json& conf) {
    index_param.index_type = vindex::IndexType::INDEX_PUCKPQ;
    index_param.metric_type = vindex::MetricType::L2;
    mochow::vindex::PuckPQParam puckpq_param;
    if (conf.contains("coarse_cluster_count")) {
        puckpq_param.coarse_cluster_count = conf["coarse_cluster_count"];
    } else {
        puckpq_param.coarse_cluster_count = 2000;
    }
    if (conf.contains("fine_cluster_count")) {
        puckpq_param.fine_cluster_count = conf["fine_cluster_count"];
    } else {
        puckpq_param.fine_cluster_count = 2000;
    }
    if (conf.contains("sample_rate")) {
        puckpq_param.sample_rate = conf["sample_rate"];
    } else {
        puckpq_param.sample_rate = 1;
    }

    index_param.puckpq_param = std::make_optional(puckpq_param);
}

void init_hnswpq_index_param(
        mochow::vindex::VectorIndexParam& index_param,
        const nlohmann::json& conf) {
    index_param.index_type = vindex::IndexType::INDEX_HNSWPQ;
    index_param.metric_type = vindex::MetricType::L2;
    mochow::vindex::HNSWPQParam hnswpq_param;
    if (conf.contains("M")) {
        hnswpq_param.M = conf["M"];
    } else {
        hnswpq_param.M = 32;
    }
    if (conf.contains("efConstruction")) {
        hnswpq_param.efConstruction = conf["efConstruction"];
    } else {
        hnswpq_param.efConstruction = 200;
    }
    if (conf.contains("NSQ")) {
        hnswpq_param.NSQ = conf["NSQ"];
    } else {
        hnswpq_param.NSQ = 1;
    }
    if (conf.contains("sample_rate")) {
        hnswpq_param.sample_rate = conf["sample_rate"];
    } else {
        hnswpq_param.sample_rate = 1;
    }

    index_param.hnswpq_param = std::make_optional(hnswpq_param);
}

void init_hnswsq_index_param(
        mochow::vindex::VectorIndexParam& index_param,
        const nlohmann::json& conf) {
    index_param.index_type = vindex::IndexType::INDEX_HNSWSQ;
    index_param.metric_type = vindex::MetricType::L2;
    mochow::vindex::HNSWSQParam hnswsq_param;
    if (conf.contains("M")) {
        hnswsq_param.M = conf["M"];
    } else {
        hnswsq_param.M = 32;
    }
    if (conf.contains("efConstruction")) {
        hnswsq_param.efConstruction = conf["efConstruction"];
    } else {
        hnswsq_param.efConstruction = 200;
    }
    if (conf.contains("qtBits")) {
        hnswsq_param.qtBits = conf["qtBits"];
    } else {
        hnswsq_param.qtBits = 8;
    }

    index_param.hnswsq_param = std::make_optional(hnswsq_param);
}

typedef void (*init_index_param_func_ptr)(mochow::vindex::VectorIndexParam&, const nlohmann::json&);

init_index_param_func_ptr init_index_param[] = {
    mochow::benchmark::init_hnsw_index_param,
    mochow::benchmark::init_flat_index_param, 
    mochow::benchmark::init_diskann_index_param, 
    mochow::benchmark::init_puck_index_param,
    mochow::benchmark::init_tinker_index_param,
    mochow::benchmark::init_puckpq_index_param,
    mochow::benchmark::init_hnswpq_index_param,
    mochow::benchmark::init_hnswsq_index_param
};

class Benchmark : public BenchmarkHDF5 {
public:
    Benchmark() {
        _column_id_allocator = std::make_shared<schema::IdAllocator>();
        _index_id_allocator = std::make_shared<schema::IdAllocator>();
    }
    void generate_schema(const nlohmann::json& conf, const char* index_type_from_argv) {
        // generate external schema
        DBID db_id = 1;
        std::string db_name = "db_test";
        TBLID table_id = 1;
        std::string table_name = "perf";

        schema::SchemaBuilder* builder = new schema::SchemaBuilder(
                schema::SchemaLayer::EXTERNAL, _column_id_allocator, _index_id_allocator);
        uint64_t schema_version = 1;
        /******************************
         *
         * |-Row key-------------|-------External Column --------|
         * |-----------------------------------------------------|
         * | CF(id=1)            | CF(id=2)                      |
         * | id(id=1,type=UINT64)|vector(id=4, type=FLOAT_VECTOR)|
         * |-----------------------------------------------------|
         *
         *****************************/
        builder->initialize_schema(schema_version, db_id, db_name, table_id, table_name);
        auto cf_id = builder->next_cf_id();
        LOG_AND_ASSERT(cf_id == schema::DEFAULT_SCALAR_CF_ID);
        auto ok = builder->add_scalar_column_family(cf_id, schema::DEFAULT_SCALAR_CF_NAME);
        LOG_AND_ASSERT(ok);
        ok = builder->add_primary_key(builder->next_column_id(), "id", 
                DataType::UINT64, SortType::ASCEND, true, true);
        LOG_AND_ASSERT(ok);
        cf_id = builder->next_cf_id();
        ok = builder->add_external_column(cf_id, "vector", builder->next_column_id(), 
                "vector", DataType::FLOAT_VECTOR, false, false, _dim);
        LOG_AND_ASSERT(ok);
        vindex::VectorIndexParam index_param;
        init_index_param[index_map[index_type_from_argv]](index_param, conf);
        ok = builder->add_vector_index(builder->next_index_id(), "vector_index", 
                "vector", index_param);
        LOG_AND_ASSERT(ok);
        auto external_schema = builder->move_and_get_schema();
        delete builder;
        LOG(NOTICE) << "external schema:" << external_schema->to_string();
        // generate schema cache
        //::mochow::datanode::g_schema_cache = new schema::SchemaCache();
        auto schema_hierarchy = ::mochow::datanode::g_schema_cache->generate_schema_hierarchy_from_external_schema(
                external_schema, _column_id_allocator, _index_id_allocator);
        ok = ::mochow::datanode::g_schema_cache->add_schema_hierarchy(schema_hierarchy);
        LOG_AND_ASSERT(ok);
        auto vector_schema = ::mochow::datanode::g_schema_cache->get_vector_schema(table_id);
        LOG(NOTICE) << "vector schema:" << vector_schema->to_string();
        LOG(NOTICE) << "auto increment keys:" << vector_schema->get_auto_increment_keys().size();
    }

    void generate_dataset(std::vector<zq::RowRef>& ds) {
        LOG(NOTICE) << "generate dataset nb:" << _nb << " dim:" << _dim;
        for (int32_t i = 0; i < _nb; ++i) {
            auto row = generate_row(i);
            if (i == 0) {
                LOG(NOTICE) << "row:" << row->to_string();
            }
            ds.push_back(row);
        }
    }

    zq::RowRef generate_row(uint32_t row_count) {
        common::RowGenerator row_generator(::mochow::datanode::g_schema_cache);
        TBLID table_id = 1;
        auto vector_schema = ::mochow::datanode::g_schema_cache->get_vector_schema(table_id);
        LOG_AND_ASSERT(vector_schema != nullptr);
        std::vector<zq::Field> fields;
        for (auto & [_, pk] : vector_schema->get_primary_keys()) {
            auto data_type = pk->get_data_type();
            auto row_key = make_increment_key(1, row_count);
            Variant variant;
            variant.set_value(row_key);
            zq::Field field(pk->get_id(), variant);
            fields.push_back(field);
        }
        for (auto & [_, sk] : vector_schema->get_system_keys()) {
            if (sk->get_name() == schema::OPTYPE_COLUMN_NAME) {
                Variant variant;
                variant.set_value((uint8_t)zq::OpType::UPSERT);
                zq::Field field(sk->get_id(), variant);
                fields.push_back(field);
            } else {
                auto data_type = sk->get_data_type();
                zq::Field field(sk->get_id(), row_generator.generate_data(data_type));
                fields.push_back(field);
            }
        }
        zq::AllKeyRef allkey = std::make_shared<zq::AllKey>(fields);
        fields.clear();
        std::map<CFID, std::vector<zq::Field>> column_families_map;
        // generate float vecotr
        for (auto & [colid, column_schema] : vector_schema->get_external_columns()) {
            auto data_type = column_schema->get_data_type();
            LOG_AND_ASSERT(data_type == DataType::FLOAT_VECTOR);
            float* data_pointer = (float*)_xb + _dim * row_count;
            PointerAndLength data = {data_pointer, (uint64_t)_dim << 2};
            Variant variant;
            variant.set_value(data, DataType::FLOAT_VECTOR);
            zq::Field field(colid, variant);
            if (column_families_map.count(column_schema->get_cf_id())) {
                column_families_map[column_schema->get_cf_id()].push_back(field);
            } else {
                column_families_map.emplace(column_schema->get_cf_id(), std::vector<zq::Field>{field});
            }
        }
        // generate vid
        for (auto & [colid, column_schema] : vector_schema->get_internal_columns()) {
            Variant variant;
            //INCID incid = common::random_uint32();
            INCID incid = row_count;
            variant.set_value(incid);
            zq::Field field = zq::Field(colid, variant);
            if (column_families_map.count(column_schema->get_cf_id())) {
                column_families_map[column_schema->get_cf_id()].push_back(field);
            } else {
                column_families_map.emplace(column_schema->get_cf_id(), std::vector<zq::Field>{field});
            }
        }
        std::vector<zq::ColumnFamilyRef> column_families;
        for (auto & [cf_id, fields] : column_families_map) {
            zq::ColumnFamilyRef cf = std::make_shared<zq::ColumnFamily>(cf_id, true, fields);
            column_families.push_back(cf);
        }

        zq::RowRef row = std::make_shared<zq::Row>(allkey, column_families);
        return row;
    }

    void test_delta_layer_recall(moss::MossRef& moss, const nlohmann::json& conf) {
        
        auto k = conf[vindex::IndexMeta::TOPK].get<int64_t>();
        
        moss::VectorSearchParam search_params;
        search_params.topk = k;
        if (_index_type == vindex::IndexType::INDEX_HNSW) {
            moss::HNSWSearchParam hnsw_search_params;
            if (conf.contains("ef")) {
                hnsw_search_params.ef = conf["ef"];
            } else {
                hnsw_search_params.ef = 200;
            }
            search_params.hnsw_params = hnsw_search_params;
        } else {
            LOG(ERROR) << "unsupported index type:" << _index_type;
            return;
        }
        
        int32_t search_cnt = 100;
        search_cnt = std::min(search_cnt, _nq);
        LOG(NOTICE) << "[" << get_time_diff() << " s]" << _ann_test_name
            << " | " << _index_type << " | search_cnt=" << search_cnt 
            << ", k=" << k;
        LOG(NOTICE) << "--------------------------------------------------------------------------------";
        
        auto schema_hierarchy = ::mochow::datanode::g_schema_cache->get_schema_hierarchy(1);
        auto moss_schema = schema_hierarchy->vector_schema;
        auto vector_indexes = moss_schema->get_vector_indexes();
        auto index_id = vector_indexes[0]->get_index_id();
        common::BitSetFilterRef bitset_filter{nullptr};
        float recall = 0;
        for(int i = 0; i < search_cnt; ++i) {
            float loop_recall = 0;
            VariantArray search_vectors;
            float* data_pointer = (float*)_xq + _dim * i;
            PointerAndLength data = {data_pointer, (uint64_t)_dim << 2};
            Variant variant;
            variant.set_value(data, DataType::FLOAT_VECTOR);
            search_vectors.push_back(variant);
            std::vector<zq::VectorSearchResultVector> results;

            zq::FilterRef filter = std::make_shared<zq::Filter>(zq::pass_through_filter());
            Status st;
            CALC_TIME_SPAN(st = moss->search(schema_hierarchy, 
                           index_id, search_vectors, 
                           search_params, filter, bitset_filter, false, true, zq::TimeoutInfo(10000), &results));

            LOG_AND_ASSERT(st.ok());
            std::vector<uint64_t> incids;
            for (auto& result : results) {
                for (int i = 0; i < k ; ++i) {
                    auto row = result[i].get_row();
                    auto row_key = row->get_allkey();
                    uint64_t key;
                    auto ok = row_key->get_fields()[0].get_variant().get_value(key);
                    LOG_AND_ASSERT(ok);
                    incids.push_back(incid(key));
                }
            }
            loop_recall = calc_recall(incids, i, k);
            recall += loop_recall;
            std::stringstream ss;
            ss << "[";
            for (auto incid : incids) {
                ss << incid << ",";
            }
            ss << "]";
            LOG(NOTICE) << "recall:" << loop_recall
                << " result:" << ss.str();
        }

        recall = recall / search_cnt;

        LOG(NOTICE) << "search cnt=" << search_cnt
            << ", k=" << k  << ", recall=" << recall;
        LOG(NOTICE) << "--------------------------------------------------------------------------------";
    }
    
    void test_recall(moss::MossRef& moss, const nlohmann::json& conf) {
        
        auto k = conf[vindex::IndexMeta::TOPK].get<int64_t>();
        
        moss::VectorSearchParam search_params;
        search_params.topk = k;
        if (_index_type == vindex::IndexType::INDEX_HNSW) {
            moss::HNSWSearchParam hnsw_search_params;
            if (conf.contains("ef")) {
                hnsw_search_params.ef = conf["ef"];
            } else {
                hnsw_search_params.ef = 200;
            }
            search_params.hnsw_params = hnsw_search_params;
        } else if (_index_type == vindex::IndexType::INDEX_PUCK) {
            moss::PuckSearchParam puck_search_params;

            if (conf.contains("search_coarse_count")) {
                puck_search_params.search_coarse_count = conf["search_coarse_count"];
            } else {
                puck_search_params.search_coarse_count = 200;
            }
            search_params.puck_params = std::make_optional(puck_search_params);
        } else if (_index_type == vindex::IndexType::INDEX_PUCKPQ) {
            moss::PuckPQSearchParam puckpq_search_params;

            if (conf.contains("search_coarse_count")) {
                puckpq_search_params.search_coarse_count = conf["search_coarse_count"];
            } else {
                puckpq_search_params.search_coarse_count = 200;
            }
            search_params.puckpq_params = std::make_optional(puckpq_search_params);
        } else if (_index_type == vindex::IndexType::INDEX_HNSWPQ) {
            moss::HNSWPQSearchParam hnswpq_search_params;

            if (conf.contains("ef")) {
                hnswpq_search_params.ef = conf["ef"];
            } else {
                hnswpq_search_params.ef = 200;
            }
            search_params.hnswpq_params = std::make_optional(hnswpq_search_params);
        } else if (_index_type == vindex::IndexType::INDEX_HNSWSQ) {
            moss::HNSWSQSearchParam hnswsq_search_params;

            if (conf.contains("ef")) {
                hnswsq_search_params.ef = conf["ef"];
            } else {
                hnswsq_search_params.ef = 200;
            }
            search_params.hnswsq_params = std::make_optional(hnswsq_search_params);
        } else {
            LOG(ERROR) << "unsupported index type:" << _index_type;
            return;
        }

        int32_t search_cnt = 100;
        search_cnt = std::min(search_cnt, _nq);
        LOG(NOTICE) << "[" << get_time_diff() << " s]" << _ann_test_name
            << " | " << _index_type << " | search_cnt=" << search_cnt 
            << ", k=" << k;
        LOG(NOTICE) << "--------------------------------------------------------------------------------";
        
        float recall = 0.0f;
        for(int i = 0; i < search_cnt; ++i) {
            float loop_recall = 0;
            VariantArray search_vectors;
            float* data_pointer = (float*)_xq + _dim * i;
            PointerAndLength data = {data_pointer, (uint64_t)_dim << 2};
            Variant variant;
            variant.set_value(data, DataType::FLOAT_VECTOR);
            search_vectors.push_back(variant);
            std::vector<zq::VectorSearchResultVector> results;
            IDXVERSION index_id = 1;
            auto vindex = moss->get_vector_index(index_id);
            auto schema_hierarchy = ::mochow::datanode::g_schema_cache->get_schema_hierarchy(1);
            zq::FilterRef filter = std::make_shared<zq::Filter>(zq::pass_through_filter());
            CALC_TIME_SPAN(auto st = moss->search(
                               schema_hierarchy, index_id, search_vectors,
                               search_params, filter, nullptr, false, true, zq::TimeoutInfo(10000), &results));
            
            LOG_AND_ASSERT(st.ok()); 
            std::vector<uint64_t> incids;
            std::vector<float> dists;
            for (auto& result : results) {
                for (int i = 0; i < k ; ++i) {
                    auto row = result[i].get_row();
                    auto row_key = row->get_allkey();
                    uint64_t key; 
                    auto ok = row_key->get_fields()[0].get_variant().get_value(key);
                    LOG_AND_ASSERT(ok);
                    incids.push_back(incid(key));
                    dists.push_back(std::sqrt(result[i].get_distance()));
                }
            }

            loop_recall = calc_recall(incids, i, k);
            recall += loop_recall;
            if (loop_recall < 1) {
                std::stringstream ss;
                ss << "[";
                for (auto incid : incids) {
                    ss << incid << ",";
                }
                ss << "]";

                std::stringstream dss;
                dss << "[";
                for (auto& dist : dists) {
                    dss << dist << ",";
                }
                dss << "]";

                LOG(NOTICE) << "recall:" << loop_recall;
                LOG(NOTICE) << "result:" << ss.str();
                LOG(NOTICE) << "dist:" << dss.str();
            }
        }

        recall = recall / search_cnt;

        LOG(NOTICE) << "search cnt=" << search_cnt
            << ", k=" << k  << ", recall=" << recall;
        LOG(NOTICE) << "--------------------------------------------------------------------------------";
    }

    void test_range_recall(moss::MossRef& moss, const nlohmann::json& conf) {
        auto k = conf[vindex::IndexMeta::TOPK].get<int64_t>();
        float distance_far = 10000000.0;
        float distance_near = 0.0;

        moss::VectorSearchParam search_params;
        search_params.topk = k;
        search_params.distance_far = distance_far;
        search_params.distance_near = distance_near;

        if (_index_type == vindex::IndexType::INDEX_HNSW) {
            moss::HNSWSearchParam hnsw_search_params;
            if (conf.contains("ef")) {
                hnsw_search_params.ef = conf["ef"];
            } else {
                hnsw_search_params.ef = 200;
            }
            search_params.hnsw_params = hnsw_search_params;
        } else if (_index_type == vindex::IndexType::INDEX_HNSWPQ) {
            moss::HNSWPQSearchParam hnswpq_search_params;
            if (conf.contains("ef")) {
                hnswpq_search_params.ef = conf["ef"];
            } else {
                hnswpq_search_params.ef = 200;
            }
            search_params.hnswpq_params = hnswpq_search_params;
        } else if (_index_type == vindex::IndexType::INDEX_HNSWSQ) {
            moss::HNSWSQSearchParam hnswsq_search_params;
            if (conf.contains("ef")) {
                hnswsq_search_params.ef = conf["ef"];
            } else {
                hnswsq_search_params.ef = 200;
            }
            search_params.hnswsq_params = hnswsq_search_params;
        } else {
            LOG(ERROR) << "unsupported index type:" << _index_type;
            return;
        }

        // range search is slow. set search count to 50 in case benchmark is too slow.
        int32_t search_cnt = 50;
        search_cnt = std::min(search_cnt, _nq);
        LOG(NOTICE) << "[" << get_time_diff() << " s]" << _ann_test_name
            << " | " << _index_type << " | search_cnt=" << search_cnt 
            << ", k=" << k;
        LOG(NOTICE) << "--------------------------------------------------------------------------------";
        
        float recall = 0;
        for(int i = 0; i < search_cnt; ++i) {
            float loop_recall = 0;

            // get search_vector
            VariantArray search_vectors;
            float* data_pointer = (float*)_xq + _dim * i;
            PointerAndLength data = {data_pointer, (uint64_t)_dim << 2};
            Variant variant;
            variant.set_value(data, DataType::FLOAT_VECTOR);
            search_vectors.push_back(variant);

            // update distance_far in search_param
            // distance_far = _gt_dist[i * _gt_k + k] + 1e-2;
            auto loop_search_param = search_params;
            // loop_search_param.distance_far = distance_far;

            std::vector<zq::VectorSearchResultVector> results;
            IDXVERSION index_id = 1;
            auto vindex = moss->get_vector_index(index_id);
            auto schema_hierarchy = ::mochow::datanode::g_schema_cache->get_schema_hierarchy(1);
            zq::FilterRef filter = std::make_shared<zq::Filter>(zq::pass_through_filter());
            CALC_TIME_SPAN(auto st = moss->search(
                               schema_hierarchy, index_id, search_vectors,
                               search_params, filter, nullptr, false, true, zq::TimeoutInfo(10000), &results));
            LOG_AND_ASSERT(st.ok());
            std::vector<uint64_t> incids;
            for (auto& result : results) {
                for (int i = 0; i < k ; ++i) {
                    auto row = result[i].get_row();
                    if (row == nullptr) {
                        incids.push_back(INVALID_INCID);
                    } else {
                        auto row_key = row->get_allkey();
                        uint64_t key; 
                        auto ok = row_key->get_fields()[0].get_variant().get_value(key);
                        LOG_AND_ASSERT(ok);
                        incids.push_back(incid(key));
                    }
                }
            }
            loop_recall = calc_recall(incids, i, k);
            recall += loop_recall;
            if (loop_recall < 1) {
                std::stringstream ss;
                ss << "[";
                for (auto incid : incids) {
                    ss << incid << ",";
                }
                ss << "]";
                LOG(NOTICE) << "recall:" << loop_recall
                    << " result:" << ss.str();
            }
        }

        recall = recall / search_cnt;

        LOG(NOTICE) << "search cnt=" << search_cnt
            << ", k=" << k  << ", recall=" << recall;
        LOG(NOTICE) << "--------------------------------------------------------------------------------";
    }
    
    void task(moss::MossRef& moss, const nlohmann::json& conf, int32_t worker_num, 
            int32_t total) {

        auto k = conf[vindex::IndexMeta::TOPK].get<int64_t>();

        moss::VectorSearchParam search_params;
        search_params.topk = k;
        if (_index_type == vindex::IndexType::INDEX_HNSW) {
            moss::HNSWSearchParam hnsw_search_params;
            if (conf.contains("ef")) {
                hnsw_search_params.ef = conf["ef"];
            } else {
                hnsw_search_params.ef = 200;
            }
            search_params.hnsw_params = hnsw_search_params;
        } else if (_index_type == vindex::IndexType::INDEX_PUCK) {
            moss::PuckSearchParam puck_search_params;
            if (conf.contains("search_coarse_count")) {
                puck_search_params.search_coarse_count = conf["search_coarse_count"];
            } else {
                puck_search_params.search_coarse_count = 200;
            }
            search_params.puck_params = puck_search_params;
        } else if (_index_type == vindex::IndexType::INDEX_PUCKPQ) {
            moss::PuckPQSearchParam puckpq_search_params;

            if (conf.contains("search_coarse_count")) {
                puckpq_search_params.search_coarse_count = conf["search_coarse_count"];
            } else {
                puckpq_search_params.search_coarse_count = 200;
            }
            search_params.puckpq_params = std::make_optional(puckpq_search_params);
        } else if (_index_type == vindex::IndexType::INDEX_HNSWPQ) {
            moss::HNSWPQSearchParam hnswpq_search_params;
            if (conf.contains("ef")) {
                hnswpq_search_params.ef = conf["ef"];
            } else {
                hnswpq_search_params.ef = 200;
            }
            search_params.hnswpq_params = hnswpq_search_params;
        } else if (_index_type == vindex::IndexType::INDEX_HNSWSQ) {
            moss::HNSWSQSearchParam hnswsq_search_params;
            if (conf.contains("ef")) {
                hnswsq_search_params.ef = conf["ef"];
            } else {
                hnswsq_search_params.ef = 200;
            }
            search_params.hnswsq_params = hnswsq_search_params;
        } else {
            LOG(ERROR) << "unsupported index type:" << _index_type;
            return;
        }

        auto worker = [&](int32_t idx_start, int32_t num) {
            //num = std::min(num, total - idx_start);
            IDXID index_id = 1;
            auto vindex = moss->get_vector_index(index_id);
            int32_t index = 0;
            for (int32_t i = 0; i < num; i++) {
                if ((idx_start + i) >= _nq) {
                    index = (idx_start + i) % _nq;
                } else {
                    index = idx_start + i;
                }
                float* data_pointer = (float*)_xq + index * _dim;
                PointerAndLength data = {data_pointer, (uint64_t)_dim << 2};
                Variant variant;
                variant.set_value(data, DataType::FLOAT_VECTOR);
                VariantArray search_vectors;
                search_vectors.push_back(variant);
                std::vector<zq::VectorSearchResultVector> results;
                auto schema_hierarchy = ::mochow::datanode::g_schema_cache->get_schema_hierarchy(1);

                zq::FilterRef filter = std::make_shared<zq::Filter>(zq::pass_through_filter());		
                auto st = moss->search(schema_hierarchy, index_id, search_vectors, search_params, filter, nullptr,
                                       false, true, zq::TimeoutInfo(10000), &results);
                LOG_AND_ASSERT(st.ok());
            }
        };

        std::vector<std::thread> thread_vector(worker_num);
        int32_t req_num = total / worker_num;
        int32_t remainder = total % worker_num;
        for (int32_t i = 0; i < worker_num; i++) {
            if (remainder != 0) {
                req_num++;
                remainder--;
            }
            int32_t idx_start = (_nq / worker_num) * i;
            LOG(NOTICE) << "start worker to seach from:" << idx_start
                << " req_num:" << req_num;
            thread_vector[i] = std::thread(worker, idx_start, req_num);
        }
        for (int32_t i = 0; i < worker_num; i++) {
            thread_vector[i].join();
        }
    }

    void test_qps(moss::MossRef& moss, const nlohmann::json& conf, int thread_num) {
        auto k = conf[vindex::IndexMeta::TOPK].get<int64_t>();
        LOG(NOTICE) << "[" << get_time_diff() << " s]" << _ann_test_name
            << " | " << _index_type << " | nq=" << _nq
            << ", k=" << k;
        LOG(NOTICE) << "--------------------------------------------------------------------------------";
        CALC_TIME_SPAN(task(moss, conf, thread_num, _search_cnt));

        LOG(NOTICE) << "thread_num =" << thread_num << ", elapse =" << t_diff
            << "s, QPS =" << _search_cnt / t_diff;
        LOG(NOTICE) << "--------------------------------------------------------------------------------";
    }

    void test_latency(const nlohmann::json& conf, int thread_num) {
        /*
        auto k = conf[vdb::meta::TOPK].get<int64_t>();

        printf("\n[%0.3f s] %s | %s | nq=%d, k=%d\n", get_time_diff(),
                _ann_test_name.c_str(), _index_type.c_str(), _nq, k);
        printf("--------------------------------------------------------------------------------\n");
        std::fflush(stdout);

        std::vector<double> latency_vec{};
        latency_vec.resize(_nq);
        calc_latency_task(conf, thread_num, _nq, latency_vec);
        std::sort(latency_vec.begin(), latency_vec.end()); // small to large by default
        double avg_latency = latency_vec[(int)(_nq * 0.50)] * 1000;
        double p90_latency = latency_vec[(int)(_nq * 0.90)] * 1000;
        double p99_latency = latency_vec[(int)(_nq * 0.99)] * 1000;

        printf("  thread_num = %2d, AVG = %6.3fms, P90 = %6.3fms, P99 = %6.3fms\n",
                thread_num, avg_latency, p90_latency, p99_latency);
        printf("--------------------------------------------------------------------------------\n");
        std::fflush(stdout);
        */
    }

public:
    void test_process(nlohmann::json& conf, int thread_num, const std::string& step, const char* index_type_from_argv) {
        if (faiss::use_sse4_2 && faiss::cpu_support_sse4_2()) {
            LOG(NOTICE) << "===============";
            LOG(NOTICE) << "SSE4_2 used.";
            LOG(NOTICE) << "===============";
        } 
        if (faiss::use_avx2 && faiss::cpu_support_avx2()) {
            LOG(NOTICE) << "===============";
            LOG(NOTICE) << "AVX2 used.";
            LOG(NOTICE) << "===============";
        } 
        if (faiss::use_avx512 && faiss::cpu_support_avx512()) {
            LOG(NOTICE) << "===============";
            LOG(NOTICE) << "AVX512F used.";
            LOG(NOTICE) << "===============";
        }
#ifdef __SSE__
        LOG(NOTICE) << "_______SSE________";
#endif

        LOG(NOTICE) << "[" << get_time_diff() << " s] " <<  _ann_test_name
            << " | " << _index_type << " | Starting...";
        
        { // generate schema
            LOG(NOTICE) << "================================================================================";
            double t_start = elapsed();
            generate_schema(conf, index_type_from_argv);
            double t_diff = elapsed() - t_start;
            LOG(NOTICE) << "[" << get_time_diff() << " s] " << _ann_test_name
                << " | " << _index_type << " | Schema generated. elapse ="
                << t_diff;
            LOG(NOTICE) << "================================================================================";
        }
        std::vector<zq::RowRef> dataset;
        { // generate dataset
            int mem_start = get_used_mem_kb();
            double t_start = elapsed();
            generate_dataset(dataset);
            double t_diff = elapsed() - t_start;
            int mem_diff = get_used_mem_kb() - mem_start;
            LOG(NOTICE) << "[" << get_time_diff() << " s] " << _ann_test_name
                << " | " << _index_type << " | Dataset generated. elapse ="
                << t_diff << " mem cost=" << mem_diff << "KB";
            LOG(NOTICE) << "================================================================================";
        }
        // init io
        {
            baidu::sfl::PosixFileSystemOption option("posix_file_system",
                    std::thread::hardware_concurrency() * 2);
            auto ok = io::initialize_posix_file_system(std::move(option));
            LOG_AND_ASSERT(baidu::sfl::is_succeeded(ok));
            auto sstable_config = std::make_unique<sstable::SSTableConfig>();
            sstable_config->set_cache_size(0);
            ok = sstable::initialize_sstable_library(std::move(sstable_config), nullptr);
            LOG_AND_ASSERT(baidu::sfl::is_succeeded(ok));
        }

        TBLID table_id = 1;
        
        // init moss
        moss::MossOptionsRef options = std::make_shared<moss::MossOptions>();
        options->segment_dir="./";
        auto tablet_id = make_tablet_id(table_id, 1);
        options->tablet_id = tablet_id;
        options->segment_id = 1;
        // options->max_sstable_size = 10 * baidu::sfl::GIBI; // 1G
        options->max_sstable_size = 64 * baidu::sfl::MEBI; // 64M
        auto schema_hierarchy = ::mochow::datanode::g_schema_cache->get_schema_hierarchy(1);
        moss::MossRef moss = std::make_shared<moss::Moss>(options);
        auto init_st = moss->initialize();

        { // insert dataset
            int mem_start = get_used_mem_kb();
            double t_start = elapsed();
            int row_cnt = 0;
            for (auto& row : dataset) {
                auto st = moss->Insert(schema_hierarchy, row);
                LOG_AND_ASSERT(st.ok());
                if (row_cnt % 10000 == 0) {
                    LOG(NOTICE) << "insert 1w row";
                }
                row_cnt++;
            }
            double t_diff = elapsed() - t_start;
            int mem_diff = get_used_mem_kb() - mem_start;
            LOG(NOTICE) << "[" << get_time_diff() << " s] " << _ann_test_name
                << " | " << _index_type << " | Dataset inserted into moss. elapse ="
                << t_diff << "s mem cost=" << mem_diff << "KB"
                << " insert qps=" << _nb / t_diff;
                bthread_usleep(10 * 1000 * 1000);
            LOG(NOTICE) << "================================================================================";
        }
        if ((step == "") || (step == TEST_INCREMENTAL_RECALL)) {
            int mem_start = get_used_mem_kb();
            CALC_TIME_SPAN(test_delta_layer_recall(moss, conf));
            int mem_diff = get_used_mem_kb() - mem_start;
            LOG(NOTICE) << "[" << get_time_diff() << " s] " << _ann_test_name
                << " | " << _index_type << " | Test incremental recall finished. elapse ="
                << t_diff << "s mem cost=" << mem_diff << "KB";
            LOG(NOTICE) << "================================================================================";
        }
        if ((step == "") || (step == TEST_RECALL) || (step == TEST_RANGE)
                || (step == TEST_QPS)) {
            // build vector index
            int mem_start = get_used_mem_kb();
            double t_start = elapsed();
            auto moss_schema = ::mochow::datanode::g_schema_cache->get_vector_schema(table_id);
            auto vindexes_schema = moss_schema->get_vector_indexes();
            
            auto vindex_schema = vindexes_schema[0];
            auto index_id = vindex_schema->get_index_id();
            IDXVERSION index_version = 1;
            auto st = moss->build_vector_index(schema_hierarchy, index_id, index_version);

            double t_diff = elapsed() - t_start;
            int mem_diff = get_used_mem_kb() - mem_start;
            LOG(NOTICE) << "[" << get_time_diff() << " s] " << _ann_test_name
                << " | " << _index_type << " | Vector index builded. elapse ="
                << t_diff << "s mem cost=" << mem_diff << "KB";
            LOG(NOTICE) << "================================================================================";
        }
        if ((step == "") || (step == TEST_RECALL)) {
            int mem_start = get_used_mem_kb();
            CALC_TIME_SPAN(test_recall(moss, conf));
            
            int mem_diff = get_used_mem_kb() - mem_start;
            LOG(NOTICE) << "[" << get_time_diff() << " s] " << _ann_test_name
                << " | " << _index_type << " | Test recall finished. elapse ="
                << t_diff << "s mem cost=" << mem_diff << "KB";
            LOG(NOTICE) << "================================================================================";
        }
        if ((step == "") || (step == TEST_RANGE)) {
            int mem_start = get_used_mem_kb();
            CALC_TIME_SPAN(test_range_recall(moss, conf));
            int mem_diff = get_used_mem_kb() - mem_start;
            LOG(NOTICE) << "[" << get_time_diff() << " s] " << _ann_test_name
                << " | " << _index_type << " | Test range recall finished. elapse ="
                << t_diff << "s mem cost=" << mem_diff << "KB";
            LOG(NOTICE) << "================================================================================";
        }
        if ((step == "") || (step == TEST_QPS)) {
            int mem_start = get_used_mem_kb();
            CALC_TIME_SPAN(test_qps(moss, conf, thread_num));
            int mem_diff = get_used_mem_kb() - mem_start;
            LOG(NOTICE) << "[" << get_time_diff() << " s] " << _ann_test_name
                << " | " << _index_type << " | Test qps finished. "
                << "elapse ="
                << t_diff << "s mem cost=" << mem_diff << "KB"
                << " search qps=" << _search_cnt / t_diff
                << " latency=" << t_diff * 1000 / (_search_cnt  / thread_num) << "ms";
            LOG(NOTICE) << "================================================================================";
        }

        /*
        if ((step == "") || (step == TEST_LATENCY)) {
            CALC_TIME_SPAN(test_latency(conf, thread_num));

            printf("\n[%0.3f s] %s | %s | Test latency finished. elapse = %6.3fs\n",
        get_time_diff(), _ann_test_name.c_str(), _index_type.c_str(), t_diff);
            printf("================================================================================\n");
            std::fflush(stdout);
        }
        */

        printf("[%.3f s] Test '%s/%s' done\n\n", get_time_diff(), _ann_test_name.c_str(),
        _index_type.c_str()); std::fflush(stdout);
    }

    int test(int argc, char** argv) {
        // start test
        _T0 = elapsed();

        // args
        std::string dataset_path_prefix = argv[1];
        std::string dataset_name = argv[2];
        std::string conf_file = argv[3];
        std::string index_type = to_upper(argv[4]);
        int thread_num = 16;
        if (argc >= 6) {
            thread_num = std::stoi(argv[5]);
        }
        LOG(NOTICE) << "thread num:" << thread_num;
        std::string step_name = "";
        if (argc >= 7) {
            step_name = to_upper(argv[6]);
        }

        // dataset init
        int dataset_index = -1;
        for (size_t i = 0; i < hdf5_datasets.size(); i++) {
            if (to_upper(hdf5_datasets[i].dataset_name) == to_upper(dataset_name)) {
                dataset_index = i;
            }
        }
        if (dataset_index == -1) {
            LOG(NOTICE) << "Invalid dataset name " << dataset_name;
            return 1;
        }
        set_ann_test_prefix(dataset_path_prefix.c_str());
        // load hdf5 file
        apply_benchmark(hdf5_datasets[dataset_index]);

        // index type init
        if (index_type == vindex::IndexType::INDEX_HNSW) {
             _index_type = index_type;
        } else if (index_type == vindex::IndexType::INDEX_PUCK) {
             _index_type = index_type;
        } else if (index_type == vindex::IndexType::INDEX_PUCKPQ) {
             _index_type = index_type;
        } else if (index_type == vindex::IndexType::INDEX_HNSWPQ) {
             _index_type = index_type;
        } else if (index_type == vindex::IndexType::INDEX_HNSWSQ) {
             _index_type = index_type;
        } else {
             LOG(NOTICE) << std::string("Invalid index type ") << index_type;
             return -1;
        }

        // step init
        if ((step_name == "") ||
            (step_name == TEST_RECALL) ||
            (step_name == TEST_QPS) ||
            (step_name == TEST_LATENCY) ||
            (step_name == TEST_RANGE) ||
            (step_name == TEST_INCREMENTAL_RECALL)) {
            // do nothing
        } else {
            LOG(NOTICE) << std::string("Invalid benchmark step ") << step_name;
            return -1;
        }

        // conf init
        std::ifstream file(conf_file, std::ios::in);
        std::stringstream buffer;
        buffer << file.rdbuf();
        std::string cfg_str = buffer.str();
        nlohmann::json cfg = nlohmann::json::parse(cfg_str);

        cfg[vindex::IndexMeta::DIM] = _dim;
        cfg[vindex::IndexMeta::METRIC_TYPE] = _metric_str;
        LOG(NOTICE) << "confg:" << cfg;
        // test body
        test_process(cfg, thread_num, step_name, argv[4]);
        // finish test
        //free_all();
        //std::this_thread::sleep_for(std::chrono::seconds(10));
        return 0;
    }
private:
    std::string _index_type;
    uint64_t _search_cnt = 10000;
    schema::IdAllocatorRef _column_id_allocator;
    schema::IdAllocatorRef _index_id_allocator;
};

}

void helper() {
    LOG(NOTICE) << "argv[0] = execution path";
    LOG(NOTICE) << "argv[1] = {dataset_path_prefix} e.g. ./test_dataset/";
    LOG(NOTICE) << "argv[2] = {hdf5_name} e.g. Fashion-MNIST";
    LOG(NOTICE) << "argv[3] = {conf_file} e.g. ./test_dataset/hnsw.cfg";
    LOG(NOTICE) << "argv[4] = index_type e.g. HNSW";
    LOG(NOTICE) << "argv[5](optional) = search_thread_num e.g. 8";
    LOG(NOTICE) << "argv[6](optional) = step_name, e.g. recall";
}

void init_common_log() {
std::string path_str = "/tmp/.log.conf";
    try {
        std::ofstream ofs(path_str);
        ofs << "[comlog]" << std::endl;
        ofs << "level: " << 4 << std::endl;
        ofs << "procname: " << "perf" << std::endl;
        ofs << "time_format: %Y-%m-%dT%H:%M:%S" << std::endl;
        ofs << std::endl;
        ofs.close();
    } catch (const std::exception& error) {
        LOG(FATAL) << "load com_log config failed, error:" << error.what();
        return;
    }

    comcfg::Configure conf;
    base::FilePath path(path_str);
    int ret =conf.load(path.DirName().value().c_str(), path.BaseName().value().c_str());
    assert(ret == 0);

    ret = comlog_init(conf["comlog"]);
    assert(ret == 0);
    ::unlink(path_str.c_str());
}

// argv[0] = execution path
// argv[1] = {dataset_path_prefix} e.g. ./test_dataset/
// argv[2] = {hdf5_name} e.g. Fashion-MNIST
// argv[3] = {conf_file} e.g. ./test_dataset/hnsw.cfg
// argv[4] = index_type e.g. HNSW
// argv[5] = search_thread_num e.g. 8
// argv[6](optional) = step_name, e.g. recall
int main(int argc, char** argv) {
    init_common_log();
    
    if (google::SetCommandLineOption("bvar_dump", "true").empty()) {
        LOG(FATAL) << "Fail to enable bvar dump";
    }

    mochow::benchmark::Benchmark benchmark;
    if (argc < 5 || argc > 7) {
        helper();
        return -1;
    }
    return benchmark.test(argc, argv);
}
