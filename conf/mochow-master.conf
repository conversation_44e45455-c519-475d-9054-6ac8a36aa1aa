# Configure for mochow-master in the format with gflags

# local port
--port=8288

# master address: support BNS & List & DNS
#  BNS  - address string should start with 'bns://',
#         if bns instance does not configure port, port will be set as which parsed from address string.
#  List - address string should start with 'list://', and addresses are seperated by ','.
#  DNS  - otherwise.
--master_address=*************:8288

# token 
--token=default_token
--admin_token=default_token
--bvar_dump_interval=60

# raft related
--raft_dir=local://./data/
--snapshot_file_name=snapshot.data

# running settings
--auto_add_datanode_in_heartbeat=false

# commlog configure
# log level definition: 0-TRACE 1-NOTICE 2-WARNING 3-ERROR 4-FATAL
--min_log_level=1
--comlog_enable_async=false
--comlog_enable_wf=true
--comlog_path=log
--comlog_split_type=1
--comlog_quota_size=2048
--comlog_quota_day=0
--comlog_quota_hour=0

############################### The following flags are reloadable ################################

# rpc timeouts
--call_timeout_ms=30000
--connect_timeout_ms=3000
--retry_interval_ms=3000

# raft related
--raft_election_timeout_ms=10000

# scheduler related
--snapshot_interval_s=86400
--monitor_scheduler_check_interval_s=30
--heartbeat_scheduler_check_interval_s=30
--balance_scheduler_check_interval_s=30
--index_scheduler_check_interval_s=10
--node_picker_refresh_interval_s=30
--scheduler_delay_start_s=300
--datanode_dead_timeout_s=180
--deadnode_auto_drop_timeout_s=86400
--proxy_dead_timeout_s=180
--tablet_scheduler_scan_all_interval_s=86400
--master_metric_refresh_interval_s=5

# balance related
--leader_balance_distance_threshold=10
--max_tranfer_leader_balance_per_cycle=100
--balance_task_timeout_s=7200
--max_running_balance_task_per_dest_node=5
--max_running_balance_task_per_src_node=20
--balance_table_replica_num_diff=0.0
--balance_disk_usage_diff=0.2
--balance_memory_usage_diff=0.2
--enable_table_balance=true
--enable_usage_balance=true
--enable_leader_balance=true

# others
--cluster_safemode_threshold=5
