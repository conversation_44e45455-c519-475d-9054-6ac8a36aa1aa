# Configure for mochow-standalone in the format with gflags

# local port
--port=8287

# master address: support BNS & List & DNS
#  BNS  - address string should start with 'bns://',
#        if bns instance does not configure port, port will be set as which parsed from address string.
#  List - address string should start with 'list://', and addresses are seperated by ','.
#  DNS  - otherwise.
--master_address=*************:8288

# token 
--token=default_token
--admin_token=default_token
--bvar_dump_interval=60

# data related
--datanode_data_dir=./data

# commlog configure
# log level definition: 0-TRACE 1-NOTICE 2-WARNING 3-ERROR 4-FATAL
--min_log_level=1
--comlog_enable_async=false
--comlog_enable_wf=true
--comlog_path=log
--comlog_split_type=1
--comlog_quota_size=2048
--comlog_quota_day=0
--comlog_quota_hour=0

############################### The following flags are reloadable ################################

# rpc timeouts
--call_timeout_ms=30000
--connect_timeout_ms=3000
--retry_interval_ms=3000

# raft related
--raft_election_timeout_ms=10000

# scheduler related
--heartbeat_interval_s=10

# ssl
--use_https=false
--allow_connection_without_certificates=true
