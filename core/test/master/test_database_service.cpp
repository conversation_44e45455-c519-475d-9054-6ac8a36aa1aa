/***************************************************************************
 *
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

#include "core/test/master/ut_common.h"
#include "core/src/common/index_auto_build_policy_type.h"

namespace mochow::master {

std::atomic<int> g_return_code = OK;
std::atomic<bool> g_is_leader = true;
base::EndPoint g_leader_endpoint = base::EndPoint();

using common::MasterStub;

class MockMasterControl : public MasterControl {
public:
    virtual base::EndPoint get_raft_leader() {
        return g_leader_endpoint;
    }

    virtual bool is_leader() {
        return g_is_leader;
    }

    virtual void list_raft_peers(std::vector<base::EndPoint>* peers) {
        peers->push_back(get_node1_addr());
        peers->push_back(get_node2_addr());
    }

    virtual void create_database(baidu::rpc::Controller* cntl,
            const pb::CreateDatabaseRequest* request,
            pb::AckResponse* response) {
        response->mutable_status()->set_code(g_return_code);
    }

    virtual void drop_database(baidu::rpc::Controller* cntl,
            const pb::DropDatabaseRequest* request,
            pb::AckResponse* response) {
        response->mutable_status()->set_code(g_return_code);
    }

    virtual void update_database(baidu::rpc::Controller* cntl,
            const pb::UpdateDatabaseRequest* request,
            pb::AckResponse* response) {
        response->mutable_status()->set_code(g_return_code);
    }

    virtual void create_table(baidu::rpc::Controller* cntl,
            const pb::CreateTableRequest* request,
            pb::AckResponse* response) {
        response->mutable_status()->set_code(g_return_code);
    }

    virtual void drop_table(baidu::rpc::Controller* cntl,
            const pb::DropTableRequest* request,
            pb::AckResponse* response) {
        response->mutable_status()->set_code(g_return_code);
    }

    virtual void update_table(baidu::rpc::Controller* cntl,
            const pb::UpdateTableRequest* request,
            pb::AckResponse* response) {
        response->mutable_status()->set_code(g_return_code);
    }

    virtual void add_column(baidu::rpc::Controller* cntl,
            const pb::AddColumnRequest* request,
            pb::AckResponse* response) {
        response->mutable_status()->set_code(g_return_code);
    }

    virtual void create_index(baidu::rpc::Controller* cntl,
            const pb::CreateIndexRequest* request,
            pb::AckResponse* response) {
        response->mutable_status()->set_code(g_return_code);
    }

    virtual void drop_index(baidu::rpc::Controller* cntl,
            const pb::DropIndexRequest* request,
            pb::AckResponse* response) {
        response->mutable_status()->set_code(g_return_code);
    }

    virtual void modify_index(baidu::rpc::Controller* cntl,
            const pb::ModifyIndexRequest* request,
            pb::AckResponse* response) {
        response->mutable_status()->set_code(g_return_code);
    }

    virtual void rebuild_index(baidu::rpc::Controller* cntl,
            const pb::RebuildIndexRequest* request,
            pb::AckResponse* response) {
        response->mutable_status()->set_code(g_return_code);
    }

    virtual void alias_table(baidu::rpc::Controller* cntl,
            const pb::AliasTableRequest* request,
            pb::AckResponse* response) {
        response->mutable_status()->set_code(g_return_code);
    }

    virtual void unalias_table(baidu::rpc::Controller* cntl,
            const pb::UnaliasTableRequest* request,
            pb::AckResponse* response) {
        response->mutable_status()->set_code(g_return_code);
    }

    virtual void create_user(baidu::rpc::Controller* cntl,
            const pb::CreateUserRequest* request,
            pb::AckResponse* response) {
        response->mutable_status()->set_code(g_return_code);
    }

    virtual void drop_user(baidu::rpc::Controller* cntl,
            const pb::DropUserRequest* request,
            pb::AckResponse* response) {
        response->mutable_status()->set_code(g_return_code);
    }

    virtual void change_password(baidu::rpc::Controller* cntl,
            const pb::ChangePasswordRequest* request,
            pb::AckResponse* response) {
        response->mutable_status()->set_code(g_return_code);
    }

    virtual void create_role(baidu::rpc::Controller* cntl,
            const pb::CreateRoleRequest* request,
            pb::AckResponse* response) {
        response->mutable_status()->set_code(g_return_code);
    }

    virtual void drop_role(baidu::rpc::Controller* cntl,
            const pb::DropRoleRequest* request,
            pb::AckResponse* response) {
        response->mutable_status()->set_code(g_return_code);
    }

    virtual void grant_user_roles(baidu::rpc::Controller* cntl,
            const pb::GrantUserRolesRequest* request,
            pb::AckResponse* response) {
        response->mutable_status()->set_code(g_return_code);
    }

    virtual void revoke_user_roles(baidu::rpc::Controller* cntl,
            const pb::RevokeUserRolesRequest* request,
            pb::AckResponse* response) {
        response->mutable_status()->set_code(g_return_code);
    }

    virtual void grant_user_privileges(baidu::rpc::Controller* cntl,
            const pb::GrantUserPrivilegesRequest* request,
            pb::AckResponse* response) {
        response->mutable_status()->set_code(g_return_code);
    }

    virtual void revoke_user_privileges(baidu::rpc::Controller* cntl,
            const pb::RevokeUserPrivilegesRequest* request,
            pb::AckResponse* response) {
        response->mutable_status()->set_code(g_return_code);
    }

    virtual void grant_role_privileges(baidu::rpc::Controller* cntl,
            const pb::GrantRolePrivilegesRequest* request,
            pb::AckResponse* response) {
        response->mutable_status()->set_code(g_return_code);
    }

    virtual void revoke_role_privileges(baidu::rpc::Controller* cntl,
            const pb::RevokeRolePrivilegesRequest* request,
            pb::AckResponse* response) {
        response->mutable_status()->set_code(g_return_code);
    }
};

class DatabaseServiceTests : public::testing::Test {
protected:
    DatabaseServiceTests() {}
    virtual ~DatabaseServiceTests() {}

    void SetUp() {
        g_master_control.reset(new MockMasterControl);
        prepare_mock_data();

        auto ret = _server.AddService(new MasterDatabaseServiceImpl,
            baidu::rpc::SERVER_OWNS_SERVICE);
        ASSERT_EQ(0, ret);
        baidu::rpc::ServerOptions options;
        options.idle_timeout_sec = 10;
        ASSERT_EQ(0, _server.Start(mock_master_port, &options));
        _rpc_options.need_retry = false;
    }

    void TearDown() {
        _server.Stop(100);
        _server.Join();
        g_metadata->clear();
    }
private:
    common::RpcCallOptions _rpc_options;
    baidu::rpc::Server _server;
};

TEST_F(DatabaseServiceTests, create_database) {
    MasterStub stub;
    // not leader
    {
        g_is_leader = false;
        pb::CreateDatabaseRequest request;
        pb::AckResponse response;
        fill_auth(request);

        common::SynchronizedClosure rpc_waiter;
        stub.create_database(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_NOT_PRIMARY);
        g_is_leader = true;
    }
    // authenticate failed
    {
        pb::CreateDatabaseRequest request;
        pb::AckResponse response;
        fill_error_auth(request);

        common::SynchronizedClosure rpc_waiter;
        stub.create_database(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_AUTHENTICATE_FAILED);
    }
    // privilege denied
    {
        pb::CreateDatabaseRequest request;
        pb::AckResponse response;
        request.mutable_auth_info()->set_username("hello1");
        request.mutable_auth_info()->set_password("world1");
        request.mutable_db_info()->set_db_name("*");

        common::SynchronizedClosure rpc_waiter;
        stub.create_database(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_PRIVILEGE_DENIED);
    }
    // invalid db name
    {
        pb::CreateDatabaseRequest request;
        pb::AckResponse response;
        fill_auth(request);

        common::SynchronizedClosure rpc_waiter;
        stub.create_database(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_INVALID_DB_NAME);
    }
    // db already exist
    {
        pb::CreateDatabaseRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.mutable_db_info()->set_db_name("db1");

        common::SynchronizedClosure rpc_waiter;
        stub.create_database(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_DB_ALREADY_EXIST);
    }
    // succ
    {
        pb::CreateDatabaseRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.mutable_db_info()->set_db_name("newdb");

        common::SynchronizedClosure rpc_waiter;
        stub.create_database(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), OK);
    }
}

TEST_F(DatabaseServiceTests, drop_database) {
    MasterStub stub;
    // not leader
    {
        g_is_leader = false;
        pb::DropDatabaseRequest request;
        pb::AckResponse response;
        fill_auth(request);

        common::SynchronizedClosure rpc_waiter;
        stub.drop_database(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_NOT_PRIMARY);
        g_is_leader = true;
    }
    // authenticate failed
    {
        pb::DropDatabaseRequest request;
        pb::AckResponse response;
        fill_error_auth(request);

        common::SynchronizedClosure rpc_waiter;
        stub.drop_database(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_AUTHENTICATE_FAILED);
    }
    // privilege denied
    {
        pb::DropDatabaseRequest request;
        pb::AckResponse response;
        request.mutable_auth_info()->set_username("hello1");
        request.mutable_auth_info()->set_password("world1");
        request.set_db_name("*");

        common::SynchronizedClosure rpc_waiter;
        stub.drop_database(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_PRIVILEGE_DENIED);
    }
    // succ
    {
        pb::DropDatabaseRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_db_name("db1");

        common::SynchronizedClosure rpc_waiter;
        stub.drop_database(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), OK);
    }
}

TEST_F(DatabaseServiceTests, update_database) {
    MasterStub stub;
    // not leader
    {
        g_is_leader = false;
        pb::UpdateDatabaseRequest request;
        pb::AckResponse response;
        fill_auth(request);

        common::SynchronizedClosure rpc_waiter;
        stub.update_database(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_NOT_PRIMARY);
        g_is_leader = true;
    }
    // authenticate failed
    {
        pb::UpdateDatabaseRequest request;
        pb::AckResponse response;
        fill_error_auth(request);

        common::SynchronizedClosure rpc_waiter;
        stub.update_database(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_AUTHENTICATE_FAILED);
    }
    // privilege denied
    {
        pb::UpdateDatabaseRequest request;
        pb::AckResponse response;
        request.mutable_auth_info()->set_username("hello1");
        request.mutable_auth_info()->set_password("world1");
        request.set_db_name("*");

        common::SynchronizedClosure rpc_waiter;
        stub.update_database(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_PRIVILEGE_DENIED);
    }
    // succ
    {
        pb::UpdateDatabaseRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_db_name("db1");

        common::SynchronizedClosure rpc_waiter;
        stub.update_database(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), OK);
    }
}

TEST_F(DatabaseServiceTests, show_database) {
    MasterStub stub;
    // not leader
    {
        g_is_leader = false;
        pb::ShowDatabaseRequest request;
        pb::ShowDatabaseResponse response;
        fill_auth(request);

        common::SynchronizedClosure rpc_waiter;
        stub.show_database(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_NOT_PRIMARY);
        g_is_leader = true;
    }
    // authenticate failed
    {
        pb::ShowDatabaseRequest request;
        pb::ShowDatabaseResponse response;
        fill_error_auth(request);

        common::SynchronizedClosure rpc_waiter;
        stub.show_database(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_AUTHENTICATE_FAILED);
    }
    // privilege denied
    {
        pb::ShowDatabaseRequest request;
        pb::ShowDatabaseResponse response;
        request.mutable_auth_info()->set_username("hello1");
        request.mutable_auth_info()->set_password("world1");
        request.set_db_name("*");

        common::SynchronizedClosure rpc_waiter;
        stub.show_database(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_PRIVILEGE_DENIED);
    }
    // not exist
    {
        pb::ShowDatabaseRequest request;
        pb::ShowDatabaseResponse response;
        fill_auth(request);
        request.set_db_name("not_exist");

        common::SynchronizedClosure rpc_waiter;
        stub.show_database(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_DB_NOT_EXIST);
    }
    // succ
    {
        pb::ShowDatabaseRequest request;
        pb::ShowDatabaseResponse response;
        fill_auth(request);
        request.set_db_name("db1");

        common::SynchronizedClosure rpc_waiter;
        stub.show_database(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), OK);
    }
}

TEST_F(DatabaseServiceTests, list_database) {
    MasterStub stub;
    // not leader
    {
        g_is_leader = false;
        pb::ListDatabaseRequest request;
        pb::ListDatabaseResponse response;
        fill_auth(request);

        common::SynchronizedClosure rpc_waiter;
        stub.list_database(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_NOT_PRIMARY);
        g_is_leader = true;
    }
    // authenticate failed
    {
        pb::ListDatabaseRequest request;
        pb::ListDatabaseResponse response;
        fill_error_auth(request);

        common::SynchronizedClosure rpc_waiter;
        stub.list_database(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_AUTHENTICATE_FAILED);
    }
    // privilege denied
    {
        pb::ListDatabaseRequest request;
        pb::ListDatabaseResponse response;
        request.mutable_auth_info()->set_username("hello1");
        request.mutable_auth_info()->set_password("world1");

        common::SynchronizedClosure rpc_waiter;
        stub.list_database(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), OK);
        EXPECT_EQ(0, response.db_infos_size());
    }
    // succ
    {
        pb::ListDatabaseRequest request;
        pb::ListDatabaseResponse response;
        fill_auth(request);

        common::SynchronizedClosure rpc_waiter;
        stub.list_database(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), OK);
        EXPECT_TRUE(response.db_infos_size() > 0);
    }
}

TEST_F(DatabaseServiceTests, create_table) {
    MasterStub stub;
    // leader
    {
        g_is_leader = false;
        pb::CreateTableRequest request;
        pb::AckResponse response;
        fill_auth(request);

        common::SynchronizedClosure rpc_waiter;
        stub.create_table(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_NOT_PRIMARY);
        g_is_leader = true;
    }
    // authenticate failed
    {
        pb::CreateTableRequest request;
        pb::AckResponse response;
        fill_error_auth(request);

        common::SynchronizedClosure rpc_waiter;
        stub.create_table(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_AUTHENTICATE_FAILED);
    }
    // privilege denied
    {
        pb::CreateTableRequest request;
        pb::AckResponse response;
        request.mutable_auth_info()->set_username("hello1");
        request.mutable_auth_info()->set_password("world1");
        request.mutable_table_info()->mutable_schema()->set_db_name("db1");
        request.mutable_table_info()->mutable_schema()->set_table_name("newtable");

        common::SynchronizedClosure rpc_waiter;
        stub.create_table(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_PRIVILEGE_DENIED);
    }
    // invalid name
    {
        pb::CreateTableRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.mutable_table_info()->mutable_schema()->set_db_name("db1");
        request.mutable_table_info()->mutable_schema()->set_table_name("? ?");

        common::SynchronizedClosure rpc_waiter;
        stub.create_table(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_INVALID_TABLE_NAME);
    }
    // replication
    {
        pb::CreateTableRequest request;
        pb::AckResponse response;
        fill_auth(request);
        auto table_info = request.mutable_table_info();
        table_info->mutable_schema()->set_db_name("db1");
        table_info->mutable_schema()->set_table_name("newtable");
        table_info->set_replication(0);

        common::SynchronizedClosure rpc_waiter;
        stub.create_table(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_INVALID_ARGUMENT);
    }
    // hash type
    {
        pb::CreateTableRequest request;
        pb::AckResponse response;
        fill_auth(request);
        auto table_info = request.mutable_table_info();
        table_info->mutable_schema()->set_db_name("db1");
        table_info->mutable_schema()->set_table_name("newtable");
        table_info->set_replication(1);
        table_info->mutable_partition()->set_type("ERROR");

        common::SynchronizedClosure rpc_waiter;
        stub.create_table(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_INVALID_ARGUMENT);
    }
    // auto build false
    {
        pb::CreateTableRequest request;
        pb::AckResponse response;
        fill_auth(request);
        auto table_info = request.mutable_table_info();
        table_info->mutable_schema()->set_db_name("not_exist");
        table_info->mutable_schema()->set_table_name("newtable");
        table_info->set_replication(1);
        table_info->mutable_partition()->set_type("HASH");
        table_info->mutable_partition()->set_hash_buckets(10);
        auto vector_index = table_info->add_vector_indexes();
        vector_index->set_is_auto_build(false);

        common::SynchronizedClosure rpc_waiter;
        stub.create_table(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_DB_NOT_EXIST);
    }
    // no auto build param
    {
        pb::CreateTableRequest request;
        pb::AckResponse response;
        fill_auth(request);
        auto table_info = request.mutable_table_info();
        table_info->mutable_schema()->set_db_name("db1");
        table_info->mutable_schema()->set_table_name("newtable");
        table_info->set_replication(1);
        table_info->mutable_partition()->set_type("HASH");
        table_info->mutable_partition()->set_hash_buckets(10);
        auto vector_index = table_info->add_vector_indexes();
        vector_index->set_is_auto_build(true);

        common::SynchronizedClosure rpc_waiter;
        stub.create_table(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_INVALID_ARGUMENT);
    }
    // no auto build policy type
    {
        pb::CreateTableRequest request;
        pb::AckResponse response;
        fill_auth(request);
        auto table_info = request.mutable_table_info();
        table_info->mutable_schema()->set_db_name("db1");
        table_info->mutable_schema()->set_table_name("newtable");
        table_info->set_replication(1);
        table_info->mutable_partition()->set_type("HASH");
        table_info->mutable_partition()->set_hash_buckets(10);
        auto vector_index = table_info->add_vector_indexes();
        vector_index->set_is_auto_build(true);
        vector_index->mutable_auto_build_index_param()->mutable_policy_param();

        common::SynchronizedClosure rpc_waiter;
        stub.create_table(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_INVALID_ARGUMENT);
    }
    // no auto build policy param 
    {
        pb::CreateTableRequest request;
        pb::AckResponse response;
        fill_auth(request);
        auto table_info = request.mutable_table_info();
        table_info->mutable_schema()->set_db_name("db1");
        table_info->mutable_schema()->set_table_name("newtable");
        table_info->set_replication(1);
        table_info->mutable_partition()->set_type("HASH");
        table_info->mutable_partition()->set_hash_buckets(10);
        auto vector_index = table_info->add_vector_indexes();
        vector_index->set_is_auto_build(true);
        vector_index->mutable_auto_build_index_param()->set_policy_type(common::IndexAutoBuildPolicyType::PERIODICAL);

        common::SynchronizedClosure rpc_waiter;
        stub.create_table(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_INVALID_ARGUMENT);
    }
    // no timing param
    {
        pb::CreateTableRequest request;
        pb::AckResponse response;
        fill_auth(request);
        auto table_info = request.mutable_table_info();
        table_info->mutable_schema()->set_db_name("db1");
        table_info->mutable_schema()->set_table_name("newtable");
        table_info->set_replication(1);
        table_info->mutable_partition()->set_type("HASH");
        table_info->mutable_partition()->set_hash_buckets(10);
        auto vector_index = table_info->add_vector_indexes();
        vector_index->set_is_auto_build(true);
        vector_index->mutable_auto_build_index_param()->set_policy_type(common::IndexAutoBuildPolicyType::TIMING);
        vector_index->mutable_auto_build_index_param()->mutable_policy_param()->set_period_s(1);

        common::SynchronizedClosure rpc_waiter;
        stub.create_table(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_INVALID_ARGUMENT);
    }
    // invalid timing param
    {
        pb::CreateTableRequest request;
        pb::AckResponse response;
        fill_auth(request);
        auto table_info = request.mutable_table_info();
        table_info->mutable_schema()->set_db_name("db1");
        table_info->mutable_schema()->set_table_name("newtable");
        table_info->set_replication(1);
        table_info->mutable_partition()->set_type("HASH");
        table_info->mutable_partition()->set_hash_buckets(10);
        auto vector_index = table_info->add_vector_indexes();
        vector_index->set_is_auto_build(true);
        vector_index->mutable_auto_build_index_param()->set_policy_type(common::IndexAutoBuildPolicyType::TIMING);
        vector_index->mutable_auto_build_index_param()->mutable_policy_param()->set_timing("");

        common::SynchronizedClosure rpc_waiter;
        stub.create_table(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_INVALID_ARGUMENT);
    }
    // invalid timing param
    {
        pb::CreateTableRequest request;
        pb::AckResponse response;
        fill_auth(request);
        auto table_info = request.mutable_table_info();
        table_info->mutable_schema()->set_db_name("db1");
        table_info->mutable_schema()->set_table_name("newtable");
        table_info->set_replication(1);
        table_info->mutable_partition()->set_type("HASH");
        table_info->mutable_partition()->set_hash_buckets(10);
        auto vector_index = table_info->add_vector_indexes();
        vector_index->set_is_auto_build(true);
        vector_index->mutable_auto_build_index_param()->set_policy_type(common::IndexAutoBuildPolicyType::TIMING);
        vector_index->mutable_auto_build_index_param()->mutable_policy_param()->set_timing("xxyy");

        common::SynchronizedClosure rpc_waiter;
        stub.create_table(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_INVALID_ARGUMENT);
    }
    // no period_s param
    {
        pb::CreateTableRequest request;
        pb::AckResponse response;
        fill_auth(request);
        auto table_info = request.mutable_table_info();
        table_info->mutable_schema()->set_db_name("db1");
        table_info->mutable_schema()->set_table_name("newtable");
        table_info->set_replication(1);
        table_info->mutable_partition()->set_type("HASH");
        table_info->mutable_partition()->set_hash_buckets(10);
        auto vector_index = table_info->add_vector_indexes();
        vector_index->set_is_auto_build(true);
        vector_index->mutable_auto_build_index_param()->set_policy_type(common::IndexAutoBuildPolicyType::PERIODICAL);
        vector_index->mutable_auto_build_index_param()->mutable_policy_param()->set_row_count_increment(1);

        common::SynchronizedClosure rpc_waiter;
        stub.create_table(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_INVALID_ARGUMENT);
    }
    // invalid period_s param
    {
        pb::CreateTableRequest request;
        pb::AckResponse response;
        fill_auth(request);
        auto table_info = request.mutable_table_info();
        table_info->mutable_schema()->set_db_name("db1");
        table_info->mutable_schema()->set_table_name("newtable");
        table_info->set_replication(1);
        table_info->mutable_partition()->set_type("HASH");
        table_info->mutable_partition()->set_hash_buckets(10);
        auto vector_index = table_info->add_vector_indexes();
        vector_index->set_is_auto_build(true);
        vector_index->mutable_auto_build_index_param()->set_policy_type(common::IndexAutoBuildPolicyType::PERIODICAL);
        vector_index->mutable_auto_build_index_param()->mutable_policy_param()->set_period_s(0);

        common::SynchronizedClosure rpc_waiter;
        stub.create_table(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_INVALID_ARGUMENT);
    }
    // invalid period_s param
    {
        pb::CreateTableRequest request;
        pb::AckResponse response;
        fill_auth(request);
        auto table_info = request.mutable_table_info();
        table_info->mutable_schema()->set_db_name("db1");
        table_info->mutable_schema()->set_table_name("newtable");
        table_info->set_replication(1);
        table_info->mutable_partition()->set_type("HASH");
        table_info->mutable_partition()->set_hash_buckets(10);
        auto vector_index = table_info->add_vector_indexes();
        vector_index->set_is_auto_build(true);
        vector_index->mutable_auto_build_index_param()->set_policy_type(common::IndexAutoBuildPolicyType::PERIODICAL);
        vector_index->mutable_auto_build_index_param()->mutable_policy_param()->set_period_s(FLAGS_auto_build_index_min_period_s - 1);

        common::SynchronizedClosure rpc_waiter;
        stub.create_table(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_INVALID_ARGUMENT);
    }
    // no row_count_increment param
    {
        pb::CreateTableRequest request;
        pb::AckResponse response;
        fill_auth(request);
        auto table_info = request.mutable_table_info();
        table_info->mutable_schema()->set_db_name("db1");
        table_info->mutable_schema()->set_table_name("newtable");
        table_info->set_replication(1);
        table_info->mutable_partition()->set_type("HASH");
        table_info->mutable_partition()->set_hash_buckets(10);
        auto vector_index = table_info->add_vector_indexes();
        vector_index->set_is_auto_build(true);
        vector_index->mutable_auto_build_index_param()->set_policy_type(common::IndexAutoBuildPolicyType::ROW_COUNT_INCREMENT);
        vector_index->mutable_auto_build_index_param()->mutable_policy_param()->set_timing("2024-02-29 00:00:00");

        common::SynchronizedClosure rpc_waiter;
        stub.create_table(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_INVALID_ARGUMENT);
    }
    // invalid row_count_increment param
    {
        pb::CreateTableRequest request;
        pb::AckResponse response;
        fill_auth(request);
        auto table_info = request.mutable_table_info();
        table_info->mutable_schema()->set_db_name("db1");
        table_info->mutable_schema()->set_table_name("newtable");
        table_info->set_replication(1);
        table_info->mutable_partition()->set_type("HASH");
        table_info->mutable_partition()->set_hash_buckets(10);
        auto vector_index = table_info->add_vector_indexes();
        vector_index->set_is_auto_build(true);
        vector_index->mutable_auto_build_index_param()->set_policy_type(common::IndexAutoBuildPolicyType::ROW_COUNT_INCREMENT);
        vector_index->mutable_auto_build_index_param()->mutable_policy_param()->set_row_count_increment(0);

        common::SynchronizedClosure rpc_waiter;
        stub.create_table(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_INVALID_ARGUMENT);
    }
    // invalid row_count param
    {
        pb::CreateTableRequest request;
        pb::AckResponse response;
        fill_auth(request);
        auto table_info = request.mutable_table_info();
        table_info->mutable_schema()->set_db_name("db1");
        table_info->mutable_schema()->set_table_name("newtable");
        table_info->set_replication(1);
        table_info->mutable_partition()->set_type("HASH");
        table_info->mutable_partition()->set_hash_buckets(10);
        auto vector_index = table_info->add_vector_indexes();
        vector_index->set_is_auto_build(true);
        vector_index->mutable_auto_build_index_param()->set_policy_type(common::IndexAutoBuildPolicyType::ROW_COUNT_INCREMENT);
        vector_index->mutable_auto_build_index_param()->mutable_policy_param()->set_row_count_increment(FLAGS_auto_build_index_min_row_count_increment - 1);

        common::SynchronizedClosure rpc_waiter;
        stub.create_table(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_INVALID_ARGUMENT);
    }
    // invalid policy type
    {
        pb::CreateTableRequest request;
        pb::AckResponse response;
        fill_auth(request);
        auto table_info = request.mutable_table_info();
        table_info->mutable_schema()->set_db_name("db1");
        table_info->mutable_schema()->set_table_name("newtable");
        table_info->set_replication(1);
        table_info->mutable_partition()->set_type("HASH");
        table_info->mutable_partition()->set_hash_buckets(10);
        auto vector_index = table_info->add_vector_indexes();
        vector_index->set_is_auto_build(true);
        vector_index->mutable_auto_build_index_param()->set_policy_type("invalid policy type");
        vector_index->mutable_auto_build_index_param()->mutable_policy_param()->set_timing("2024-02-29 00:00:00");

        common::SynchronizedClosure rpc_waiter;
        stub.create_table(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_INVALID_ARGUMENT);
    }
    // db not exist
    {
        pb::CreateTableRequest request;
        pb::AckResponse response;
        fill_auth(request);
        auto table_info = request.mutable_table_info();
        table_info->mutable_schema()->set_db_name("not_exist");
        table_info->mutable_schema()->set_table_name("newtable");
        table_info->set_replication(1);
        table_info->mutable_partition()->set_type("HASH");
        table_info->mutable_partition()->set_hash_buckets(10);

        common::SynchronizedClosure rpc_waiter;
        stub.create_table(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_DB_NOT_EXIST);
    }
    // table exist
    {
        pb::CreateTableRequest request;
        pb::AckResponse response;
        fill_auth(request);
        auto table_info = request.mutable_table_info();
        table_info->mutable_schema()->set_db_name("db1");
        table_info->mutable_schema()->set_table_name("table1");
        table_info->set_replication(1);
        table_info->mutable_partition()->set_type("HASH");
        table_info->mutable_partition()->set_hash_buckets(10);

        common::SynchronizedClosure rpc_waiter;
        stub.create_table(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_TABLE_ALREADY_EXIST);
    }
    // schema
    {
        pb::CreateTableRequest request;
        pb::AckResponse response;
        fill_auth(request);
        auto table_info = request.mutable_table_info();
        table_info->mutable_schema()->set_db_name("db1");
        table_info->mutable_schema()->set_table_name("newtable");
        table_info->set_replication(1);
        table_info->mutable_partition()->set_type("HASH");
        table_info->mutable_partition()->set_hash_buckets(10);

        common::SynchronizedClosure rpc_waiter;
        stub.create_table(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_INVALID_TABLE_SCHEMA);
    }
    // succ
    {
        pb::CreateTableRequest request;
        pb::AckResponse response;
        fill_auth(request);
        auto table_info = request.mutable_table_info();
        table_info->mutable_schema()->set_db_name("db1");
        table_info->mutable_schema()->set_table_name("newtable");
        table_info->set_replication(1);
        table_info->mutable_partition()->set_type("HASH");
        table_info->mutable_partition()->set_hash_buckets(10);
        auto pk = table_info->mutable_schema()->add_primary_keys();
        pk->set_column_name("primary");
        pk->set_data_type("UINT32");
        pk->set_sort_type("ASCEND");
        pk->set_is_partition_key(true);
        pk->set_is_auto_increment(false);
        pk->set_is_not_null(true);
        auto scf = table_info->mutable_schema()->add_column_families();
        scf->set_is_vector_cf(false);
        auto col1 = scf->add_columns();
        col1->set_column_name("col1");
        col1->set_data_type("DOUBLE");
        col1->set_is_not_null(false);
        col1->set_is_partition_key(false);

        common::SynchronizedClosure rpc_waiter;
        stub.create_table(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), OK);
    }

    // succ with TTL;
    {
        pb::CreateTableRequest request;
        pb::AckResponse response;
        fill_auth(request);
        auto table_info = request.mutable_table_info();
        table_info->mutable_schema()->set_db_name("db1");
        table_info->mutable_schema()->set_table_name("newtable_1");
        table_info->set_replication(1);
        table_info->mutable_partition()->set_type("HASH");
        table_info->mutable_partition()->set_hash_buckets(10);
        auto pk = table_info->mutable_schema()->add_primary_keys();
        pk->set_column_name("primary");
        pk->set_data_type("UINT32");
        pk->set_sort_type("ASCEND");
        pk->set_is_partition_key(true);
        pk->set_is_auto_increment(false);
        pk->set_is_not_null(true);
        auto scf = table_info->mutable_schema()->add_column_families();
        scf->set_is_vector_cf(false);
        auto col1 = scf->add_columns();
        col1->set_column_name("col1");
        col1->set_data_type("DOUBLE");
        col1->set_is_not_null(false);
        col1->set_is_partition_key(false);
        table_info->set_ttl(60);
        common::SynchronizedClosure rpc_waiter;
        stub.create_table(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), OK);
    }
}

TEST_F(DatabaseServiceTests, drop_table) {
    MasterStub stub;
    // not leader
    {
        g_is_leader = false;
        pb::DropTableRequest request;
        pb::AckResponse response;
        fill_auth(request);

        common::SynchronizedClosure rpc_waiter;
        stub.drop_table(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_NOT_PRIMARY);
        g_is_leader = true;
    }
    // authenticate failed
    {
        pb::DropTableRequest request;
        pb::AckResponse response;
        fill_error_auth(request);

        common::SynchronizedClosure rpc_waiter;
        stub.drop_table(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_AUTHENTICATE_FAILED);
    }
    // privilege denied
    {
        pb::DropTableRequest request;
        pb::AckResponse response;
        request.mutable_auth_info()->set_username("hello1");
        request.mutable_auth_info()->set_password("world1");
        request.set_db_name("db1");
        request.set_table_name("table1");

        common::SynchronizedClosure rpc_waiter;
        stub.drop_table(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_PRIVILEGE_DENIED);
    }
    // succ
    {
        pb::DropTableRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_db_name("db1");
        request.set_table_name("table1");

        common::SynchronizedClosure rpc_waiter;
        stub.drop_table(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), OK);
    }
}

TEST_F(DatabaseServiceTests, update_table) {
    MasterStub stub;
    // not leader
    {
        g_is_leader = false;
        pb::UpdateTableRequest request;
        pb::AckResponse response;
        fill_auth(request);

        common::SynchronizedClosure rpc_waiter;
        stub.update_table(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_NOT_PRIMARY);
        g_is_leader = true;
    }
    // authenticate failed
    {
        pb::UpdateTableRequest request;
        pb::AckResponse response;
        fill_error_auth(request);

        common::SynchronizedClosure rpc_waiter;
        stub.update_table(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_AUTHENTICATE_FAILED);
    }
    // privilege denied
    {
        pb::UpdateTableRequest request;
        pb::AckResponse response;
        request.mutable_auth_info()->set_username("hello1");
        request.mutable_auth_info()->set_password("world1");
        request.set_db_name("db1");
        request.set_table_name("table1");

        common::SynchronizedClosure rpc_waiter;
        stub.update_table(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_PRIVILEGE_DENIED);
    }
    // table not exist
    {
        pb::UpdateTableRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_db_name("db1");
        request.set_table_name("not_exist");
        request.set_description("new description");

        common::SynchronizedClosure rpc_waiter;
        stub.update_table(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_TABLE_NOT_EXIST);
    }
    // succ
    {
        pb::UpdateTableRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_db_name("db1");
        request.set_table_name("table1");
        request.set_description("new description");

        common::SynchronizedClosure rpc_waiter;
        stub.update_table(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), OK);
    }
}

TEST_F(DatabaseServiceTests, add_column) {
    MasterStub stub;
    // not leader
    {
        g_is_leader = false;
        pb::AddColumnRequest request;
        pb::AckResponse response;
        fill_auth(request);

        common::SynchronizedClosure rpc_waiter;
        stub.add_column(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_NOT_PRIMARY);
        g_is_leader = true;
    }
    // authenticate failed
    {
        pb::AddColumnRequest request;
        pb::AckResponse response;
        fill_error_auth(request);

        common::SynchronizedClosure rpc_waiter;
        stub.add_column(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_AUTHENTICATE_FAILED);
    }
    // privilege denied
    {
        pb::AddColumnRequest request;
        pb::AckResponse response;
        request.mutable_auth_info()->set_username("hello1");
        request.mutable_auth_info()->set_password("world1");
        request.set_db_name("db1");
        request.set_table_name("table1");

        common::SynchronizedClosure rpc_waiter;
        stub.add_column(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_PRIVILEGE_DENIED);
    }
    // succ
    {
        pb::AddColumnRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_db_name("db1");
        request.set_table_name("table1");

        common::SynchronizedClosure rpc_waiter;
        stub.add_column(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), OK);
    }
}

TEST_F(DatabaseServiceTests, show_table) {
    MasterStub stub;
    // not leader
    {
        g_is_leader = false;
        pb::ShowTableRequest request;
        pb::ShowTableResponse response;
        fill_auth(request);

        common::SynchronizedClosure rpc_waiter;
        stub.show_table(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_NOT_PRIMARY);
        g_is_leader = true;
    }
    // authenticate failed
    {
        pb::ShowTableRequest request;
        pb::ShowTableResponse response;
        fill_error_auth(request);

        common::SynchronizedClosure rpc_waiter;
        stub.show_table(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_AUTHENTICATE_FAILED);
    }
    // privilege denied
    {
        pb::ShowTableRequest request;
        pb::ShowTableResponse response;
        request.mutable_auth_info()->set_username("hello1");
        request.mutable_auth_info()->set_password("world1");
        request.set_db_name("db1");
        request.set_table_name("table1");

        common::SynchronizedClosure rpc_waiter;
        stub.show_table(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_PRIVILEGE_DENIED);
    }
    // not exist
    {
        pb::ShowTableRequest request;
        pb::ShowTableResponse response;
        fill_auth(request);
        request.set_table_id(TBLID_MAX);

        common::SynchronizedClosure rpc_waiter;
        stub.show_table(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_TABLE_NOT_EXIST);
    }
    // succ
    {
        pb::ShowTableRequest request;
        pb::ShowTableResponse response;
        fill_auth(request);
        request.set_table_id(11);

        common::SynchronizedClosure rpc_waiter;
        stub.show_table(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), OK);
    }
}

TEST_F(DatabaseServiceTests, list_table) {
    MasterStub stub;
    // not leader
    {
        g_is_leader = false;
        pb::ListTableRequest request;
        pb::ListTableResponse response;
        fill_auth(request);

        common::SynchronizedClosure rpc_waiter;
        stub.list_table(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_NOT_PRIMARY);
        g_is_leader = true;
    }
    // authenticate failed
    {
        pb::ListTableRequest request;
        pb::ListTableResponse response;
        fill_error_auth(request);

        common::SynchronizedClosure rpc_waiter;
        stub.list_table(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_AUTHENTICATE_FAILED);
    }
    // privilege denied
    {
        pb::ListTableRequest request;
        pb::ListTableResponse response;
        request.mutable_auth_info()->set_username("hello1");
        request.mutable_auth_info()->set_password("world1");
        request.set_db_name("db1");

        common::SynchronizedClosure rpc_waiter;
        stub.list_table(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), OK);
        EXPECT_EQ(0, response.table_infos_size());
    }
    // not exist
    {
        pb::ListTableRequest request;
        pb::ListTableResponse response;
        fill_auth(request);
        request.set_db_name("not_exist");

        common::SynchronizedClosure rpc_waiter;
        stub.list_table(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_DB_NOT_EXIST);
    }
    // succ
    {
        pb::ListTableRequest request;
        pb::ListTableResponse response;
        fill_auth(request);
        request.set_db_name("db1");

        common::SynchronizedClosure rpc_waiter;
        stub.list_table(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), OK);
        EXPECT_TRUE(response.table_infos_size() > 0);
    }
}

TEST_F(DatabaseServiceTests, create_index) {
    MasterStub stub;
    // not leader
    {
        g_is_leader = false;
        pb::CreateIndexRequest request;
        pb::AckResponse response;
        fill_auth(request);

        common::SynchronizedClosure rpc_waiter;
        stub.create_index(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_NOT_PRIMARY);
        g_is_leader = true;
    }
    // authenticate failed
    {
        pb::CreateIndexRequest request;
        pb::AckResponse response;
        fill_error_auth(request);

        common::SynchronizedClosure rpc_waiter;
        stub.create_index(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_AUTHENTICATE_FAILED);
    }
    // privilege denied
    {
        pb::CreateIndexRequest request;
        pb::AckResponse response;
        request.mutable_auth_info()->set_username("hello1");
        request.mutable_auth_info()->set_password("world1");
        request.set_db_name("db1");
        request.set_table_name("table1");

        common::SynchronizedClosure rpc_waiter;
        stub.create_index(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_PRIVILEGE_DENIED);
    }
    // db not exist
    {
        pb::CreateIndexRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_db_name("not_exist");
        request.set_table_name("table1");

        common::SynchronizedClosure rpc_waiter;
        stub.create_index(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_DB_NOT_EXIST);
    }
    // table not exist
    {
        pb::CreateIndexRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_db_name("db1");
        request.set_table_name("not_exist");

        common::SynchronizedClosure rpc_waiter;
        stub.create_index(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_TABLE_NOT_EXIST);
    }
    // no any index
    {
        pb::CreateIndexRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_db_name("db1");
        request.set_table_name("table1");

        common::SynchronizedClosure rpc_waiter;
        stub.create_index(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_INVALID_ARGUMENT);
    }
    {
        pb::CreateIndexRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_db_name("db1");
        request.set_table_name("table1");
        auto vector_index = request.add_vector_indexes();
        vector_index->set_is_auto_build(true);

        common::SynchronizedClosure rpc_waiter;
        stub.create_index(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_INVALID_ARGUMENT);
    }
    {
        pb::CreateIndexRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_db_name("db1");
        request.set_table_name("table1");
        auto vector_index = request.add_vector_indexes();
        vector_index->set_is_auto_build(true);
        vector_index->mutable_auto_build_index_param()->mutable_policy_param();

        common::SynchronizedClosure rpc_waiter;
        stub.create_index(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_INVALID_ARGUMENT);
    }
    {
        pb::CreateIndexRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_db_name("db1");
        request.set_table_name("table1");
        auto vector_index = request.add_vector_indexes();
        vector_index->set_is_auto_build(true);
        vector_index->mutable_auto_build_index_param()->set_policy_type(common::IndexAutoBuildPolicyType::TIMING);
        vector_index->mutable_auto_build_index_param()->mutable_policy_param()->set_period_s(1);

        common::SynchronizedClosure rpc_waiter;
        stub.create_index(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_INVALID_ARGUMENT);
    }
    {
        pb::CreateIndexRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_db_name("db1");
        request.set_table_name("table1");
        auto vector_index = request.add_vector_indexes();
        vector_index->set_is_auto_build(true);
        vector_index->mutable_auto_build_index_param()->set_policy_type(common::IndexAutoBuildPolicyType::TIMING);
        vector_index->mutable_auto_build_index_param()->mutable_policy_param()->set_period_s(1);

        common::SynchronizedClosure rpc_waiter;
        stub.create_index(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_INVALID_ARGUMENT);
    }
    {
        pb::CreateIndexRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_db_name("db1");
        request.set_table_name("table1");
        auto vector_index = request.add_vector_indexes();
        vector_index->set_is_auto_build(true);
        vector_index->mutable_auto_build_index_param()->set_policy_type(common::IndexAutoBuildPolicyType::TIMING);
        vector_index->mutable_auto_build_index_param()->mutable_policy_param()->set_timing("");

        common::SynchronizedClosure rpc_waiter;
        stub.create_index(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_INVALID_ARGUMENT);
    }
    {
        pb::CreateIndexRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_db_name("db1");
        request.set_table_name("table1");
        auto vector_index = request.add_vector_indexes();
        vector_index->set_is_auto_build(true);
        vector_index->mutable_auto_build_index_param()->set_policy_type(common::IndexAutoBuildPolicyType::TIMING);
        vector_index->mutable_auto_build_index_param()->mutable_policy_param()->set_timing("xxyy");

        common::SynchronizedClosure rpc_waiter;
        stub.create_index(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_INVALID_ARGUMENT);
    }
    {
        pb::CreateIndexRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_db_name("db1");
        request.set_table_name("table1");
        auto vector_index = request.add_vector_indexes();
        vector_index->set_is_auto_build(true);
        vector_index->mutable_auto_build_index_param()->set_policy_type(common::IndexAutoBuildPolicyType::PERIODICAL);
        vector_index->mutable_auto_build_index_param()->mutable_policy_param()->set_row_count_increment(1);

        common::SynchronizedClosure rpc_waiter;
        stub.create_index(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_INVALID_ARGUMENT);
    }
    {
        pb::CreateIndexRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_db_name("db1");
        request.set_table_name("table1");
        auto vector_index = request.add_vector_indexes();
        vector_index->set_is_auto_build(true);
        vector_index->mutable_auto_build_index_param()->set_policy_type(common::IndexAutoBuildPolicyType::PERIODICAL);
        vector_index->mutable_auto_build_index_param()->mutable_policy_param()->set_period_s(0);

        common::SynchronizedClosure rpc_waiter;
        stub.create_index(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_INVALID_ARGUMENT);
    }
    {
        pb::CreateIndexRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_db_name("db1");
        request.set_table_name("table1");
        auto vector_index = request.add_vector_indexes();
        vector_index->set_is_auto_build(true);
        vector_index->mutable_auto_build_index_param()->set_policy_type(common::IndexAutoBuildPolicyType::PERIODICAL);
        vector_index->mutable_auto_build_index_param()->mutable_policy_param()->set_period_s(FLAGS_auto_build_index_min_period_s - 1);

        common::SynchronizedClosure rpc_waiter;
        stub.create_index(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_INVALID_ARGUMENT);
    }
    {
        pb::CreateIndexRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_db_name("db1");
        request.set_table_name("table1");
        auto vector_index = request.add_vector_indexes();
        vector_index->set_is_auto_build(true);
        vector_index->mutable_auto_build_index_param()->set_policy_type(common::IndexAutoBuildPolicyType::ROW_COUNT_INCREMENT);
        vector_index->mutable_auto_build_index_param()->mutable_policy_param()->set_timing("2024-02-29T00:00:00Z");

        common::SynchronizedClosure rpc_waiter;
        stub.create_index(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_INVALID_ARGUMENT);
    }
    {
        pb::CreateIndexRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_db_name("db1");
        request.set_table_name("table1");
        auto vector_index = request.add_vector_indexes();
        vector_index->set_is_auto_build(true);
        vector_index->mutable_auto_build_index_param()->set_policy_type(common::IndexAutoBuildPolicyType::ROW_COUNT_INCREMENT);
        vector_index->mutable_auto_build_index_param()->mutable_policy_param()->set_row_count_increment(0);

        common::SynchronizedClosure rpc_waiter;
        stub.create_index(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_INVALID_ARGUMENT);
    }
    {
        pb::CreateIndexRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_db_name("db1");
        request.set_table_name("table1");
        auto vector_index = request.add_vector_indexes();
        vector_index->set_is_auto_build(true);
        vector_index->mutable_auto_build_index_param()->set_policy_type(common::IndexAutoBuildPolicyType::ROW_COUNT_INCREMENT);
        vector_index->mutable_auto_build_index_param()->mutable_policy_param()->set_row_count_increment(0);

        common::SynchronizedClosure rpc_waiter;
        stub.create_index(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_INVALID_ARGUMENT);
    }
    {
        pb::CreateIndexRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_db_name("db1");
        request.set_table_name("table1");
        auto vector_index = request.add_vector_indexes();
        vector_index->set_is_auto_build(true);
        vector_index->mutable_auto_build_index_param()->set_policy_type(common::IndexAutoBuildPolicyType::ROW_COUNT_INCREMENT);
        vector_index->mutable_auto_build_index_param()->mutable_policy_param()->set_row_count_increment(FLAGS_auto_build_index_min_row_count_increment - 1);

        common::SynchronizedClosure rpc_waiter;
        stub.create_index(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_INVALID_ARGUMENT);
    }
    {
        pb::CreateIndexRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_db_name("db1");
        request.set_table_name("table1");
        auto vector_index = request.add_vector_indexes();
        vector_index->set_is_auto_build(true);
        vector_index->mutable_auto_build_index_param()->set_policy_type("invalid policy type");
        vector_index->mutable_auto_build_index_param()->mutable_policy_param()->set_timing("2024-02-29T00:00:00Z");

        common::SynchronizedClosure rpc_waiter;
        stub.create_index(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_INVALID_ARGUMENT);
    }

    // succ
    {
        pb::CreateIndexRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_db_name("db1");
        request.set_table_name("table1");
        auto vector_index = request.add_vector_indexes();
        vector_index->set_is_auto_build(false);
        common::SynchronizedClosure rpc_waiter;
        stub.create_index(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), OK);
    }
}

TEST_F(DatabaseServiceTests, drop_index) {
    MasterStub stub;
    // not leader
    {
        g_is_leader = false;
        pb::DropIndexRequest request;
        pb::AckResponse response;
        fill_auth(request);

        common::SynchronizedClosure rpc_waiter;
        stub.drop_index(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_NOT_PRIMARY);
        g_is_leader = true;
    }
    // authenticate failed
    {
        pb::DropIndexRequest request;
        pb::AckResponse response;
        fill_error_auth(request);

        common::SynchronizedClosure rpc_waiter;
        stub.drop_index(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_AUTHENTICATE_FAILED);
    }
    // privilege denied
    {
        pb::DropIndexRequest request;
        pb::AckResponse response;
        request.mutable_auth_info()->set_username("hello1");
        request.mutable_auth_info()->set_password("world1");
        request.set_db_name("db1");
        request.set_table_name("table1");

        common::SynchronizedClosure rpc_waiter;
        stub.drop_index(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_PRIVILEGE_DENIED);
    }
    // invalid argument
    {
        pb::DropIndexRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_db_name("db1");
        request.set_table_name("table1");
        request.set_index_name("my_index");

        common::SynchronizedClosure rpc_waiter;
        stub.drop_index(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_INVALID_ARGUMENT);
    }
    // invalid argument
    {
        pb::DropIndexRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_db_name("db1");
        request.set_table_name("table1");
        request.set_index_id(123);

        common::SynchronizedClosure rpc_waiter;
        stub.drop_index(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_INVALID_ARGUMENT);
    }
    // succ
    {
        pb::DropIndexRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_db_name("db1");
        request.set_table_name("table1");
        request.set_index_name("my_index");
        request.set_index_id(123);

        common::SynchronizedClosure rpc_waiter;
        stub.drop_index(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), OK);
    }
}

TEST_F(DatabaseServiceTests, rebuild_index) {
    MasterStub stub;
    // not leader
    {
        g_is_leader = false;
        pb::RebuildIndexRequest request;
        pb::AckResponse response;
        fill_auth(request);

        common::SynchronizedClosure rpc_waiter;
        stub.rebuild_index(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_NOT_PRIMARY);
        g_is_leader = true;
    }
    // authenticate failed
    {
        pb::RebuildIndexRequest request;
        pb::AckResponse response;
        fill_error_auth(request);

        common::SynchronizedClosure rpc_waiter;
        stub.rebuild_index(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_AUTHENTICATE_FAILED);
    }
    // privilege denied
    {
        pb::RebuildIndexRequest request;
        pb::AckResponse response;
        request.mutable_auth_info()->set_username("hello1");
        request.mutable_auth_info()->set_password("world1");
        request.set_db_name("db1");
        request.set_table_name("table1");

        common::SynchronizedClosure rpc_waiter;
        stub.rebuild_index(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_PRIVILEGE_DENIED);
    }
    // db not exist
    {
        pb::RebuildIndexRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_db_name("not_exist");
        request.set_table_name("table1");

        common::SynchronizedClosure rpc_waiter;
        stub.rebuild_index(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_DB_NOT_EXIST);
    }
    // table not exist
    {
        pb::RebuildIndexRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_db_name("db1");
        request.set_table_name("not_exist");

        common::SynchronizedClosure rpc_waiter;
        stub.rebuild_index(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_TABLE_NOT_EXIST);
    }
    // succ
    {
        pb::RebuildIndexRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_db_name("db1");
        request.set_table_name("table1");
        request.set_index_name("my_index");

        common::SynchronizedClosure rpc_waiter;
        stub.rebuild_index(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), OK);
    }
}

TEST_F(DatabaseServiceTests, show_index) {
    MasterStub stub;
    // not leader
    {
        g_is_leader = false;
        pb::ShowIndexRequest request;
        pb::ShowIndexResponse response;
        fill_auth(request);

        common::SynchronizedClosure rpc_waiter;
        stub.show_index(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_NOT_PRIMARY);
        g_is_leader = true;
    }
    // authenticate failed
    {
        pb::ShowIndexRequest request;
        pb::ShowIndexResponse response;
        fill_error_auth(request);

        common::SynchronizedClosure rpc_waiter;
        stub.show_index(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_AUTHENTICATE_FAILED);
    }
    // privilege denied
    {
        pb::ShowIndexRequest request;
        pb::ShowIndexResponse response;
        request.mutable_auth_info()->set_username("hello1");
        request.mutable_auth_info()->set_password("world1");
        request.set_db_name("db1");
        request.set_table_name("table1");

        common::SynchronizedClosure rpc_waiter;
        stub.show_index(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_PRIVILEGE_DENIED);
    }
    // db not exist
    {
        pb::ShowIndexRequest request;
        pb::ShowIndexResponse response;
        fill_auth(request);
        request.set_db_name("not_exist");
        request.set_table_name("table1");

        common::SynchronizedClosure rpc_waiter;
        stub.show_index(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_DB_NOT_EXIST);
    }
    // table not exist
    {
        pb::ShowIndexRequest request;
        pb::ShowIndexResponse response;
        fill_auth(request);
        request.set_db_name("db1");
        request.set_table_name("not_exist");

        common::SynchronizedClosure rpc_waiter;
        stub.show_index(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_TABLE_NOT_EXIST);
    }
    // index not exist
    {
        pb::ShowIndexRequest request;
        pb::ShowIndexResponse response;
        fill_auth(request);
        request.set_db_name("db1");
        request.set_table_name("table1");

        common::SynchronizedClosure rpc_waiter;
        stub.show_index(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_INDEX_NOT_EXIST);
    }
    // succ with vector index
    {
        pb::ShowIndexRequest request;
        pb::ShowIndexResponse response;
        fill_auth(request);
        request.set_db_name("db1");
        request.set_table_name("table1");
        request.set_index_name("index_col3");

        common::SynchronizedClosure rpc_waiter;
        stub.show_index(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), OK);
        EXPECT_EQ(response.vector_index().schema().index_name(), "index_col3");
    }
    // succ with scalar index
    {
        pb::ShowIndexRequest request;
        pb::ShowIndexResponse response;
        fill_auth(request);
        request.set_db_name("db1");
        request.set_table_name("table1");
        request.set_index_name("index_col2");

        common::SynchronizedClosure rpc_waiter;
        stub.show_index(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), OK);
        EXPECT_EQ(response.scalar_index().schema().index_name(), "index_col2");
    }
}

TEST_F(DatabaseServiceTests, list_index) {
    MasterStub stub;
    // not leader
    {
        g_is_leader = false;
        pb::ListIndexRequest request;
        pb::ListIndexResponse response;
        fill_auth(request);

        common::SynchronizedClosure rpc_waiter;
        stub.list_index(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_NOT_PRIMARY);
        g_is_leader = true;
    }
    // authenticate failed
    {
        pb::ListIndexRequest request;
        pb::ListIndexResponse response;
        fill_error_auth(request);

        common::SynchronizedClosure rpc_waiter;
        stub.list_index(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_AUTHENTICATE_FAILED);
    }
    // privilege denied
    {
        pb::ListIndexRequest request;
        pb::ListIndexResponse response;
        request.mutable_auth_info()->set_username("hello1");
        request.mutable_auth_info()->set_password("world1");

        common::SynchronizedClosure rpc_waiter;
        stub.list_index(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_PRIVILEGE_DENIED);
    }
    // db not exist
    {
        pb::ListIndexRequest request;
        pb::ListIndexResponse response;
        fill_auth(request);
        request.set_db_name("not_exist");
        request.set_table_name("table1");

        common::SynchronizedClosure rpc_waiter;
        stub.list_index(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_DB_NOT_EXIST);
    }
    // table not exist
    {
        pb::ListIndexRequest request;
        pb::ListIndexResponse response;
        fill_auth(request);
        request.set_db_name("db1");
        request.set_table_name("not_exist");

        common::SynchronizedClosure rpc_waiter;
        stub.list_index(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_TABLE_NOT_EXIST);
    }
    // succ
    {
        pb::ListIndexRequest request;
        pb::ListIndexResponse response;
        fill_auth(request);
        request.set_db_name("db1");
        request.set_table_name("table1");

        common::SynchronizedClosure rpc_waiter;
        stub.list_index(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), OK);
    }
}

TEST_F(DatabaseServiceTests, modify_index) {
    MasterStub stub;
    // leader
    {
        g_is_leader = false;
        pb::ModifyIndexRequest request;
        pb::AckResponse response;
        fill_auth(request);

        common::SynchronizedClosure rpc_waiter;
        stub.modify_index(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_NOT_PRIMARY);
        g_is_leader = true;
    }
    // authenticate failed
    {
        pb::ModifyIndexRequest request;
        pb::AckResponse response;
        fill_error_auth(request);

        common::SynchronizedClosure rpc_waiter;
        stub.modify_index(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_AUTHENTICATE_FAILED);
    }
    // privilege denied
    {
        pb::ModifyIndexRequest request;
        pb::AckResponse response;
        request.mutable_auth_info()->set_username("hello1");
        request.mutable_auth_info()->set_password("world1");
        request.set_db_name("db1");
        request.set_table_name("table1");

        common::SynchronizedClosure rpc_waiter;
        stub.modify_index(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_PRIVILEGE_DENIED);
    }
    // no is auto build
    {
        pb::ModifyIndexRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_db_name("db1");
        request.set_table_name("table1");

        common::SynchronizedClosure rpc_waiter;
        stub.modify_index(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_INVALID_ARGUMENT);
    }
    // no auto build param
    {
        pb::ModifyIndexRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_db_name("db1");
        request.set_table_name("table1");
        request.set_is_auto_build(true);

        common::SynchronizedClosure rpc_waiter;
        stub.modify_index(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_INVALID_ARGUMENT);
    }
    // no auto build policy type
    {
        pb::ModifyIndexRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_db_name("db1");
        request.set_table_name("table1");
        request.set_is_auto_build(true);
        mochow::pb::AutoBuildIndexParam empty_param;
        request.mutable_auto_build_index_param()->CopyFrom(empty_param);

        common::SynchronizedClosure rpc_waiter;
        stub.modify_index(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_INVALID_ARGUMENT);
    }
    // no auto build policy param
    {
        pb::ModifyIndexRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_db_name("db1");
        request.set_table_name("table1");
        request.set_is_auto_build(true);
        request.mutable_auto_build_index_param()->set_policy_type(common::IndexAutoBuildPolicyType::TIMING);

        common::SynchronizedClosure rpc_waiter;
        stub.modify_index(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_INVALID_ARGUMENT);
    }
    // no timing param
    {
        pb::ModifyIndexRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_db_name("db1");
        request.set_table_name("table1");
        request.set_is_auto_build(true);
        request.mutable_auto_build_index_param()->set_policy_type(common::IndexAutoBuildPolicyType::TIMING);
        request.mutable_auto_build_index_param()->mutable_policy_param()->set_period_s(1);

        common::SynchronizedClosure rpc_waiter;
        stub.modify_index(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_INVALID_ARGUMENT);
    }
    // invaild timing param
    {
        pb::ModifyIndexRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_db_name("db1");
        request.set_table_name("table1");
        request.set_is_auto_build(true);
        request.mutable_auto_build_index_param()->set_policy_type(common::IndexAutoBuildPolicyType::TIMING);
        request.mutable_auto_build_index_param()->mutable_policy_param()->set_timing("");

        common::SynchronizedClosure rpc_waiter;
        stub.modify_index(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_INVALID_ARGUMENT);
    }
    // invaild timing param
    {
        pb::ModifyIndexRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_db_name("db1");
        request.set_table_name("table1");
        request.set_is_auto_build(true);
        request.mutable_auto_build_index_param()->set_policy_type(common::IndexAutoBuildPolicyType::TIMING);
        request.mutable_auto_build_index_param()->mutable_policy_param()->set_timing("xxyy");

        common::SynchronizedClosure rpc_waiter;
        stub.modify_index(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_INVALID_ARGUMENT);
    }
    // no period_s param
    {
        pb::ModifyIndexRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_db_name("db1");
        request.set_table_name("table1");
        request.set_is_auto_build(true);
        request.mutable_auto_build_index_param()->set_policy_type(common::IndexAutoBuildPolicyType::PERIODICAL);
        request.mutable_auto_build_index_param()->mutable_policy_param()->set_row_count_increment(1);

        common::SynchronizedClosure rpc_waiter;
        stub.modify_index(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_INVALID_ARGUMENT);
    }
    // invalid period_s param
    {
        pb::ModifyIndexRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_db_name("db1");
        request.set_table_name("table1");
        request.set_is_auto_build(true);
        request.mutable_auto_build_index_param()->set_policy_type(common::IndexAutoBuildPolicyType::PERIODICAL);
        request.mutable_auto_build_index_param()->mutable_policy_param()->set_period_s(0);

        common::SynchronizedClosure rpc_waiter;
        stub.modify_index(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_INVALID_ARGUMENT);
    }
    // invalid period_s param
    {
        pb::ModifyIndexRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_db_name("db1");
        request.set_table_name("table1");
        request.set_is_auto_build(true);
        request.mutable_auto_build_index_param()->set_policy_type(common::IndexAutoBuildPolicyType::PERIODICAL);
        request.mutable_auto_build_index_param()->mutable_policy_param()->set_period_s(FLAGS_auto_build_index_min_period_s - 1);

        common::SynchronizedClosure rpc_waiter;
        stub.modify_index(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_INVALID_ARGUMENT);
    }
    // no row_count_increment param
    {
        pb::ModifyIndexRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_db_name("db1");
        request.set_table_name("table1");
        request.set_is_auto_build(true);
        request.mutable_auto_build_index_param()->set_policy_type(common::IndexAutoBuildPolicyType::ROW_COUNT_INCREMENT);
        request.mutable_auto_build_index_param()->mutable_policy_param()->set_timing("2024-02-29T00:00:00Z");

        common::SynchronizedClosure rpc_waiter;
        stub.modify_index(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_INVALID_ARGUMENT);
    }
    // invalid row_count_increment param
    {
        pb::ModifyIndexRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_db_name("db1");
        request.set_table_name("table1");
        request.set_is_auto_build(true);
        request.mutable_auto_build_index_param()->set_policy_type(common::IndexAutoBuildPolicyType::ROW_COUNT_INCREMENT);
        request.mutable_auto_build_index_param()->mutable_policy_param()->set_row_count_increment(0);

        common::SynchronizedClosure rpc_waiter;
        stub.modify_index(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_INVALID_ARGUMENT);
    }
    // invalid row_count param
    {
        pb::ModifyIndexRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_db_name("db1");
        request.set_table_name("table1");
        request.set_is_auto_build(true);
        request.mutable_auto_build_index_param()->set_policy_type(common::IndexAutoBuildPolicyType::ROW_COUNT_INCREMENT);
        request.mutable_auto_build_index_param()->mutable_policy_param()->set_row_count_increment(FLAGS_auto_build_index_min_row_count_increment - 1);

        common::SynchronizedClosure rpc_waiter;
        stub.modify_index(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_INVALID_ARGUMENT);
    }
    // invalid policy type
    {
        pb::ModifyIndexRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_db_name("db1");
        request.set_table_name("table1");
        request.set_is_auto_build(true);
        request.mutable_auto_build_index_param()->set_policy_type("invalid policy type");
        request.mutable_auto_build_index_param()->mutable_policy_param()->set_timing("2024-02-29T00:00:00Z");

        common::SynchronizedClosure rpc_waiter;
        stub.modify_index(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_INVALID_ARGUMENT);
    }

    // succ for disable auto build
    {
        pb::ModifyIndexRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_db_name("db1");
        request.set_table_name("table1");
        request.set_is_auto_build(false);

        common::SynchronizedClosure rpc_waiter;
        stub.modify_index(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), OK);
    }
    // succ for enable auto build
    {
        pb::ModifyIndexRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_db_name("db1");
        request.set_table_name("table1");
        request.set_is_auto_build(true);
        request.mutable_auto_build_index_param()->set_policy_type(common::IndexAutoBuildPolicyType::TIMING);
        request.mutable_auto_build_index_param()->mutable_policy_param()->set_timing("2025-02-28T00:00:00Z");

        common::SynchronizedClosure rpc_waiter;
        stub.modify_index(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), OK);
    }
}

TEST_F(DatabaseServiceTests, alias_table) {
    MasterStub stub;
    // not leader
    {
        g_is_leader = false;
        pb::AliasTableRequest request;
        pb::AckResponse response;
        fill_auth(request);

        common::SynchronizedClosure rpc_waiter;
        stub.alias_table(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_NOT_PRIMARY);
        g_is_leader = true;
    }
    // authenticate failed
    {
        pb::AliasTableRequest request;
        pb::AckResponse response;
        fill_error_auth(request);

        common::SynchronizedClosure rpc_waiter;
        stub.alias_table(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_AUTHENTICATE_FAILED);
    }
    // privilege denied
    {
        pb::AliasTableRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_db_name("*");
        request.set_table_name("any");

        common::SynchronizedClosure rpc_waiter;
        stub.alias_table(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_PRIVILEGE_DENIED);
    }
    // invalid alias
    {
        pb::AliasTableRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_db_name("db1");
        request.set_table_name("table1");
        request.set_alias("");

        common::SynchronizedClosure rpc_waiter;
        stub.alias_table(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_INVALID_ALIAS);
    }
    // db not exist
    {
        pb::AliasTableRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_db_name("not_exist");
        request.set_table_name("*");
        request.set_alias("table1_alias");

        common::SynchronizedClosure rpc_waiter;
        stub.alias_table(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_DB_NOT_EXIST);
    }
    // table not exist
    {
        pb::AliasTableRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_db_name("db1");
        request.set_table_name("not_exist");
        request.set_alias("table1_alias");

        common::SynchronizedClosure rpc_waiter;
        stub.alias_table(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_TABLE_NOT_EXIST);
    }
    // succ
    {
        pb::AliasTableRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_db_name("db1");
        request.set_table_name("table1");
        request.set_alias("table1_alias");

        common::SynchronizedClosure rpc_waiter;
        stub.alias_table(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), OK);
    }
    // drop after alias
    {
        auto db = g_db_manager->get_db("db1");
        ASSERT_NE(db, nullptr);
        auto table = db->get_table("table1");
        ASSERT_NE(table, nullptr);
        EXPECT_EQ(db->get_table("table1_alias"), nullptr);

        db->alias_table("table1", "table1_alias");
        EXPECT_NE(db->get_table("table1_alias"), nullptr);
        EXPECT_EQ(db->get_table("table1_alias")->name(), "table1");

        EXPECT_TRUE(db->drop_table("table1_alias"));
        EXPECT_EQ(db->get_table("table1_alias"), nullptr);
        EXPECT_EQ(db->get_table("table1"), nullptr);
    }
}

TEST_F(DatabaseServiceTests, unalias_table) {
    MasterStub stub;
    // not leader
    {
        g_is_leader = false;
        pb::UnaliasTableRequest request;
        pb::AckResponse response;
        fill_auth(request);

        common::SynchronizedClosure rpc_waiter;
        stub.unalias_table(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_NOT_PRIMARY);
        g_is_leader = true;
    }
    // authenticate failed
    {
        pb::UnaliasTableRequest request;
        pb::AckResponse response;
        fill_error_auth(request);

        common::SynchronizedClosure rpc_waiter;
        stub.unalias_table(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_AUTHENTICATE_FAILED);
    }
    // privilege denied
    {
        pb::UnaliasTableRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_db_name("*");
        request.set_table_name("any");

        common::SynchronizedClosure rpc_waiter;
        stub.unalias_table(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_PRIVILEGE_DENIED);
    }
    // db not exist
    {
        pb::UnaliasTableRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_db_name("not_exist");
        request.set_table_name("*");

        common::SynchronizedClosure rpc_waiter;
        stub.unalias_table(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_DB_NOT_EXIST);
    }
    // table not exist
    {
        pb::UnaliasTableRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_db_name("db1");
        request.set_table_name("not_exist");

        common::SynchronizedClosure rpc_waiter;
        stub.unalias_table(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_TABLE_NOT_EXIST);
    }
    // succ
    {
        pb::UnaliasTableRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_db_name("db1");
        request.set_table_name("table1");
        request.set_alias("table1_alias");

        common::SynchronizedClosure rpc_waiter;
        stub.unalias_table(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), OK);
    }
}

TEST_F(DatabaseServiceTests, create_user) {
    MasterStub stub;
    // not leader
    {
        g_is_leader = false;
        pb::CreateUserRequest request;
        pb::AckResponse response;
        fill_auth(request);

        common::SynchronizedClosure rpc_waiter;
        stub.create_user(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_NOT_PRIMARY);
        g_is_leader = true;
    }
    // authenticate failed
    {
        pb::CreateUserRequest request;
        pb::AckResponse response;
        request.mutable_auth_info()->set_username(S_MOCHOW_ROOT_USERNAME);
        request.mutable_auth_info()->set_password("");

        common::SynchronizedClosure rpc_waiter;
        stub.create_user(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_AUTHENTICATE_FAILED);
    }
    // privilege denied
    {
        pb::CreateUserRequest request;
        pb::AckResponse response;
        request.mutable_auth_info()->set_username("hello1");
        request.mutable_auth_info()->set_password("world1");

        common::SynchronizedClosure rpc_waiter;
        stub.create_user(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_PRIVILEGE_DENIED);
    }
    // target user name is empty
    {
        pb::CreateUserRequest request;
        pb::AckResponse response;
        request.mutable_auth_info()->set_username(S_MOCHOW_ROOT_USERNAME);
        request.mutable_auth_info()->set_password(S_MOCHOW_ROOT_PASSWORD);
        request.mutable_target_user()->set_username("");

        common::SynchronizedClosure rpc_waiter;
        stub.create_user(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_INVALID_USERNAME);
    }
    // target user password is empty
    {
        pb::CreateUserRequest request;
        pb::AckResponse response;
        request.mutable_auth_info()->set_username(S_MOCHOW_ROOT_USERNAME);
        request.mutable_auth_info()->set_password(S_MOCHOW_ROOT_PASSWORD);
        request.mutable_target_user()->set_username("hello2");
        request.mutable_target_user()->set_password("");

        common::SynchronizedClosure rpc_waiter;
        stub.create_user(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_INVALID_PASSWORD);
    }
    // target user already exists
    {
        pb::CreateUserRequest request;
        pb::AckResponse response;
        request.mutable_auth_info()->set_username(S_MOCHOW_ROOT_USERNAME);
        request.mutable_auth_info()->set_password(S_MOCHOW_ROOT_PASSWORD);
        request.mutable_target_user()->set_username("hello1");
        request.mutable_target_user()->set_password("world1");

        common::SynchronizedClosure rpc_waiter;
        stub.create_user(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_USER_ALREADY_EXIST);
    }
    // create new user success
    {
        pb::CreateUserRequest request;
        pb::AckResponse response;
        request.mutable_auth_info()->set_username(S_MOCHOW_ROOT_USERNAME);
        request.mutable_auth_info()->set_password(S_MOCHOW_ROOT_PASSWORD);
        request.mutable_target_user()->set_username("hello2");
        request.mutable_target_user()->set_password("world2");

        common::SynchronizedClosure rpc_waiter;
        stub.create_user(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), OK);
    }

    // drop users
    g_rbac_manager->drop_user("hello2");
}

TEST_F(DatabaseServiceTests, drop_user) {
    MasterStub stub;
    // not leader
    {
        g_is_leader = false;
        pb::CreateUserRequest request;
        pb::AckResponse response;
        fill_auth(request);

        common::SynchronizedClosure rpc_waiter;
        stub.create_user(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_NOT_PRIMARY);
        g_is_leader = true;
    }
    // authenticate failed
    {
        pb::DropUserRequest request;
        pb::AckResponse response;
        request.mutable_auth_info()->set_username(S_MOCHOW_ROOT_USERNAME);
        request.mutable_auth_info()->set_password("");

        common::SynchronizedClosure rpc_waiter;
        stub.drop_user(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_AUTHENTICATE_FAILED);
    }
    // authorize failed
    {
        pb::DropUserRequest request;
        pb::AckResponse response;
        fill_auth(request, "hello1", "world1");

        common::SynchronizedClosure rpc_waiter;
        stub.drop_user(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_PRIVILEGE_DENIED);
    }
    // target user name is empty
    {
        pb::DropUserRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.mutable_target_user()->set_username("");

        common::SynchronizedClosure rpc_waiter;
        stub.drop_user(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_INVALID_USERNAME);
    }
    // target user not exists
    {
        pb::DropUserRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.mutable_target_user()->set_username("not_exist");

        common::SynchronizedClosure rpc_waiter;
        stub.drop_user(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_USER_NOT_EXIST);
    }
    // cannot drop __system
    {
        pb::DropUserRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.mutable_target_user()->set_username("__system");

        common::SynchronizedClosure rpc_waiter;
        stub.drop_user(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_REJECTED);
    }
    // cannot drop root
    {
        pb::DropUserRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.mutable_target_user()->set_username("root");

        common::SynchronizedClosure rpc_waiter;
        stub.drop_user(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_REJECTED);
    }
    // rejected by bloodline
    {
        pb::DropUserRequest request;
        pb::AckResponse response;
        fill_auth(request, "test_user", "anything");
        request.mutable_target_user()->set_username("hello1");

        common::SynchronizedClosure rpc_waiter;
        stub.drop_user(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_REJECTED);
    }
    // drop user success
    {
        pb::DropUserRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.mutable_target_user()->set_username("hello1");

        common::SynchronizedClosure rpc_waiter;
        stub.drop_user(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), OK);
    }
}

TEST_F(DatabaseServiceTests, change_password) {
    MasterStub stub;
    // Add more new user besides root and hello1
    ASSERT_TRUE(g_rbac_manager->create_user("hello2", "world2", "root") != nullptr);

    // not leader
    {
        g_is_leader = false;
        pb::ChangePasswordRequest request;
        pb::AckResponse response;
        fill_auth(request);

        common::SynchronizedClosure rpc_waiter;
        stub.change_password(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_NOT_PRIMARY);
        g_is_leader = true;
    }
    // authenticate failed
    {
        pb::ChangePasswordRequest request;
        pb::AckResponse response;
        fill_error_auth(request);

        common::SynchronizedClosure rpc_waiter;
        stub.change_password(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_AUTHENTICATE_FAILED);
    }
    // target user not exist
    {
        pb::ChangePasswordRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.mutable_target_user()->set_username("not_exist");

        common::SynchronizedClosure rpc_waiter;
        stub.change_password(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_USER_NOT_EXIST);
    }
    // target user password is empty
    {
        pb::ChangePasswordRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.mutable_target_user()->set_username("hello2");
        request.mutable_target_user()->set_password("");

        common::SynchronizedClosure rpc_waiter;
        stub.change_password(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_INVALID_PASSWORD);
    }

    // privilege denied (root change __system's password, root is not the ancestor of __system)
    {
        pb::ChangePasswordRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.mutable_target_user()->set_username(S_MOCHOW_SYSTEM_USERNAME);
        request.mutable_target_user()->set_password("any");

        common::SynchronizedClosure rpc_waiter;
        stub.change_password(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_PRIVILEGE_DENIED);
    }
    // privilege denied (common user change __system's password, common user is not the ancestor of __system)
    {
        pb::ChangePasswordRequest request;
        pb::AckResponse response;
        request.mutable_auth_info()->set_username("hello1");
        request.mutable_auth_info()->set_password("world1");
        request.mutable_target_user()->set_username(S_MOCHOW_SYSTEM_USERNAME);
        request.mutable_target_user()->set_password("any");

        common::SynchronizedClosure rpc_waiter;
        stub.change_password(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_PRIVILEGE_DENIED);
    }
    // privilege denied (common user change root's password, common user is not the ancestor of root)
    {
        pb::ChangePasswordRequest request;
        pb::AckResponse response;
        request.mutable_auth_info()->set_username("hello1");
        request.mutable_auth_info()->set_password("world1");
        request.mutable_target_user()->set_username(S_MOCHOW_ROOT_USERNAME);
        request.mutable_target_user()->set_password("any");

        common::SynchronizedClosure rpc_waiter;
        stub.change_password(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_PRIVILEGE_DENIED);
    }
    // privilege denied (auth user is the ancestor of target user, but it has no PASSWORD privilege)
    {
        pb::ChangePasswordRequest request;
        pb::AckResponse response;
        request.mutable_auth_info()->set_username("test_user_2");
        request.mutable_auth_info()->set_password("anything");
        request.mutable_target_user()->set_username("test_user_3");
        request.mutable_target_user()->set_password("new_password");

        common::SynchronizedClosure rpc_waiter;
        stub.change_password(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_PRIVILEGE_DENIED);
    }

    // common user change himself's password succ
    {
        pb::ChangePasswordRequest request;
        pb::AckResponse response;
        request.mutable_auth_info()->set_username("hello1");
        request.mutable_auth_info()->set_password("world1");
        request.mutable_target_user()->set_username("hello1");
        request.mutable_target_user()->set_password("mochow1");

        common::SynchronizedClosure rpc_waiter;
        stub.change_password(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), OK);
    }
    // root change common user's password succ
    {
        pb::ChangePasswordRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.mutable_target_user()->set_username("hello2");
        request.mutable_target_user()->set_password("mochow2");

        common::SynchronizedClosure rpc_waiter;
        stub.change_password(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), OK);
    }
    // __system change common user's password succ
    {
        pb::ChangePasswordRequest request;
        pb::AckResponse response;
        request.mutable_auth_info()->set_username(S_MOCHOW_SYSTEM_USERNAME);
        request.mutable_auth_info()->set_password(S_MOCHOW_SYSTEM_PASSWORD);
        request.mutable_target_user()->set_username("hello2");
        request.mutable_target_user()->set_password("mochow2");

        common::SynchronizedClosure rpc_waiter;
        stub.change_password(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), OK);
    }
    // root change root's password succ
    {
        pb::ChangePasswordRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.mutable_target_user()->set_username(S_MOCHOW_ROOT_USERNAME);
        request.mutable_target_user()->set_password("new_root_password");

        common::SynchronizedClosure rpc_waiter;
        stub.change_password(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), OK);
    }
    // __system change root's password succ
    {
        pb::ChangePasswordRequest request;
        pb::AckResponse response;
        request.mutable_auth_info()->set_username(S_MOCHOW_SYSTEM_USERNAME);
        request.mutable_auth_info()->set_password(S_MOCHOW_SYSTEM_PASSWORD);
        request.mutable_target_user()->set_username(S_MOCHOW_ROOT_USERNAME);
        request.mutable_target_user()->set_password(S_MOCHOW_ROOT_PASSWORD);

        common::SynchronizedClosure rpc_waiter;
        stub.change_password(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), OK);
    }
    // __system change __system's password succ
    {
        pb::ChangePasswordRequest request;
        pb::AckResponse response;
        request.mutable_auth_info()->set_username(S_MOCHOW_SYSTEM_USERNAME);
        request.mutable_auth_info()->set_password(S_MOCHOW_SYSTEM_PASSWORD);
        request.mutable_target_user()->set_username(S_MOCHOW_SYSTEM_USERNAME);
        request.mutable_target_user()->set_password("new_system_password");

        common::SynchronizedClosure rpc_waiter;
        stub.change_password(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), OK);
    }

    // drop users
    g_rbac_manager->drop_user("hello2");
}

TEST_F(DatabaseServiceTests, create_role) {
    MasterStub stub;
    // not leader
    {
        g_is_leader = false;
        pb::CreateRoleRequest request;
        pb::AckResponse response;
        fill_auth(request);

        common::SynchronizedClosure rpc_waiter;
        stub.create_role(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_NOT_PRIMARY);
        g_is_leader = true;
    }
    // authenticate failed
    {
        pb::CreateRoleRequest request;
        pb::AckResponse response;
        request.mutable_auth_info()->set_username(S_MOCHOW_ROOT_USERNAME);
        request.mutable_auth_info()->set_password("");

        common::SynchronizedClosure rpc_waiter;
        stub.create_role(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_AUTHENTICATE_FAILED);
    }
    // privilege denied
    {
        pb::CreateRoleRequest request;
        pb::AckResponse response;
        request.mutable_auth_info()->set_username("hello1");
        request.mutable_auth_info()->set_password("world1");

        common::SynchronizedClosure rpc_waiter;
        stub.create_role(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_PRIVILEGE_DENIED);
    }
    // role name is empty
    {
        pb::CreateRoleRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_role("");

        common::SynchronizedClosure rpc_waiter;
        stub.create_role(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_INVALID_ROLE_NAME);
    }
    // role already exists
    {
        pb::CreateRoleRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_role("ADMIN");

        common::SynchronizedClosure rpc_waiter;
        stub.create_role(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_ROLE_ALREADY_EXIST);
    }
    // succ
    {
        pb::CreateRoleRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_role("myrole");

        common::SynchronizedClosure rpc_waiter;
        stub.create_role(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), OK);
    }
}

TEST_F(DatabaseServiceTests, drop_role) {
    MasterStub stub;
    // not leader
    {
        g_is_leader = false;
        pb::DropRoleRequest request;
        pb::AckResponse response;
        fill_auth(request);

        common::SynchronizedClosure rpc_waiter;
        stub.drop_role(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_NOT_PRIMARY);
        g_is_leader = true;
    }
    // authenticate failed
    {
        pb::DropRoleRequest request;
        pb::AckResponse response;
        request.mutable_auth_info()->set_username(S_MOCHOW_ROOT_USERNAME);
        request.mutable_auth_info()->set_password("");

        common::SynchronizedClosure rpc_waiter;
        stub.drop_role(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_AUTHENTICATE_FAILED);
    }
    // privilege denied
    {
        pb::DropRoleRequest request;
        pb::AckResponse response;
        fill_auth(request, "hello1", "world1");

        common::SynchronizedClosure rpc_waiter;
        stub.drop_role(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_PRIVILEGE_DENIED);
    }
    // role name is empty
    {
        pb::DropRoleRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_role("");

        common::SynchronizedClosure rpc_waiter;
        stub.drop_role(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_INVALID_ROLE_NAME);
    }
    // role not exist
    {
        pb::DropRoleRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_role("not_exist");

        common::SynchronizedClosure rpc_waiter;
        stub.drop_role(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_ROLE_NOT_EXIST);
    }
    // catnot drop builtin role
    {
        pb::DropRoleRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_role("ADMIN");

        common::SynchronizedClosure rpc_waiter;
        stub.drop_role(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_REJECTED);
    }
    // rejected by bloodline
    {
        pb::DropRoleRequest request;
        pb::AckResponse response;
        fill_auth(request, "test_user", "anything");
        request.set_role("role1");

        common::SynchronizedClosure rpc_waiter;
        stub.drop_role(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_REJECTED);
    }
    // succ
    {
        pb::DropRoleRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_role("role1");

        common::SynchronizedClosure rpc_waiter;
        stub.drop_role(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), OK);
    }
}

TEST_F(DatabaseServiceTests, grant_user_roles) {
    MasterStub stub;
    // not leader
    {
        g_is_leader = false;
        pb::GrantUserRolesRequest request;
        pb::AckResponse response;
        fill_auth(request);

        common::SynchronizedClosure rpc_waiter;
        stub.grant_user_roles(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_NOT_PRIMARY);
        g_is_leader = true;
    }
    // authenticate failed
    {
        pb::GrantUserRolesRequest request;
        pb::AckResponse response;
        request.mutable_auth_info()->set_username(S_MOCHOW_ROOT_USERNAME);
        request.mutable_auth_info()->set_password("");

        common::SynchronizedClosure rpc_waiter;
        stub.grant_user_roles(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_AUTHENTICATE_FAILED);
    }
    // privilege denied
    {
        pb::GrantUserRolesRequest request;
        pb::AckResponse response;
        request.mutable_auth_info()->set_username("hello1");
        request.mutable_auth_info()->set_password("world1");

        common::SynchronizedClosure rpc_waiter;
        stub.grant_user_roles(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_PRIVILEGE_DENIED);
    }
    // invalid username
    {
        pb::GrantUserRolesRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_username("");

        common::SynchronizedClosure rpc_waiter;
        stub.grant_user_roles(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_INVALID_USERNAME);
    }
    // user not exist
    {
        pb::GrantUserRolesRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_username("not_exist");

        common::SynchronizedClosure rpc_waiter;
        stub.grant_user_roles(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_USER_NOT_EXIST);
    }
    // rejected by bloodline
    {
        pb::GrantUserRolesRequest request;
        pb::AckResponse response;
        fill_auth(request, "test_user", "anything");
        request.set_username("hello1");

        common::SynchronizedClosure rpc_waiter;
        stub.grant_user_roles(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_REJECTED);
    }
    // invalid role name
    {
        pb::GrantUserRolesRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_username("test_user");
        request.add_roles("");

        common::SynchronizedClosure rpc_waiter;
        stub.grant_user_roles(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_INVALID_ROLE_NAME);
    }
    // role not exist
    {
        pb::GrantUserRolesRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_username("test_user");
        request.add_roles("not_exist");

        common::SynchronizedClosure rpc_waiter;
        stub.grant_user_roles(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_ROLE_NOT_EXIST);
    }
    // succ
    {
        pb::GrantUserRolesRequest request;
        pb::AckResponse response;
        fill_auth(request, "test_user", "anything");
        request.set_username("test_user_3");
        request.add_roles("test_role");

        common::SynchronizedClosure rpc_waiter;
        stub.grant_user_roles(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), OK);
    }
}

TEST_F(DatabaseServiceTests, revoke_user_roles) {
    MasterStub stub;
    // not leader
    {
        g_is_leader = false;
        pb::RevokeUserRolesRequest request;
        pb::AckResponse response;
        fill_auth(request);

        common::SynchronizedClosure rpc_waiter;
        stub.revoke_user_roles(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_NOT_PRIMARY);
        g_is_leader = true;
    }
    // authenticate failed
    {
        pb::RevokeUserRolesRequest request;
        pb::AckResponse response;
        request.mutable_auth_info()->set_username(S_MOCHOW_ROOT_USERNAME);
        request.mutable_auth_info()->set_password("");

        common::SynchronizedClosure rpc_waiter;
        stub.revoke_user_roles(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_AUTHENTICATE_FAILED);
    }
    // privilege denied
    {
        pb::RevokeUserRolesRequest request;
        pb::AckResponse response;
        request.mutable_auth_info()->set_username("hello1");
        request.mutable_auth_info()->set_password("world1");

        common::SynchronizedClosure rpc_waiter;
        stub.revoke_user_roles(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_PRIVILEGE_DENIED);
    }
    // invalid username
    {
        pb::RevokeUserRolesRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_username("");

        common::SynchronizedClosure rpc_waiter;
        stub.revoke_user_roles(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_INVALID_USERNAME);
    }
    // user not exist
    {
        pb::RevokeUserRolesRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_username("not_exist");

        common::SynchronizedClosure rpc_waiter;
        stub.revoke_user_roles(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_USER_NOT_EXIST);
    }
    // cannot revoke root user any privileges
    {
        pb::RevokeUserRolesRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_username("root");

        common::SynchronizedClosure rpc_waiter;
        stub.revoke_user_roles(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_REJECTED);
    }
    // cannot revoke __system user any privileges
    {
        pb::RevokeUserRolesRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_username("__system");

        common::SynchronizedClosure rpc_waiter;
        stub.revoke_user_roles(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_REJECTED);
    }
    // rejected by bloodline
    {
        pb::RevokeUserRolesRequest request;
        pb::AckResponse response;
        fill_auth(request, "test_user", "anything");
        request.set_username("hello1");

        common::SynchronizedClosure rpc_waiter;
        stub.revoke_user_roles(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_REJECTED);
    }
    // invalid role name
    {
        pb::RevokeUserRolesRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_username("test_user");
        request.add_roles("");

        common::SynchronizedClosure rpc_waiter;
        stub.revoke_user_roles(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_INVALID_ROLE_NAME);
    }
    // role not exist
    {
        pb::RevokeUserRolesRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_username("test_user");
        request.add_roles("not_exist");

        common::SynchronizedClosure rpc_waiter;
        stub.revoke_user_roles(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_ROLE_NOT_EXIST);
    }
    // succ
    {
        pb::RevokeUserRolesRequest request;
        pb::AckResponse response;
        fill_auth(request, "test_user", "anything");
        request.set_username("test_user_2");
        request.add_roles("test_role");

        common::SynchronizedClosure rpc_waiter;
        stub.revoke_user_roles(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), OK);
    }
}

TEST_F(DatabaseServiceTests, grant_user_privileges) {
    MasterStub stub;
    // not leader
    {
        g_is_leader = false;
        pb::GrantUserPrivilegesRequest request;
        pb::AckResponse response;
        fill_auth(request);

        common::SynchronizedClosure rpc_waiter;
        stub.grant_user_privileges(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_NOT_PRIMARY);
        g_is_leader = true;
    }
    // authenticate failed
    {
        pb::GrantUserPrivilegesRequest request;
        pb::AckResponse response;
        request.mutable_auth_info()->set_username(S_MOCHOW_ROOT_USERNAME);
        request.mutable_auth_info()->set_password("");

        common::SynchronizedClosure rpc_waiter;
        stub.grant_user_privileges(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_AUTHENTICATE_FAILED);
    }
    // privilege denied
    {
        pb::GrantUserPrivilegesRequest request;
        pb::AckResponse response;
        fill_auth(request, "hello1", "world1");

        common::SynchronizedClosure rpc_waiter;
        stub.grant_user_privileges(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_PRIVILEGE_DENIED);
    }
    // invalid username
    {
        pb::GrantUserPrivilegesRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_username("");

        common::SynchronizedClosure rpc_waiter;
        stub.grant_user_privileges(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_INVALID_USERNAME);
    }
    // user not exist
    {
        pb::GrantUserPrivilegesRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_username("not_exist");

        common::SynchronizedClosure rpc_waiter;
        stub.grant_user_privileges(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_USER_NOT_EXIST);
    }
    // rejected by bloodline
    {
        pb::GrantUserPrivilegesRequest request;
        pb::AckResponse response;
        fill_auth(request, "test_user", "anything");
        request.set_username("hello1");

        common::SynchronizedClosure rpc_waiter;
        stub.grant_user_privileges(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_REJECTED);
    }
    // no any privilege tuple
    {
        pb::GrantUserPrivilegesRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_username("test_user");

        common::SynchronizedClosure rpc_waiter;
        stub.grant_user_privileges(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_INVALID_ARGUMENT);
    }
    // invalid rbac object
    {
        pb::GrantUserPrivilegesRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_username("test_user");
        request.add_priv_tuples(); // set nothing

        common::SynchronizedClosure rpc_waiter;
        stub.grant_user_privileges(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_INVALID_RBAC_OBJECT);
    }
    // db not exist in rbac object
    {
        pb::GrantUserPrivilegesRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_username("test_user");
        auto* priv_tuple = request.add_priv_tuples();
        priv_tuple->mutable_object()->set_database("not_exist");
        priv_tuple->mutable_object()->set_table("*");

        common::SynchronizedClosure rpc_waiter;
        stub.grant_user_privileges(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_DB_NOT_EXIST);
    }
    // table not exist in rbac object
    {
        pb::GrantUserPrivilegesRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_username("test_user");
        auto* priv_tuple = request.add_priv_tuples();
        priv_tuple->mutable_object()->set_database("db1");
        priv_tuple->mutable_object()->set_table("not_exist");

        common::SynchronizedClosure rpc_waiter;
        stub.grant_user_privileges(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_TABLE_NOT_EXIST);
    }
    // invalid privilege
    {
        pb::GrantUserPrivilegesRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_username("test_user");
        auto* priv_tuple = request.add_priv_tuples();
        priv_tuple->mutable_object()->set_database("*");
        priv_tuple->mutable_object()->set_table("*");
        priv_tuple->add_privileges("QUERY");
        priv_tuple = request.add_priv_tuples();
        priv_tuple->mutable_object()->set_database("*");
        priv_tuple->mutable_object()->set_table("*");
        priv_tuple->add_privileges("INVALID");

        common::SynchronizedClosure rpc_waiter;
        stub.grant_user_privileges(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_PRIVILEGE_NOT_FOUND);
    }
    // success
    {
        pb::GrantUserPrivilegesRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_username("test_user");
        auto* priv_tuple = request.add_priv_tuples();
        priv_tuple->mutable_object()->set_database("*");
        priv_tuple->mutable_object()->set_table("*");
        priv_tuple->add_privileges("INSERT");
        priv_tuple->add_privileges("SYSTEM_ALL");
        priv_tuple = request.add_priv_tuples();
        priv_tuple->mutable_object()->set_database("*");
        priv_tuple->mutable_object()->set_table("*");
        priv_tuple->add_privileges("TABLE_ALL");
        priv_tuple->add_privileges("TABLE_ALL"); // dup
        priv_tuple = request.add_priv_tuples();
        priv_tuple->mutable_object()->set_database("db1");
        priv_tuple->mutable_object()->set_table("*");
        priv_tuple->add_privileges("TABLE_READWRITE");
        priv_tuple = request.add_priv_tuples();
        priv_tuple->mutable_object()->set_database("db1");
        priv_tuple->mutable_object()->set_table("table1");
        priv_tuple->add_privileges("ALL");

        common::SynchronizedClosure rpc_waiter;
        stub.grant_user_privileges(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), OK);
    }
}

TEST_F(DatabaseServiceTests, revoke_user_privileges) {
    MasterStub stub;
    // not leader
    {
        g_is_leader = false;
        pb::RevokeUserPrivilegesRequest request;
        pb::AckResponse response;
        fill_auth(request);

        common::SynchronizedClosure rpc_waiter;
        stub.revoke_user_privileges(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_NOT_PRIMARY);
        g_is_leader = true;
    }
    // authenticate failed
    {
        pb::RevokeUserPrivilegesRequest request;
        pb::AckResponse response;
        request.mutable_auth_info()->set_username(S_MOCHOW_ROOT_USERNAME);
        request.mutable_auth_info()->set_password("");

        common::SynchronizedClosure rpc_waiter;
        stub.revoke_user_privileges(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_AUTHENTICATE_FAILED);
    }
    // privilege denied
    {
        pb::RevokeUserPrivilegesRequest request;
        pb::AckResponse response;
        request.mutable_auth_info()->set_username("hello1");
        request.mutable_auth_info()->set_password("world1");

        common::SynchronizedClosure rpc_waiter;
        stub.revoke_user_privileges(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_PRIVILEGE_DENIED);
    }
    // invalid username
    {
        pb::RevokeUserPrivilegesRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_username("");

        common::SynchronizedClosure rpc_waiter;
        stub.revoke_user_privileges(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_INVALID_USERNAME);
    }
    // user not exist
    {
        pb::RevokeUserPrivilegesRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_username("not_exist");

        common::SynchronizedClosure rpc_waiter;
        stub.revoke_user_privileges(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_USER_NOT_EXIST);
    }
    // cannot revoke any privilege from root
    {
        pb::RevokeUserPrivilegesRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_username("root");

        common::SynchronizedClosure rpc_waiter;
        stub.revoke_user_privileges(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_REJECTED);
    }
    // cannot revoke any privilege from __system
    {
        pb::RevokeUserPrivilegesRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_username("__system");

        common::SynchronizedClosure rpc_waiter;
        stub.revoke_user_privileges(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_REJECTED);
    }
    // rejected by bloodline
    {
        pb::RevokeUserPrivilegesRequest request;
        pb::AckResponse response;
        fill_auth(request, "test_user", "anything");
        request.set_username("hello1");

        common::SynchronizedClosure rpc_waiter;
        stub.revoke_user_privileges(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_REJECTED);
    }
    // no any privilege tuple
    {
        pb::RevokeUserPrivilegesRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_username("test_user");

        common::SynchronizedClosure rpc_waiter;
        stub.revoke_user_privileges(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_INVALID_ARGUMENT);
    }
    // invalid rbac object
    {
        pb::RevokeUserPrivilegesRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_username("test_user");
        request.add_priv_tuples(); // set nothing

        common::SynchronizedClosure rpc_waiter;
        stub.revoke_user_privileges(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_INVALID_RBAC_OBJECT);
    }
    // db not exist in rbac object
    {
        pb::RevokeUserPrivilegesRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_username("test_user");
        auto* priv_tuple = request.add_priv_tuples();
        priv_tuple->mutable_object()->set_database("not_exist");
        priv_tuple->mutable_object()->set_table("*");

        common::SynchronizedClosure rpc_waiter;
        stub.revoke_user_privileges(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_DB_NOT_EXIST);
    }
    // table not exist in rbac object
    {
        pb::RevokeUserPrivilegesRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_username("test_user");
        auto* priv_tuple = request.add_priv_tuples();
        priv_tuple->mutable_object()->set_database("db1");
        priv_tuple->mutable_object()->set_table("not_exist");

        common::SynchronizedClosure rpc_waiter;
        stub.revoke_user_privileges(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_TABLE_NOT_EXIST);
    }
    // invalid privilege
    {
        pb::RevokeUserPrivilegesRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_username("test_user");
        auto* priv_tuple = request.add_priv_tuples();
        priv_tuple->mutable_object()->set_database("*");
        priv_tuple->mutable_object()->set_table("*");
        priv_tuple->add_privileges("SELECT");
        priv_tuple = request.add_priv_tuples();
        priv_tuple->mutable_object()->set_database("*");
        priv_tuple->mutable_object()->set_table("*");
        priv_tuple->add_privileges("INSERT");
        priv_tuple->add_privileges("INVALID");

        common::SynchronizedClosure rpc_waiter;
        stub.revoke_user_privileges(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_PRIVILEGE_NOT_FOUND);
    }
    // success
    {
        pb::RevokeUserPrivilegesRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_username("test_user");
        auto* priv_tuple = request.add_priv_tuples();
        priv_tuple->mutable_object()->set_database("*");
        priv_tuple->mutable_object()->set_table("*");
        priv_tuple->add_privileges("INSERT");
        priv_tuple->add_privileges("CREATE_ROLE");
        priv_tuple->add_privileges("TABLE_CONTROL");
        priv_tuple = request.add_priv_tuples();
        priv_tuple->mutable_object()->set_database("*");
        priv_tuple->mutable_object()->set_table("*");
        priv_tuple->add_privileges("SYSTEM_ALL");
        priv_tuple->add_privileges("SYSTEM_ALL"); // dup
        priv_tuple = request.add_priv_tuples();
        priv_tuple->mutable_object()->set_database("db1");
        priv_tuple->mutable_object()->set_table("*");
        priv_tuple->add_privileges("SELECT");
        priv_tuple->add_privileges("TABLE_READWRITE");
        priv_tuple = request.add_priv_tuples();
        priv_tuple->mutable_object()->set_database("db1");
        priv_tuple->mutable_object()->set_table("table1");
        priv_tuple->add_privileges("TABLE_ALL");

        common::SynchronizedClosure rpc_waiter;
        stub.revoke_user_privileges(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), OK);
    }
}

TEST_F(DatabaseServiceTests, grant_role_privileges) {
    MasterStub stub;
    // not leader
    {
        g_is_leader = false;
        pb::GrantRolePrivilegesRequest request;
        pb::AckResponse response;
        fill_auth(request);

        common::SynchronizedClosure rpc_waiter;
        stub.grant_role_privileges(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_NOT_PRIMARY);
        g_is_leader = true;
    }
    // authenticate failed
    {
        pb::GrantRolePrivilegesRequest request;
        pb::AckResponse response;
        request.mutable_auth_info()->set_username(S_MOCHOW_ROOT_USERNAME);
        request.mutable_auth_info()->set_password("");

        common::SynchronizedClosure rpc_waiter;
        stub.grant_role_privileges(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_AUTHENTICATE_FAILED);
    }
    // privilege denied
    {
        pb::GrantRolePrivilegesRequest request;
        pb::AckResponse response;
        fill_auth(request, "hello1", "world1");

        common::SynchronizedClosure rpc_waiter;
        stub.grant_role_privileges(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_PRIVILEGE_DENIED);
    }
    // invalid role name
    {
        pb::GrantRolePrivilegesRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_role("");

        common::SynchronizedClosure rpc_waiter;
        stub.grant_role_privileges(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_INVALID_ROLE_NAME);
    }
    // role not exist
    {
        pb::GrantRolePrivilegesRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_role("not_exist");

        common::SynchronizedClosure rpc_waiter;
        stub.grant_role_privileges(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_ROLE_NOT_EXIST);
    }
    // rejected by bloodline
    {
        pb::GrantRolePrivilegesRequest request;
        pb::AckResponse response;
        fill_auth(request, "test_user", "anything");
        request.set_role("role1");

        common::SynchronizedClosure rpc_waiter;
        stub.grant_role_privileges(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_REJECTED);
    }
    // no any privilege tuple
    {
        pb::GrantRolePrivilegesRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_role("test_role");

        common::SynchronizedClosure rpc_waiter;
        stub.grant_role_privileges(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_INVALID_ARGUMENT);
    }
    // invalid rbac object
    {
        pb::GrantRolePrivilegesRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_role("test_role");
        request.add_priv_tuples(); // set nothing

        common::SynchronizedClosure rpc_waiter;
        stub.grant_role_privileges(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_INVALID_RBAC_OBJECT);
    }
    // db not exist in rbac object
    {
        pb::GrantRolePrivilegesRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_role("test_role");
        auto* priv_tuple = request.add_priv_tuples();
        priv_tuple->mutable_object()->set_database("not_exist");
        priv_tuple->mutable_object()->set_table("*");

        common::SynchronizedClosure rpc_waiter;
        stub.grant_role_privileges(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_DB_NOT_EXIST);
    }
    // table not exist in rbac object
    {
        pb::GrantRolePrivilegesRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_role("test_role");
        auto* priv_tuple = request.add_priv_tuples();
        priv_tuple->mutable_object()->set_database("db1");
        priv_tuple->mutable_object()->set_table("not_exist");

        common::SynchronizedClosure rpc_waiter;
        stub.grant_role_privileges(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_TABLE_NOT_EXIST);
    }
    // invalid privilege
    {
        pb::GrantRolePrivilegesRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_role("test_role");
        auto* priv_tuple = request.add_priv_tuples();
        priv_tuple->mutable_object()->set_database("*");
        priv_tuple->mutable_object()->set_table("*");
        priv_tuple->add_privileges("INSERT");
        priv_tuple->add_privileges("INVALID");

        common::SynchronizedClosure rpc_waiter;
        stub.grant_role_privileges(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_PRIVILEGE_NOT_FOUND);
    }
    // success
    {
        pb::GrantRolePrivilegesRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_role("test_role");
        auto* priv_tuple = request.add_priv_tuples();
        priv_tuple->mutable_object()->set_database("*");
        priv_tuple->mutable_object()->set_table("*");
        priv_tuple->add_privileges("DELETE");
        priv_tuple->add_privileges("TABLE_READONLY");
        priv_tuple = request.add_priv_tuples();
        priv_tuple->mutable_object()->set_database("*");
        priv_tuple->mutable_object()->set_table("*");
        priv_tuple->add_privileges("SYSTEM_ALL");
        priv_tuple->add_privileges("SYSTEM_ALL"); // dup
        priv_tuple = request.add_priv_tuples();
        priv_tuple->mutable_object()->set_database("db1");
        priv_tuple->mutable_object()->set_table("*");
        priv_tuple->add_privileges("SELECT");
        priv_tuple->add_privileges("TABLE_READWRITE");
        priv_tuple = request.add_priv_tuples();
        priv_tuple->mutable_object()->set_database("db1");
        priv_tuple->mutable_object()->set_table("table1");
        priv_tuple->add_privileges("TABLE_ALL");

        common::SynchronizedClosure rpc_waiter;
        stub.grant_role_privileges(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), OK);
    }
}

TEST_F(DatabaseServiceTests, revoke_role_privileges) {
    MasterStub stub;
    // not leader
    {
        g_is_leader = false;
        pb::RevokeRolePrivilegesRequest request;
        pb::AckResponse response;
        fill_auth(request);

        common::SynchronizedClosure rpc_waiter;
        stub.revoke_role_privileges(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_NOT_PRIMARY);
        g_is_leader = true;
    }
    // authenticate failed
    {
        pb::RevokeRolePrivilegesRequest request;
        pb::AckResponse response;
        request.mutable_auth_info()->set_username(S_MOCHOW_ROOT_USERNAME);
        request.mutable_auth_info()->set_password("");

        common::SynchronizedClosure rpc_waiter;
        stub.revoke_role_privileges(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_AUTHENTICATE_FAILED);
    }
    // privilege denied
    {
        pb::RevokeRolePrivilegesRequest request;
        pb::AckResponse response;
        request.mutable_auth_info()->set_username("hello1");
        request.mutable_auth_info()->set_password("world1");

        common::SynchronizedClosure rpc_waiter;
        stub.revoke_role_privileges(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_PRIVILEGE_DENIED);
    }
    // invalid role name
    {
        pb::RevokeRolePrivilegesRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_role("");

        common::SynchronizedClosure rpc_waiter;
        stub.revoke_role_privileges(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_INVALID_ROLE_NAME);
    }
    // role not exist
    {
        pb::RevokeRolePrivilegesRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_role("not_exist");

        common::SynchronizedClosure rpc_waiter;
        stub.revoke_role_privileges(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_ROLE_NOT_EXIST);
    }
    // cannot revoke any privilege from ADMIN
    {
        pb::RevokeRolePrivilegesRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_role("ADMIN");

        common::SynchronizedClosure rpc_waiter;
        stub.revoke_role_privileges(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_REJECTED);
    }
    // rejected by bloodline
    {
        pb::GrantRolePrivilegesRequest request;
        pb::AckResponse response;
        fill_auth(request, "test_user", "anything");
        request.set_role("role1");

        common::SynchronizedClosure rpc_waiter;
        stub.grant_role_privileges(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_REJECTED);
    }
    // no any privilege tuple
    {
        pb::RevokeRolePrivilegesRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_role("test_role");

        common::SynchronizedClosure rpc_waiter;
        stub.revoke_role_privileges(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_INVALID_ARGUMENT);
    }
    // invalid rbac object
    {
        pb::RevokeRolePrivilegesRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_role("test_role");
        request.add_priv_tuples(); // set nothing

        common::SynchronizedClosure rpc_waiter;
        stub.revoke_role_privileges(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_INVALID_RBAC_OBJECT);
    }
    // db not exist in rbac object
    {
        pb::RevokeRolePrivilegesRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_role("test_role");
        auto* priv_tuple = request.add_priv_tuples();
        priv_tuple->mutable_object()->set_database("not_exist");
        priv_tuple->mutable_object()->set_table("*");

        common::SynchronizedClosure rpc_waiter;
        stub.revoke_role_privileges(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_DB_NOT_EXIST);
    }
    // table not exist in rbac object
    {
        pb::RevokeRolePrivilegesRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_role("test_role");
        auto* priv_tuple = request.add_priv_tuples();
        priv_tuple->mutable_object()->set_database("db1");
        priv_tuple->mutable_object()->set_table("not_exist");

        common::SynchronizedClosure rpc_waiter;
        stub.revoke_role_privileges(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_TABLE_NOT_EXIST);
    }
    // invalid privilege
    {
        pb::RevokeRolePrivilegesRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_role("test_role");
        auto* priv_tuple = request.add_priv_tuples();
        priv_tuple->mutable_object()->set_database("*");
        priv_tuple->mutable_object()->set_table("*");
        priv_tuple->add_privileges("INSERT");
        priv_tuple->add_privileges("INVALID");

        common::SynchronizedClosure rpc_waiter;
        stub.revoke_role_privileges(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_PRIVILEGE_NOT_FOUND);
    }
    // success
    {
        pb::RevokeRolePrivilegesRequest request;
        pb::AckResponse response;
        fill_auth(request);
        request.set_role("test_role");
        auto* priv_tuple = request.add_priv_tuples();
        priv_tuple->mutable_object()->set_database("*");
        priv_tuple->mutable_object()->set_table("*");
        priv_tuple->add_privileges("DELETE");
        priv_tuple->add_privileges("TABLE_READWRITE");
        priv_tuple = request.add_priv_tuples();
        priv_tuple->mutable_object()->set_database("*");
        priv_tuple->mutable_object()->set_table("*");
        priv_tuple->add_privileges("TABLE_ALL");
        priv_tuple->add_privileges("TABLE_ALL"); // dup
        priv_tuple = request.add_priv_tuples();
        priv_tuple->mutable_object()->set_database("db1");
        priv_tuple->mutable_object()->set_table("*");
        priv_tuple->add_privileges("SYSTEM_ALL");
        priv_tuple = request.add_priv_tuples();
        priv_tuple->mutable_object()->set_database("db1");
        priv_tuple->mutable_object()->set_table("table1");
        priv_tuple->add_privileges("TABLE_CONTROL");

        common::SynchronizedClosure rpc_waiter;
        stub.revoke_role_privileges(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), OK);
    }
}

TEST_F(DatabaseServiceTests, show_user_privileges) {
    MasterStub stub;
    // not leader
    {
        g_is_leader = false;
        pb::ShowUserPrivilegesRequest request;
        pb::ShowUserPrivilegesResponse response;
        fill_auth(request);

        common::SynchronizedClosure rpc_waiter;
        stub.show_user_privileges(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_NOT_PRIMARY);
        g_is_leader = true;
    }
    // authenticate failed
    {
        pb::ShowUserPrivilegesRequest request;
        pb::ShowUserPrivilegesResponse response;
        request.mutable_auth_info()->set_username(S_MOCHOW_ROOT_USERNAME);
        request.mutable_auth_info()->set_password("");

        common::SynchronizedClosure rpc_waiter;
        stub.show_user_privileges(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_AUTHENTICATE_FAILED);
    }
    // privilege denied
    {
        pb::ShowUserPrivilegesRequest request;
        pb::ShowUserPrivilegesResponse response;
        request.mutable_auth_info()->set_username("hello1");
        request.mutable_auth_info()->set_password("world1");
        request.set_username("root");

        common::SynchronizedClosure rpc_waiter;
        stub.show_user_privileges(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_PRIVILEGE_DENIED);
    }
    // user not exist
    {
        pb::ShowUserPrivilegesRequest request;
        pb::ShowUserPrivilegesResponse response;
        fill_auth(request);
        request.set_username("not_exist");

        common::SynchronizedClosure rpc_waiter;
        stub.show_user_privileges(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_USER_NOT_EXIST);
    }
    // succ with having privilege
    {
        auto root = g_rbac_manager->get_user("root");
        root->grant_privileges_by_bitset("A", "T1", *g_privilege_bitset_TABLE_ALL);

        pb::ShowUserPrivilegesRequest request;
        pb::ShowUserPrivilegesResponse response;
        fill_auth(request);
        request.set_username("root");

        common::SynchronizedClosure rpc_waiter;
        stub.show_user_privileges(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), OK);
        ASSERT_TRUE(response.username() == "root");
        ASSERT_EQ(response.roles_size(), 1);
        const auto& role = response.roles(0);
        ASSERT_TRUE(role.role() == "ADMIN");
        ASSERT_EQ(role.priv_tuples_size(), 1);
        const auto& role_priv_tuple = role.priv_tuples(0);
        ASSERT_TRUE(role_priv_tuple.object().database() == "*");
        ASSERT_TRUE(role_priv_tuple.object().table() == "*");
        ASSERT_TRUE(role_priv_tuple.priv_bitset() == g_privilege_bitset_ALL->to_string());
        ASSERT_EQ(response.priv_tuples_size(), 2);
        const auto& priv_tuple = response.priv_tuples(0);
        ASSERT_TRUE(priv_tuple.object().database() == "*");
        ASSERT_TRUE(priv_tuple.object().table() == "*");
        PrivilegeBitset usage_bitset;
        usage_bitset.set(0);
        ASSERT_TRUE(priv_tuple.priv_bitset() == usage_bitset.to_string());
        const auto& priv_tuple2 = response.priv_tuples(1);
        ASSERT_TRUE(priv_tuple2.object().database() == "A");
        ASSERT_TRUE(priv_tuple2.object().table() == "T1");
        ASSERT_TRUE(priv_tuple2.priv_bitset() == g_privilege_bitset_TABLE_ALL->to_string());
    }
    // succ with having bloodline
    {
        pb::ShowUserPrivilegesRequest request;
        pb::ShowUserPrivilegesResponse response;
        request.mutable_auth_info()->set_username("test_user_2");
        request.mutable_auth_info()->set_password("anything");
        request.set_username("test_user_3");

        common::SynchronizedClosure rpc_waiter;
        stub.show_user_privileges(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), OK);
        ASSERT_TRUE(response.username() == "test_user_3");
    }
}

TEST_F(DatabaseServiceTests, show_role_privileges) {
    MasterStub stub;
    // not leader
    {
        g_is_leader = false;
        pb::ShowRolePrivilegesRequest request;
        pb::ShowRolePrivilegesResponse response;
        fill_auth(request);

        common::SynchronizedClosure rpc_waiter;
        stub.show_role_privileges(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_NOT_PRIMARY);
        g_is_leader = true;
    }
    // authenticate failed
    {
        pb::ShowRolePrivilegesRequest request;
        pb::ShowRolePrivilegesResponse response;
        request.mutable_auth_info()->set_username(S_MOCHOW_ROOT_USERNAME);
        request.mutable_auth_info()->set_password("");

        common::SynchronizedClosure rpc_waiter;
        stub.show_role_privileges(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_AUTHENTICATE_FAILED);
    }
    // privilege denied
    {
        pb::ShowRolePrivilegesRequest request;
        pb::ShowRolePrivilegesResponse response;
        request.mutable_auth_info()->set_username("hello1");
        request.mutable_auth_info()->set_password("world1");
        request.set_role("ADMIN");

        common::SynchronizedClosure rpc_waiter;
        stub.show_role_privileges(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_PRIVILEGE_DENIED);
    }
    // role not exist
    {
        pb::ShowRolePrivilegesRequest request;
        pb::ShowRolePrivilegesResponse response;
        fill_auth(request);
        request.set_role("not_exist");

        common::SynchronizedClosure rpc_waiter;
        stub.show_role_privileges(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_ROLE_NOT_EXIST);
    }
    // succ
    {
        pb::ShowRolePrivilegesRequest request;
        pb::ShowRolePrivilegesResponse response;
        fill_auth(request);
        request.set_role("ADMIN");

        common::SynchronizedClosure rpc_waiter;
        stub.show_role_privileges(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), OK);
        ASSERT_TRUE(response.role() == "ADMIN");
        ASSERT_EQ(response.usernames_size(), 2);
        ASSERT_TRUE(response.usernames(0) == S_MOCHOW_SYSTEM_USERNAME);
        ASSERT_TRUE(response.usernames(1) == S_MOCHOW_ROOT_USERNAME);
        ASSERT_EQ(response.priv_tuples_size(), 1);
        const auto& priv_tuple = response.priv_tuples(0);
        ASSERT_TRUE(priv_tuple.object().database() == "*");
        ASSERT_TRUE(priv_tuple.object().table() == "*");
        ASSERT_TRUE(priv_tuple.priv_bitset() == g_privilege_bitset_ALL->to_string());
    }
}

TEST_F(DatabaseServiceTests, select_user_by_privileges) {
    MasterStub stub;
    // not leader
    {
        g_is_leader = false;
        pb::SelectUserRequest request;
        pb::SelectUserResponse response;
        fill_auth(request);

        common::SynchronizedClosure rpc_waiter;
        stub.select_user(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_NOT_PRIMARY);
        g_is_leader = true;
    }
    // authenticate failed
    {
        pb::SelectUserRequest request;
        pb::SelectUserResponse response;
        request.mutable_auth_info()->set_username(S_MOCHOW_ROOT_USERNAME);
        request.mutable_auth_info()->set_password("");

        common::SynchronizedClosure rpc_waiter;
        stub.select_user(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_AUTHENTICATE_FAILED);
    }
    // privilege denied
    {
        pb::SelectUserRequest request;
        pb::SelectUserResponse response;
        request.mutable_auth_info()->set_username("hello1");
        request.mutable_auth_info()->set_password("world1");

        common::SynchronizedClosure rpc_waiter;
        stub.select_user(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_PRIVILEGE_DENIED);
    }
    // invalid rbac object
    {
        pb::SelectUserRequest request;
        pb::SelectUserResponse response;
        fill_auth(request);
        auto* priv_tuple = request.add_priv_tuples(); // set nothing

        common::SynchronizedClosure rpc_waiter;
        stub.select_user(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_INVALID_RBAC_OBJECT);
    }
    // privilege not found
    {
        pb::SelectUserRequest request;
        pb::SelectUserResponse response;
        fill_auth(request);
        auto* priv_tuple = request.add_priv_tuples();
        priv_tuple->mutable_object()->set_database("*");
        priv_tuple->mutable_object()->set_table("*");
        priv_tuple->add_privileges("NOT_EXIST");

        common::SynchronizedClosure rpc_waiter;
        stub.select_user(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_PRIVILEGE_NOT_FOUND);
    }
    // role not exist
    {
        pb::SelectUserRequest request;
        pb::SelectUserResponse response;
        fill_auth(request);
        auto* priv_tuple = request.add_priv_tuples();
        priv_tuple->mutable_object()->set_database("*");
        priv_tuple->mutable_object()->set_table("*");
        priv_tuple->add_privileges("INSERT");
        request.add_roles("not_exist");

        common::SynchronizedClosure rpc_waiter;
        stub.select_user(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_ROLE_NOT_EXIST);
    }
    // succ with emtpy filters
    {
        pb::SelectUserRequest request;
        pb::SelectUserResponse response;
        fill_auth(request);

        common::SynchronizedClosure rpc_waiter;
        stub.select_user(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), OK);
        ASSERT_EQ(response.usernames_size(), 6);
        ASSERT_TRUE(response.usernames(0) == S_MOCHOW_SYSTEM_USERNAME);
        ASSERT_TRUE(response.usernames(1) == "hello1");
        ASSERT_TRUE(response.usernames(2) == S_MOCHOW_ROOT_USERNAME);
        ASSERT_TRUE(response.usernames(3) == "test_user");
        ASSERT_TRUE(response.usernames(4) == "test_user_2");
        ASSERT_TRUE(response.usernames(5) == "test_user_3");
    }
    // succ with role filter
    {
        pb::SelectUserRequest request;
        pb::SelectUserResponse response;
        fill_auth(request);
        request.add_roles("ADMIN");

        common::SynchronizedClosure rpc_waiter;
        stub.select_user(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), OK);
        ASSERT_EQ(response.usernames_size(), 2);
        ASSERT_TRUE(response.usernames(0) == S_MOCHOW_SYSTEM_USERNAME);
        ASSERT_TRUE(response.usernames(1) == S_MOCHOW_ROOT_USERNAME);
    }
    // succ with privilege filter
    {
        pb::SelectUserRequest request;
        pb::SelectUserResponse response;
        fill_auth(request);
        auto* priv_tuple = request.add_priv_tuples();
        priv_tuple->mutable_object()->set_database("db123");
        priv_tuple->mutable_object()->set_table("*");
        priv_tuple->add_privileges("QUERY");

        common::SynchronizedClosure rpc_waiter;
        stub.select_user(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), OK);
        ASSERT_EQ(response.usernames_size(), 2);
        ASSERT_TRUE(response.usernames(0) == S_MOCHOW_SYSTEM_USERNAME);
        ASSERT_TRUE(response.usernames(1) == S_MOCHOW_ROOT_USERNAME);
    }
    // succ with privilege filter
    {
        pb::SelectUserRequest request;
        pb::SelectUserResponse response;
        fill_auth(request);
        auto* priv_tuple = request.add_priv_tuples();
        priv_tuple->mutable_object()->set_database("db1");
        priv_tuple->mutable_object()->set_table("*");
        priv_tuple->add_privileges("TABLE_CONTROL");

        common::SynchronizedClosure rpc_waiter;
        stub.select_user(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), OK);
        ASSERT_EQ(response.usernames_size(), 3);
        ASSERT_TRUE(response.usernames(0) == S_MOCHOW_SYSTEM_USERNAME);
        ASSERT_TRUE(response.usernames(1) == S_MOCHOW_ROOT_USERNAME);
        ASSERT_TRUE(response.usernames(2) == "test_user");
    }
    // succ with privilege filter
    {
        pb::SelectUserRequest request;
        pb::SelectUserResponse response;
        fill_auth(request);
        auto* priv_tuple = request.add_priv_tuples();
        priv_tuple->mutable_object()->set_database("db1");
        priv_tuple->mutable_object()->set_table("*");
        priv_tuple->add_privileges("TABLE_READWRITE");

        common::SynchronizedClosure rpc_waiter;
        stub.select_user(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), OK);
        ASSERT_EQ(response.usernames_size(), 4);
        ASSERT_TRUE(response.usernames(0) == S_MOCHOW_SYSTEM_USERNAME);
        ASSERT_TRUE(response.usernames(1) == S_MOCHOW_ROOT_USERNAME);
        ASSERT_TRUE(response.usernames(2) == "test_user");
        ASSERT_TRUE(response.usernames(3) == "test_user_2");
    }
    // succ with privilege filter
    {
        pb::SelectUserRequest request;
        pb::SelectUserResponse response;
        fill_auth(request);
        auto* priv_tuple = request.add_priv_tuples();
        priv_tuple->mutable_object()->set_database("db1");
        priv_tuple->mutable_object()->set_table("table1");
        priv_tuple->add_privileges("TABLE_READONLY");

        common::SynchronizedClosure rpc_waiter;
        stub.select_user(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), OK);
        ASSERT_EQ(response.usernames_size(), 5);
        ASSERT_TRUE(response.usernames(0) == S_MOCHOW_SYSTEM_USERNAME);
        ASSERT_TRUE(response.usernames(1) == S_MOCHOW_ROOT_USERNAME);
        ASSERT_TRUE(response.usernames(2) == "test_user");
        ASSERT_TRUE(response.usernames(3) == "test_user_2");
        ASSERT_TRUE(response.usernames(4) == "test_user_3");
    }
    // succ with privilege filter
    {
        pb::SelectUserRequest request;
        pb::SelectUserResponse response;
        fill_auth(request);
        auto* priv_tuple = request.add_priv_tuples();
        priv_tuple->mutable_object()->set_database("db1");
        priv_tuple->mutable_object()->set_table("*");
        priv_tuple->add_privileges("SELECT");
        priv_tuple->add_privileges("SEARCH");
        priv_tuple = request.add_priv_tuples();
        priv_tuple->mutable_object()->set_database("db1");
        priv_tuple->mutable_object()->set_table("table1");
        priv_tuple->add_privileges("INSERT");

        common::SynchronizedClosure rpc_waiter;
        stub.select_user(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), OK);
        ASSERT_EQ(response.usernames_size(), 4);
        ASSERT_TRUE(response.usernames(0) == S_MOCHOW_SYSTEM_USERNAME);
        ASSERT_TRUE(response.usernames(1) == S_MOCHOW_ROOT_USERNAME);
        ASSERT_TRUE(response.usernames(2) == "test_user");
        ASSERT_TRUE(response.usernames(3) == "test_user_2");
    }
    // succ with all kind of filters (role + privilege)
    {
        pb::SelectUserRequest request;
        pb::SelectUserResponse response;
        fill_auth(request);
        auto* priv_tuple = request.add_priv_tuples();
        priv_tuple->mutable_object()->set_database("db1");
        priv_tuple->mutable_object()->set_table("*");
        priv_tuple->add_privileges("INSERT");
        request.add_roles("role1"); // role1 is not granted to any user

        common::SynchronizedClosure rpc_waiter;
        stub.select_user(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), OK);
        ASSERT_EQ(response.usernames_size(), 0);
    }
}

TEST_F(DatabaseServiceTests, select_role_by_privileges) {
    MasterStub stub;
    // not leader
    {
        g_is_leader = false;
        pb::SelectRoleRequest request;
        pb::SelectRoleResponse response;
        fill_auth(request);

        common::SynchronizedClosure rpc_waiter;
        stub.select_role(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_NOT_PRIMARY);
        g_is_leader = true;
    }
    // authenticate failed
    {
        pb::SelectRoleRequest request;
        pb::SelectRoleResponse response;
        request.mutable_auth_info()->set_username(S_MOCHOW_ROOT_USERNAME);
        request.mutable_auth_info()->set_password("");

        common::SynchronizedClosure rpc_waiter;
        stub.select_role(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_AUTHENTICATE_FAILED);
    }
    // privilege denied
    {
        pb::SelectRoleRequest request;
        pb::SelectRoleResponse response;
        request.mutable_auth_info()->set_username("hello1");
        request.mutable_auth_info()->set_password("world1");

        common::SynchronizedClosure rpc_waiter;
        stub.select_role(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_PRIVILEGE_DENIED);
    }
    // invalid rbac object
    {
        pb::SelectRoleRequest request;
        pb::SelectRoleResponse response;
        fill_auth(request);
        auto* priv_tuple = request.add_priv_tuples(); // set nothing

        common::SynchronizedClosure rpc_waiter;
        stub.select_role(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_INVALID_RBAC_OBJECT);
    }
    // privilege not found
    {
        pb::SelectRoleRequest request;
        pb::SelectRoleResponse response;
        fill_auth(request);
        auto* priv_tuple = request.add_priv_tuples();
        priv_tuple->mutable_object()->set_database("*");
        priv_tuple->mutable_object()->set_table("*");
        priv_tuple->add_privileges("NOT_EXIST");

        common::SynchronizedClosure rpc_waiter;
        stub.select_role(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), ERR_PRIVILEGE_NOT_FOUND);
    }
    // succ with emtpy filters
    {
        pb::SelectRoleRequest request;
        pb::SelectRoleResponse response;
        fill_auth(request);

        common::SynchronizedClosure rpc_waiter;
        stub.select_role(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), OK);
        ASSERT_EQ(response.roles_size(), 3);
        ASSERT_TRUE(response.roles(0) == "ADMIN");
        ASSERT_TRUE(response.roles(1) == "role1");
        ASSERT_TRUE(response.roles(2) == "test_role");
    }
    // succ with privilege filter
    {
        pb::SelectRoleRequest request;
        pb::SelectRoleResponse response;
        fill_auth(request);
        auto* priv_tuple = request.add_priv_tuples();
        priv_tuple->mutable_object()->set_database("*");
        priv_tuple->mutable_object()->set_table("*");
        priv_tuple->add_privileges("INSERT");
        priv_tuple->add_privileges("ALL");

        common::SynchronizedClosure rpc_waiter;
        stub.select_role(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), OK);
        ASSERT_EQ(response.roles_size(), 1);
        ASSERT_TRUE(response.roles(0) == "ADMIN");
    }
    // succ with privilege filter
    {
        pb::SelectRoleRequest request;
        pb::SelectRoleResponse response;
        fill_auth(request);
        auto* priv_tuple = request.add_priv_tuples();
        priv_tuple->mutable_object()->set_database("db123");
        priv_tuple->mutable_object()->set_table("*");
        priv_tuple->add_privileges("QUERY");

        common::SynchronizedClosure rpc_waiter;
        stub.select_role(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), OK);
        ASSERT_EQ(response.roles_size(), 1);
        ASSERT_TRUE(response.roles(0) == "ADMIN");
    }
    // succ with privilege filter
    {
        pb::SelectRoleRequest request;
        pb::SelectRoleResponse response;
        fill_auth(request);
        auto* priv_tuple = request.add_priv_tuples();
        priv_tuple->mutable_object()->set_database("db1");
        priv_tuple->mutable_object()->set_table("*");
        priv_tuple->add_privileges("INSERT");

        common::SynchronizedClosure rpc_waiter;
        stub.select_role(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), OK);
        ASSERT_EQ(response.roles_size(), 2);
        ASSERT_TRUE(response.roles(0) == "ADMIN");
        ASSERT_TRUE(response.roles(1) == "test_role");
    }
    // succ with privilege filter
    {
        pb::SelectRoleRequest request;
        pb::SelectRoleResponse response;
        fill_auth(request);
        auto* priv_tuple = request.add_priv_tuples();
        priv_tuple->mutable_object()->set_database("db1");
        priv_tuple->mutable_object()->set_table("*");
        priv_tuple->add_privileges("QUERY");
        priv_tuple->add_privileges("TABLE_READWRITE");

        common::SynchronizedClosure rpc_waiter;
        stub.select_role(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), OK);
        ASSERT_EQ(response.roles_size(), 2);
        ASSERT_TRUE(response.roles(0) == "ADMIN");
        ASSERT_TRUE(response.roles(1) == "test_role");
    }
    // succ with privilege filter
    {
        pb::SelectRoleRequest request;
        pb::SelectRoleResponse response;
        fill_auth(request);
        auto* priv_tuple = request.add_priv_tuples();
        priv_tuple->mutable_object()->set_database("db1");
        priv_tuple->mutable_object()->set_table("*");
        priv_tuple->add_privileges("SYSTEM_ALL");

        common::SynchronizedClosure rpc_waiter;
        stub.select_role(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), OK);
        ASSERT_EQ(response.roles_size(), 1);
        ASSERT_TRUE(response.roles(0) == "ADMIN");
    }
    // succ with privilege filter
    {
        pb::SelectRoleRequest request;
        pb::SelectRoleResponse response;
        fill_auth(request);
        auto* priv_tuple = request.add_priv_tuples();
        priv_tuple->mutable_object()->set_database("db1");
        priv_tuple->mutable_object()->set_table("*");
        priv_tuple->add_privileges("TABLE_READWRITE");
        priv_tuple = request.add_priv_tuples();
        priv_tuple->mutable_object()->set_database("db1");
        priv_tuple->mutable_object()->set_table("table1");
        priv_tuple->add_privileges("TABLE_READWRITE");

        common::SynchronizedClosure rpc_waiter;
        stub.select_role(get_master_addr(), &request, &response, &rpc_waiter, &_rpc_options);
        rpc_waiter.wait();
        EXPECT_EQ(response.status().code(), OK);
        ASSERT_EQ(response.roles_size(), 2);
        ASSERT_TRUE(response.roles(0) == "ADMIN");
        ASSERT_TRUE(response.roles(1) == "test_role");
    }
}

}

int main(int argc, char** argv) {
    ::testing::InitGoogleMock(&argc, argv);
    int ret = RUN_ALL_TESTS();
    return ret;
}
