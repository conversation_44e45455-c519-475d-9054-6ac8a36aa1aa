#include <stdlib.h>
#include <gtest/gtest.h>

#include "core/test/tools/random_generator.h"
#include "third-party/hnswlib/src/simd/hook.h"
#include "third-party/hnswlib/src/simd/distances_ref.h"

#if __x86_64__
#include "third-party/hnswlib/src/simd/distances_avx.h"
#include "third-party/hnswlib/src/simd/distances_avx512.h"
#include "third-party/hnswlib/src/simd/distances_sse.h"
#endif

#if __aarch64__
// TODO(gaokun02): Implement later
#endif

namespace mochow::vindex {

static constexpr float err = 1e-4F;

static constexpr bool equal(float a, float b) {
    return std::abs(a - b) <= err;
}

class InstructionSetArchitectureTest : public ::testing::Test {};

// TEST_F(InstructionSetArchitectureTest, test_fvec_sum) {
//     float standard_sum = .0f;
//     size_t d = 63UL;
//     float* x = mochow::common::random_float_vector(d);
//     for (size_t i = 0; i < d; ++i) {
//         standard_sum += x[i];
//     }
// #if __x86_64__
//     if (faiss::cpu_support_avx512()) {
//         float avx512_sum = faiss::fvec_sum_avx512(x, d);
//         EXPECT_TRUE(equal(standard_sum, avx512_sum));
//     }

//     if (faiss::cpu_support_avx2()) {
//         float avx256_sum = faiss::fvec_sum_avx256(x, d);
//         EXPECT_TRUE(equal(standard_sum, avx256_sum));
//     }

//     if (faiss::cpu_support_sse4_2()) {
//         float sse_sum = faiss::fvec_sum_sse(x, d);
//         EXPECT_TRUE(equal(standard_sum, sse_sum));
//     }

//     float ref_sum = faiss::fvec_sum_ref(x, d);
//     EXPECT_EQ(standard_sum, ref_sum);
// #endif

// #if __aarch64__
// // TODO(gaokun02): Implement later
// #endif
//     delete[] x;
// }

// TEST_F(InstructionSetArchitectureTest, test_pq_dist) {
//     float standard_sum = .0f;
//     size_t nsq = 8;
//     uint8_t code[nsq] = {0U};
//     uint32_t offset = 256U;
//     float* x = new float[offset * nsq];

//     for (int i = 0; i < nsq; ++i) {
//         code[i] = mochow::common::random_uint8();
//     }

//     for (int i = 0; i < offset * nsq; ++i) {
//         x[i] = mochow::common::random_float(0.0f, 10.0f);
//     }

//     standard_sum = faiss::fvec_pq_dist_ref(code, x, nsq, offset);
    
// #if __x86_64__
//     if (faiss::cpu_support_avx512()) {
//         float avx512_sum = faiss::fvec_pq_dist_avx512(code, x, nsq, offset);
//         EXPECT_TRUE(equal(standard_sum, avx512_sum));
//     }

//     if (faiss::cpu_support_avx2()) {
//         float avx256_sum = faiss::fvec_pq_dist_avx(code, x, nsq, offset);
//         EXPECT_TRUE(equal(standard_sum, avx256_sum));
//     }

//     if (faiss::cpu_support_sse4_2()) {
//         float sse_sum = faiss::fvec_pq_dist_sse(code, x, nsq, offset);
//         EXPECT_TRUE(equal(standard_sum, sse_sum));
//     }
// #endif

// #if __aarch64__
// // TODO(gaokun02): Implement later
// #endif

//     delete[] x;
// }

// TEST_F(InstructionSetArchitectureTest, test_fvec_sub) {
//     size_t d = 63UL;
//     float standard_result[d] = {0.0f};
//     float x[d], y[d], test_result[d];
//     for (size_t i = 0; i < d; ++i) {
//         x[i] = mochow::common::random_float(-100.0f, 100.0f);
//         y[i] = mochow::common::random_float(-100.0f, 100.0f);
//         standard_result[i] = x[i] - y[i];
//     }
// #if __x86_64__
//     if (faiss::cpu_support_avx512()) {
//         faiss::fvec_sub_avx512(x, y, test_result, d);
//         for (size_t i = 0; i < d; ++i) {
//             EXPECT_TRUE(equal(standard_result[i], test_result[i]));
//         }
//     }

//     if (faiss::cpu_support_avx2()) {
//         faiss::fvec_sub_avx256(x, y, test_result, d);
//         for (size_t i = 0; i < d; ++i) {
//             EXPECT_TRUE(equal(standard_result[i], test_result[i]));
//         }
//     }

//     if (faiss::cpu_support_sse4_2()) {
//         faiss::fvec_sub_sse(x, y, test_result, d);
//         for (size_t i = 0; i < d; ++i) {
//             EXPECT_TRUE(equal(standard_result[i], test_result[i]));
//         }
//     }

//     faiss::fvec_sub_ref(x, y, test_result, d);
//     for (size_t i = 0; i < d; ++i) {
//         EXPECT_EQ(standard_result[i], test_result[i]);
//     }

// #endif

// #if __arm__
// #endif
// }

TEST_F(InstructionSetArchitectureTest, test_fvec_hnswsq_decode) {
    // hnswlib::fvec_hnswsq_decode_sse
    int d = 5;
    int bit = 5;
    float result1[d], result2[d];
    float diff[d] = {0.0f, 1.0f, 2.0f, 3.0f, 4.0f};
    float min[d] = {0.0f};
    uint32_t xn = 100;
    uint32_t code_size = (xn * d * bit + 7) / 8 + 4;
    uint8_t* code = (uint8_t*)calloc(code_size, sizeof(uint8_t));
    for (int i = 0; i < code_size - 4; ++i) {
        code[i] = mochow::common::random_uint8();
    }
#if __x86_64__
    for (uint32_t i = 0; i < xn; ++i) {
        hnswlib::fvec_hnswsq_decode_ref(i, d, bit, result1, diff, min, code);
        if (faiss::cpu_support_sse4_2()) {
            hnswlib::fvec_hnswsq_decode_sse(i, d, bit, result2, diff, min, code);
            for (int j = 0; j < d; ++j) {
                EXPECT_TRUE(equal(result1[j], result2[j]));
            }
        }
    }
#endif

#if __arm__

#endif
    free(code);
}

TEST_F(InstructionSetArchitectureTest, test_fvec_sq_8bit_L2_dist) {
    // float fvec_sq_8bit_L2_dist_ref(uint8_t *code, const float *x, const float *min, const float *diff, size_t d);
    size_t d = 31UL;
    size_t adjust_size = (d * sizeof(float) + 63UL) / 64UL * 64UL;
    float *min = (float *)aligned_alloc(64, adjust_size);
    float *diff = (float *)aligned_alloc(64, adjust_size);
    float *query = (float *)malloc(d * sizeof(float));
    uint8_t *code = (uint8_t *)malloc(d * sizeof(uint8_t));

    for (size_t i = 0UL; i < d; ++i) {
        query[i] = mochow::common::random_float(-1.0f, 1.0f);
        code[i] = mochow::common::random_uint8() % 8;
        min[i] = mochow::common::random_float(0.0f, 1.0f);
        diff[i] = mochow::common::random_float(0.0f, 1.0f);
    }
    float standard_result = hnswlib::fvec_sq_8bit_L2_dist_ref(code, query, min, diff, d);
#if __x86_64__
    if (faiss::cpu_support_sse4_2()) {
        float test_result = hnswlib::fvec_sq_8bit_L2_dist_sse(code, query, min, diff, d);
        EXPECT_TRUE(equal(standard_result, test_result));
    }

    if (faiss::cpu_support_avx2()) {
        float test_result = hnswlib::fvec_sq_8bit_L2_dist_avx(code, query, min, diff, d);
        EXPECT_TRUE(equal(standard_result, test_result));
    }

    if (faiss::cpu_support_avx512()) {
        float test_result = hnswlib::fvec_sq_8bit_L2_dist_avx512(code, query, min, diff, d);
        EXPECT_TRUE(equal(standard_result, test_result));
    }
#endif

#if __arm__

#endif
    free(min);
    free(diff);
    free(query);
    free(code);
}

TEST_F(InstructionSetArchitectureTest, test_fvec_sq_8bit_IP_dist) {
    // float fvec_sq_8bit_IP_dist_ref(uint8_t *code, const float *x, const float *min, const float *diff, size_t d);
    size_t d = 31UL;
    size_t adjust_size = (d * sizeof(float) + 63UL) / 64UL * 64UL;
    float *min = (float *)aligned_alloc(64, adjust_size);
    float *diff = (float *)aligned_alloc(64, adjust_size);
    float *query = (float *)malloc(d * sizeof(float));
    uint8_t *code = (uint8_t *)malloc(d * sizeof(uint8_t));

    for (size_t i = 0UL; i < d; ++i) {
        query[i] = mochow::common::random_float(-1.0f, 1.0f);
        code[i] = mochow::common::random_uint8() % 8;
        min[i] = mochow::common::random_float(0.0f, 1.0f);
        diff[i] = mochow::common::random_float(0.0f, 1.0f);
    }
    float standard_result = hnswlib::fvec_sq_8bit_IP_dist_ref(code, query, min, diff, d);
#if __x86_64__
    if (faiss::cpu_support_sse4_2()) {
        float test_result = hnswlib::fvec_sq_8bit_IP_dist_sse(code, query, min, diff, d);
        EXPECT_TRUE(equal(standard_result, test_result));
    }

    if (faiss::cpu_support_avx2()) {
        float test_result = hnswlib::fvec_sq_8bit_IP_dist_avx(code, query, min, diff, d);
        EXPECT_TRUE(equal(standard_result, test_result));
    }

    if (faiss::cpu_support_avx512()) {
        float test_result = hnswlib::fvec_sq_8bit_IP_dist_avx512(code, query, min, diff, d);
        EXPECT_TRUE(equal(standard_result, test_result));
    }
#endif

#if __arm__

#endif
    free(min);
    free(diff);
    free(query);
    free(code);
}

TEST_F(InstructionSetArchitectureTest, test_fvec_sq_16bit_L2_dist) {
    // float fvec_sq_16bit_L2_dist_ref(uint16_t *code, const float *x, const float *min, const float *diff, size_t d);
    size_t d = 31UL;
    size_t adjust_size = (d * sizeof(float) + 63UL) / 64UL * 64UL;
    float *min = (float *)aligned_alloc(64, adjust_size);
    float *diff = (float *)aligned_alloc(64, adjust_size);
    float *query = (float *)malloc(d * sizeof(float));
    uint16_t *code = (uint16_t *)malloc(d * sizeof(uint16_t));

    for (size_t i = 0UL; i < d; ++i) {
        query[i] = mochow::common::random_float(0.0f, 0.5f);
        code[i] = mochow::common::random_uint16() % 8;
        min[i] = mochow::common::random_float(0.0f, 0.5f);
        diff[i] = mochow::common::random_float(0.0f, 0.1f);
    }
    float standard_result = hnswlib::fvec_sq_16bit_L2_dist_ref(code, query, min, diff, d);
#if __x86_64__
    if (faiss::cpu_support_sse4_2()) {
        float test_result = hnswlib::fvec_sq_16bit_L2_dist_sse(code, query, min, diff, d);
        EXPECT_TRUE(equal(standard_result, test_result));
    }

    if (faiss::cpu_support_avx2()) {
        float test_result = hnswlib::fvec_sq_16bit_L2_dist_avx(code, query, min, diff, d);
        EXPECT_TRUE(equal(standard_result, test_result));
    }

    if (faiss::cpu_support_avx512()) {
        float test_result = hnswlib::fvec_sq_16bit_L2_dist_avx512(code, query, min, diff, d);
        EXPECT_TRUE(equal(standard_result, test_result));
    }
#endif

#if __arm__

#endif
    free(min);
    free(diff);
    free(query);
    free(code);
}

TEST_F(InstructionSetArchitectureTest, test_fvec_sq_16bit_IP_dist) {
    // float fvec_sq_16bit_IP_dist_ref(uint16_t *code, const float *x, const float *min, const float *diff, size_t d);
    size_t d = 31UL;
    size_t adjust_size = (d * sizeof(float) + 63UL) / 64UL * 64UL;
    float *min = (float *)aligned_alloc(64, adjust_size);
    float *diff = (float *)aligned_alloc(64, adjust_size);
    float *query = (float *)malloc(d * sizeof(float));
    uint16_t *code = (uint16_t *)malloc(d * sizeof(uint16_t));

    for (size_t i = 0UL; i < d; ++i) {
        query[i] = mochow::common::random_float(0.0f, 0.5f);
        code[i] = mochow::common::random_uint16() % 8;
        min[i] = mochow::common::random_float(0.0f, 0.5f);
        diff[i] = mochow::common::random_float(0.0f, 0.1f);
    }
    float standard_result = hnswlib::fvec_sq_16bit_IP_dist_ref(code, query, min, diff, d);
#if __x86_64__
    if (faiss::cpu_support_sse4_2()) {
        float test_result = hnswlib::fvec_sq_16bit_IP_dist_sse(code, query, min, diff, d);
        EXPECT_TRUE(equal(standard_result, test_result));
    }

    if (faiss::cpu_support_avx2()) {
        float test_result = hnswlib::fvec_sq_16bit_IP_dist_avx(code, query, min, diff, d);
        EXPECT_TRUE(equal(standard_result, test_result));
    }

    if (faiss::cpu_support_avx512()) {
        float test_result = hnswlib::fvec_sq_16bit_IP_dist_avx512(code, query, min, diff, d);
        EXPECT_TRUE(equal(standard_result, test_result));
    }
#endif

#if __arm__

#endif
    free(min);
    free(diff);
    free(query);
    free(code);
}
}



int main(int argc, char* argv[]) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}
