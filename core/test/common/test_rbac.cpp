/**
 * MOCHOW - A Vector Database System.
 * Copyright (c) 2024 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON> (<EMAIL>)
 * Date: 2024/06/20
 * Desciption: Unittests for RBAC
 *
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "core/src/common/meta/privilege_tree.h"
#include "core/src/common/meta/rbac.h"

namespace mochow::common {

class RBACTests : public ::testing::Test {
};

TEST(RBACTests, test_role) {
    Role role("ADMIN", "root");
    ASSERT_TRUE(role.name() == "ADMIN");
    ASSERT_TRUE(role.get_parent() == "root");

    role.set_parent("any");
    ASSERT_TRUE(role.get_parent() == "any");
    role.set_parent("root");

    std::map<RBACObject, PrivilegeBitset> priv_map;

    {
        ASSERT_FALSE(role.authorize("*", "*", *g_privilege_bitset_ALL));
        for (PrivilegeId priv_id = 0; priv_id < MOCHOW_PRIVILEGE_COUNT_MAX; ++priv_id) {
            ASSERT_FALSE(role.authorize("*", "*", priv_id));
        }

        role.grant_privileges_by_bitset("*", "*", *g_privilege_bitset_ALL);
        role.revoke_privileges_by_bitset("*", "*", *g_privilege_bitset_TABLE_ALL);
        role.grant_privilege("A", "*", Privilege::QUERY);
        role.grant_privilege("A", "*", Privilege::INSERT);
        role.revoke_privilege("A", "*", Privilege::QUERY);

        ASSERT_FALSE(role.authorize("*", "*", *g_privilege_bitset_TABLE_ALL));
        ASSERT_TRUE(role.authorize("*", "*", *g_privilege_bitset_SYSTEM_ALL));
        ASSERT_TRUE(role.authorize("*", "*", Privilege::CREATE_ROLE));
        ASSERT_TRUE(role.authorize("*", "*", Privilege::CREATE_ROLE));
        ASSERT_FALSE(role.authorize("*", "*", Privilege::INSERT));
        ASSERT_FALSE(role.authorize("A", "*", *g_privilege_bitset_TABLE_ALL));
        ASSERT_TRUE(role.authorize("A", "*", *g_privilege_bitset_SYSTEM_ALL));
        ASSERT_TRUE(role.authorize("A", "*", Privilege::CREATE_ROLE));
        ASSERT_TRUE(role.authorize("A", "*", Privilege::CREATE_ROLE));
        ASSERT_TRUE(role.authorize("A", "*", Privilege::INSERT));

        role.show_privileges(&priv_map);
        ASSERT_EQ(priv_map.size(), 2);
        ASSERT_EQ(priv_map.count(RBACObject("*", "*")), 1);
        ASSERT_EQ(priv_map.count(RBACObject("A", "*")), 1);
    }

    {
        mochow::pb::PRole prole;
        role.serialize(&prole);
        ASSERT_TRUE(prole.role() == "ADMIN");
        ASSERT_TRUE(prole.parent() == "root");

        Role role2;
        ASSERT_TRUE(role2.deserialize(prole));
        std::map<RBACObject, PrivilegeBitset> priv_map2;
        role2.show_privileges(&priv_map2);
        ASSERT_TRUE(priv_map2 == priv_map);
        ASSERT_TRUE(role2.name() == "ADMIN");
        ASSERT_TRUE(role2.get_parent() == "root");

        prole.clear_role();
        ASSERT_FALSE(role2.deserialize(prole));
        prole.set_role("ADMIN");
        prole.set_parent("root");

        prole.mutable_priv_tree()->mutable_nodes(0)->mutable_object()->clear_database();
        ASSERT_FALSE(role2.deserialize(prole));
        prole.mutable_priv_tree()->mutable_nodes(0)->mutable_object()->set_database("*");

        prole.clear_priv_tree();
        ASSERT_TRUE(role2.deserialize(prole));
    }
}

TEST(RBACTests, test_user) {
    RoleRef role1 = std::make_shared<Role>("admin", "root");
    role1->grant_privileges_by_bitset("*", "*", *g_privilege_bitset_ALL);
    RoleRef role2 = std::make_shared<Role>("myrole", "root");
    role2->grant_privileges_by_bitset("A", "*", *g_privilege_bitset_TABLE_READONLY);

    User user("root", "root_password", "__system");
    ASSERT_TRUE(user.username() == "root");
    ASSERT_TRUE(user.password() == "root_password");
    ASSERT_TRUE(user.get_parent() == "__system");
    ASSERT_EQ(user.list_child_user().size(), 0);
    ASSERT_EQ(user.list_child_role().size(), 0);

    {
        user.add_child_user("user1");
        user.add_child_user("user2");
        auto child_users = user.list_child_user();
        ASSERT_EQ(child_users.size(), 2);
        ASSERT_EQ(child_users.count("user1"), 1);
        ASSERT_EQ(child_users.count("user2"), 1);
        user.remove_child_user("user1");
        user.remove_child_user("user2");
        child_users = user.list_child_user();
        ASSERT_EQ(child_users.size(), 0);
        ASSERT_EQ(child_users.count("user1"), 0);
        ASSERT_EQ(child_users.count("user2"), 0);
    }

    {
        user.add_child_role("admin");
        user.add_child_role("myrole");
        auto child_roles = user.list_child_role();
        ASSERT_EQ(child_roles.size(), 2);
        ASSERT_EQ(child_roles.count("myrole"), 1);
        ASSERT_EQ(child_roles.count("admin"), 1);
        user.remove_child_role("admin");
        user.remove_child_role("myrole");
        child_roles = user.list_child_role();
        ASSERT_EQ(child_roles.size(), 0);
        ASSERT_EQ(child_roles.count("myrole"), 0);
        ASSERT_EQ(child_roles.count("admin"), 0);
    }

    {
        user.grant_privileges_by_bitset("A", "T1", *g_privilege_bitset_TABLE_READWRITE);
        user.revoke_privileges_by_bitset("A", "T1", *g_privilege_bitset_TABLE_READONLY);
        user.grant_privilege("A", "*", Privilege::CREATE_ROLE);
        user.grant_privilege("A", "*", Privilege::CREATE_USER);
        user.revoke_privilege("A", "*", Privilege::CREATE_USER);
        ASSERT_FALSE(user.authorize("*", "*", rand() % MOCHOW_PRIVILEGE_COUNT_MAX));
        ASSERT_FALSE(user.authorize("A", "*", Privilege::CREATE_USER));
        ASSERT_TRUE(user.authorize("A", "*", Privilege::CREATE_ROLE));
        ASSERT_TRUE(user.authorize("A", "T1", Privilege::CREATE_ROLE));
        ASSERT_TRUE(user.authorize("A", "T1", Privilege::INSERT));
        ASSERT_FALSE(user.authorize("A", "T1", Privilege::SELECT));
        ASSERT_FALSE(user.authorize("A", "T1", Privilege::QUERY));
        ASSERT_FALSE(user.authorize("A", "T1", Privilege::SEARCH));
        ASSERT_FALSE(user.authorize("A", "T2", Privilege::INSERT));

        std::map<RBACObject, PrivilegeBitset> priv_map;
        user.show_privileges(&priv_map);
        ASSERT_EQ(priv_map.size(), 2);
        ASSERT_EQ(priv_map.count(RBACObject("A", "*")), 1);
        ASSERT_EQ(priv_map.count(RBACObject("A", "T1")), 1);
    }

    {
        ASSERT_FALSE(user.is_role_granted("admin"));
        ASSERT_FALSE(user.is_role_granted("myrole"));
        ASSERT_TRUE(user.grant_role(role1));
        ASSERT_FALSE(user.grant_role(role1));
        ASSERT_TRUE(user.grant_role(role2));
        ASSERT_TRUE(user.is_role_granted("admin"));
        ASSERT_TRUE(user.is_role_granted("myrole"));
        std::vector<RoleRef> roles;
        user.list_role(&roles);
        ASSERT_EQ(roles.size(), 2);
        ASSERT_TRUE(roles[0]->name() == role1->name());
        ASSERT_TRUE(roles[1]->name() == role2->name());
    }

    {
        std::vector<RoleRef> roles;
        user.select_role_by_privilege("*", "*", Privilege::CREATE_USER, &roles);
        ASSERT_EQ(roles.size(), 1);
        ASSERT_TRUE(roles[0]->name() == role1->name()); // admin

        roles.clear();
        user.select_role_by_privilege("A", "*", Privilege::SELECT, &roles);
        ASSERT_EQ(roles.size(), 2);
        ASSERT_TRUE(roles[0]->name() == role1->name()); // admin
        ASSERT_TRUE(roles[1]->name() == role2->name()); // myrole

        roles.clear();
        user.select_role_by_privilege("A", "T1", Privilege::SELECT, &roles);
        ASSERT_EQ(roles.size(), 2);
        ASSERT_TRUE(roles[0]->name() == role1->name()); // admin
        ASSERT_TRUE(roles[1]->name() == role2->name()); // myrole

        ASSERT_TRUE(user.revoke_role(role1->name()));
        ASSERT_TRUE(user.revoke_role(role1->name()));
        ASSERT_FALSE(user.is_role_granted("admin"));
        roles.clear();
        user.list_role(&roles);
        ASSERT_EQ(roles.size(), 1);
        ASSERT_TRUE(roles[0]->name() == role2->name());
    }

    {
        ASSERT_FALSE(user.authorize("A", "*", Privilege::CREATE_USER));
        ASSERT_TRUE(user.authorize("A", "*", Privilege::CREATE_ROLE));
        ASSERT_TRUE(user.authorize("A", "T1", Privilege::CREATE_ROLE));
        ASSERT_TRUE(user.authorize("A", "T1", Privilege::INSERT));
        ASSERT_TRUE(user.authorize("A", "T1", Privilege::SELECT));
        ASSERT_TRUE(user.authorize("A", "T1", Privilege::QUERY));
        ASSERT_TRUE(user.authorize("A", "T1", Privilege::SEARCH));
        ASSERT_FALSE(user.authorize("A", "T2", Privilege::INSERT));
        ASSERT_TRUE(user.authorize("A", "T2", Privilege::SELECT));

        user.revoke_privileges_by_bitset("A", "*", *g_privilege_bitset_ALL);
        ASSERT_FALSE(user.authorize("A", "*", Privilege::CREATE_USER));
        ASSERT_FALSE(user.authorize("A", "*", Privilege::CREATE_ROLE));
        ASSERT_TRUE(user.authorize("A", "*", Privilege::SELECT));
        ASSERT_TRUE(user.authorize("A", "*", Privilege::QUERY));
        ASSERT_TRUE(user.authorize("A", "*", Privilege::SEARCH));
        ASSERT_FALSE(user.authorize("A", "T1", Privilege::CREATE_ROLE));
        ASSERT_FALSE(user.authorize("A", "T1", Privilege::INSERT));
        ASSERT_TRUE(user.authorize("A", "T1", Privilege::SELECT));
        ASSERT_TRUE(user.authorize("A", "T1", Privilege::QUERY));
        ASSERT_TRUE(user.authorize("A", "T1", Privilege::SEARCH));
        ASSERT_FALSE(user.authorize("A", "T2", Privilege::INSERT));
        ASSERT_TRUE(user.authorize("A", "T2", Privilege::SELECT));
        ASSERT_TRUE(user.authorize("A", "T2", Privilege::QUERY));
        ASSERT_TRUE(user.authorize("A", "T2", Privilege::SEARCH));
    }

    {
        // test list_authorized_database
        RoleRef role1 = std::make_shared<Role>("test", "root");
        role1->grant_privilege("A", "*", Privilege::USAGE);
        User user("root", "root_password", "__system");
        user.grant_role(role1);
        std::unordered_set<std::string> db_list;
        // 1. there is not db match SHOW_DATABASE
        user.list_authorized_database(db_list, Privilege::SHOW_DATABASE);
        EXPECT_EQ(0, db_list.size());

        db_list.clear();
        // 2. role has SHOW_DATABASE
        role1->grant_privilege("A", "*", Privilege::SHOW_DATABASE);
        user.list_authorized_database(db_list, Privilege::SHOW_DATABASE);
        EXPECT_EQ(1, db_list.size());
        auto it = db_list.find("A");
        EXPECT_EQ(1, db_list.count("A"));
        EXPECT_TRUE(it != db_list.end());

        user.revoke_role("test");
        db_list.clear();
        // all db match SHOW_DATABASE
        user.grant_privilege("*", "*", Privilege::SHOW_DATABASE);
        user.list_authorized_database(db_list, Privilege::SHOW_DATABASE);
        EXPECT_EQ(1, db_list.size());
        EXPECT_EQ(1, db_list.count("*"));

        db_list.clear();
        // A match SHOW_DATABASE
        user.revoke_privilege("*", "*", Privilege::SHOW_DATABASE);
        user.grant_privilege("A", "*", Privilege::SHOW_DATABASE);
        user.list_authorized_database(db_list, Privilege::SHOW_DATABASE);
        EXPECT_EQ(1, db_list.size());
        EXPECT_EQ(1, db_list.count("A"));
    }

    {
        // test list_authorized_database
        RoleRef role1 = std::make_shared<Role>("test", "root");
        role1->grant_privilege("A", "*", Privilege::USAGE);
        User user("root", "root_password", "__system");
        user.grant_role(role1);
        std::unordered_set<std::string> table_list;
        // 1. there is not db match SHOW_DATABASE
        user.list_authorized_table(table_list, "AA", Privilege::SHOW_DATABASE);
        EXPECT_EQ(0, table_list.size());

        table_list.clear();
        // 2. role has SHOW_DATABASE
        role1->grant_privilege("A", "a", Privilege::SHOW_DATABASE);
        user.list_authorized_table(table_list, "AA", Privilege::SHOW_DATABASE);
        EXPECT_EQ(0, table_list.size());

        table_list.clear();
        user.list_authorized_table(table_list, "A", Privilege::SHOW_DATABASE);
        EXPECT_EQ(1, table_list.size());
        auto it = table_list.find("a");
        EXPECT_EQ(1, table_list.count("a"));
        EXPECT_TRUE(it != table_list.end());

        user.revoke_role("test");
        table_list.clear();
        // all table match SHOW_DATABASE
        user.grant_privilege("*", "*", Privilege::SHOW_DATABASE);
        user.list_authorized_table(table_list, "A", Privilege::SHOW_DATABASE);
        EXPECT_EQ(1, table_list.size());
        EXPECT_EQ(1, table_list.count("*"));

        table_list.clear();
        // a match SHOW_DATABASE
        user.revoke_privilege("*", "*", Privilege::SHOW_DATABASE);
        user.grant_privilege("A", "a", Privilege::SHOW_DATABASE);
        user.list_authorized_table(table_list, "A", Privilege::SHOW_DATABASE);
        EXPECT_EQ(1, table_list.size());
        EXPECT_EQ(1, table_list.count("a"));
    }
}

TEST(RBACTests, test_user_seder) {
    RoleRef role1 = std::make_shared<Role>("admin", "root");
    role1->grant_privileges_by_bitset("*", "*", *g_privilege_bitset_ALL);
    RoleRef role2 = std::make_shared<Role>("myrole", "root");
    role2->grant_privileges_by_bitset("A", "*", *g_privilege_bitset_TABLE_READONLY);

    User user("root", "root_password", "__system");
    ASSERT_TRUE(user.username() == "root");
    ASSERT_TRUE(user.password() == "root_password");
    ASSERT_TRUE(user.get_parent() == "__system");

    user.add_child_user("user1");
    user.add_child_user("user2");
    user.add_child_role("admin");
    user.add_child_role("myrole");

    user.grant_role(role1);
    user.grant_role(role2);
    user.grant_privileges_by_bitset("A", "T1", *g_privilege_bitset_TABLE_READWRITE);
    user.grant_privileges_by_bitset("B", "T2", *g_privilege_bitset_TABLE_ALL);

    std::map<RBACObject, PrivilegeBitset> priv_map;
    user.show_privileges(&priv_map);

    mochow::pb::PUser puser;
    user.serialize(&puser);

    User user2;
    ASSERT_TRUE(user2.deserialize(puser));

    ASSERT_TRUE(user2.username() == "root");
    ASSERT_TRUE(user2.password() == "root_password");
    ASSERT_TRUE(user2.get_parent() == "__system");
    auto child_users = user2.list_child_user();
    ASSERT_EQ(child_users.size(), 2);
    ASSERT_EQ(child_users.count("user1"), 1);
    ASSERT_EQ(child_users.count("user2"), 1);
    auto child_roles = user2.list_child_role();
    ASSERT_EQ(child_roles.size(), 2);
    ASSERT_EQ(child_roles.count("myrole"), 1);
    ASSERT_EQ(child_roles.count("admin"), 1);

    std::map<RBACObject, PrivilegeBitset> priv_map2;
    user2.show_privileges(&priv_map2);
    ASSERT_TRUE(priv_map2 == priv_map);

    puser.clear_username();
    ASSERT_FALSE(user2.deserialize(puser));
    puser.set_username("root");

    puser.clear_password();
    ASSERT_FALSE(user2.deserialize(puser));
    puser.set_password("root_password");

    puser.mutable_priv_tree()->mutable_nodes(0)->mutable_object()->clear_database();
    ASSERT_FALSE(user2.deserialize(puser));
    puser.mutable_priv_tree()->mutable_nodes(0)->mutable_object()->set_database("A");

    puser.clear_priv_tree();
    ASSERT_TRUE(user2.deserialize(puser));
}

TEST(RBACTests, test_rbac_manager) {
    RBACManager rbac_manager;

    initialize_rbac_manager(&rbac_manager);

    {
        // verify initlialization
        auto admin_role = rbac_manager.get_builtin_admin_role();
        auto system_user = rbac_manager.get_user("__system");
        auto root_user = rbac_manager.get_user("root");
        ASSERT_NE(admin_role, nullptr);
        ASSERT_NE(system_user, nullptr);
        ASSERT_NE(root_user, nullptr);

        ASSERT_TRUE(admin_role->get_parent() == "root");

        ASSERT_TRUE(system_user->authorize("*", "*", Privilege::USAGE));
        ASSERT_TRUE(system_user->authorize("*", "*", *g_privilege_bitset_ALL));
        ASSERT_TRUE(system_user->is_role_granted("ADMIN"));
        ASSERT_TRUE(system_user->get_parent() == "");
        auto child_users = system_user->list_child_user();
        ASSERT_EQ(child_users.size(), 1);
        ASSERT_EQ(child_users.count("root"), 1);
        auto child_roles = system_user->list_child_role();
        ASSERT_EQ(child_roles.size(), 0);

        ASSERT_TRUE(root_user->authorize("*", "*", Privilege::USAGE));
        ASSERT_TRUE(root_user->authorize("*", "*", *g_privilege_bitset_ALL));
        ASSERT_TRUE(root_user->is_role_granted("ADMIN"));
        ASSERT_TRUE(root_user->get_parent() == "__system");
        child_users = root_user->list_child_user();
        ASSERT_EQ(child_users.size(), 0);
        child_roles = root_user->list_child_role();
        ASSERT_EQ(child_roles.size(), 1);
        ASSERT_EQ(child_roles.count("ADMIN"), 1);
    }

    RoleRef admin_role = rbac_manager.get_builtin_admin_role();
    RoleRef role1 = nullptr;
    RoleRef role2 = nullptr;
    UserRef user1 = nullptr;
    UserRef user2 = nullptr;

    {
        role1 = rbac_manager.create_role("role1", "root");
        role2 = rbac_manager.create_role("role2", "root");
        std::vector<RoleRef> roles;
        rbac_manager.list_role(&roles);
        ASSERT_EQ(roles.size(), 3);

        auto root_user = rbac_manager.get_user("root");
        auto child_roles = root_user->list_child_role();
        ASSERT_EQ(child_roles.size(), 3);
        ASSERT_EQ(child_roles.count("ADMIN"), 1);
        ASSERT_EQ(child_roles.count("role1"), 1);
        ASSERT_EQ(child_roles.count("role2"), 1);
    }

    {
        user1 = rbac_manager.create_user("user1", "password", "root");
        user2 = rbac_manager.create_user("user2", "password", "root");

        std::vector<UserRef> users;
        rbac_manager.list_user(&users);
        ASSERT_EQ(users.size(), 4);

        auto root_user = rbac_manager.get_user("root");
        auto child_users = root_user->list_child_user();
        ASSERT_EQ(child_users.size(), 2);
        ASSERT_EQ(child_users.count("user1"), 1);
        ASSERT_EQ(child_users.count("user2"), 1);
    }

    role1->grant_privileges_by_bitset("*", "*", *g_privilege_bitset_TABLE_READONLY);
    role2->grant_privileges_by_bitset("A", "*", *g_privilege_bitset_TABLE_ALL);
    user1->grant_role(admin_role);
    user1->grant_role(role1);
    user2->grant_role(role1);
    user2->grant_role(role2);

    {
        // select user by role
        std::vector<UserRef> users;
        rbac_manager.select_user_by_role("ADMIN", &users);
        ASSERT_EQ(users.size(), 3);
        ASSERT_TRUE(users[0]->username() == "__system");
        ASSERT_TRUE(users[1]->username() == "root");
        ASSERT_TRUE(users[2]->username() == "user1");
        users.clear();
        rbac_manager.select_user_by_role("role1", &users);
        ASSERT_EQ(users.size(), 2);
        ASSERT_TRUE(users[0]->username() == "user1");
        ASSERT_TRUE(users[1]->username() == "user2");
        users.clear();
        rbac_manager.select_user_by_role("role2", &users);
        ASSERT_EQ(users.size(), 1);
        ASSERT_TRUE(users[0]->username() == "user2");
    }

    {
        // select user by privilege
        std::vector<UserRef> users;
        rbac_manager.select_user_by_privilege("*", "*", Privilege::CREATE_USER, &users);
        ASSERT_EQ(users.size(), 3);
        ASSERT_TRUE(users[0]->username() == "__system");
        ASSERT_TRUE(users[1]->username() == "root");
        ASSERT_TRUE(users[2]->username() == "user1");
        users.clear();
        rbac_manager.select_user_by_privilege("A", "*", Privilege::SELECT, &users);
        ASSERT_EQ(users.size(), 4);
        ASSERT_TRUE(users[0]->username() == "__system");
        ASSERT_TRUE(users[1]->username() == "root");
        ASSERT_TRUE(users[2]->username() == "user1");
        ASSERT_TRUE(users[3]->username() == "user2");
        users.clear();
        rbac_manager.select_user_by_privilege("A", "*", *g_privilege_bitset_SYSTEM_ALL, &users);
        ASSERT_EQ(users.size(), 3);
        ASSERT_TRUE(users[0]->username() == "__system");
        ASSERT_TRUE(users[1]->username() == "root");
        ASSERT_TRUE(users[2]->username() == "user1");
    }

    {
        // select role by privilege
        std::vector<RoleRef> roles;
        rbac_manager.select_role_by_privilege("*", "*", Privilege::CREATE_USER, &roles);
        ASSERT_EQ(roles.size(), 1);
        ASSERT_TRUE(roles[0]->name() == "ADMIN");
        roles.clear();
        rbac_manager.select_role_by_privilege("A", "*", *g_privilege_bitset_TABLE_READWRITE, &roles);
        ASSERT_EQ(roles.size(), 2);
        ASSERT_TRUE(roles[0]->name() == "ADMIN");
        ASSERT_TRUE(roles[1]->name() == "role2");
    }

    // drop roles failed due to referenced
    ASSERT_FALSE(rbac_manager.drop_role("ADMIN"));
    ASSERT_FALSE(rbac_manager.drop_role("role1"));
    ASSERT_FALSE(rbac_manager.drop_role("role2"));

    // drop users and roles
    rbac_manager.drop_user("user1");
    rbac_manager.drop_user("user2");
    rbac_manager.drop_user("root");
    rbac_manager.drop_user("__system");
    ASSERT_TRUE(rbac_manager.drop_role("ADMIN"));
    ASSERT_TRUE(rbac_manager.drop_role("role1"));
    ASSERT_TRUE(rbac_manager.drop_role("role2"));
    ASSERT_EQ(rbac_manager._users.size(), 0);
    ASSERT_EQ(rbac_manager._roles.size(), 0);
}

}
