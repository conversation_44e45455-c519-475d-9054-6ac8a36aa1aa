/***************************************************************************
 *
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

#include <gtest/gtest.h>
#include <gmock/gmock.h>

#include "core/src/common/token_bucket.h"

namespace mochow::common {

class TokenBucketTests : public::testing::Test {
};

TEST_F(TokenBucketTests, token_bucket_test) {
    FLAGS_enable_token_bucket = true;
    FLAGS_token_bucket_max_tokens = 2;
    FLAGS_token_bucket_fill_rate = 1;

    g_token_bucket->_tokens = 2;

    EXPECT_TRUE(g_token_bucket->consume(1));
    EXPECT_FALSE(g_token_bucket->consume(10));

    FLAGS_enable_token_bucket = false;
    EXPECT_TRUE(g_token_bucket->consume(1));
    EXPECT_TRUE(g_token_bucket->consume(10));
}

}
