#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "bmock.h"

#include "core/test/common/bmock_util.h"

#include "sfl/constants.h"
#include "core/src/common/utils.h"

#include "core/src/datanode/flags.h"
#include "core/src/datanode/scheduler/memory_estimator.h"

namespace mochow::datanode {

using ::testing::_;
using ::testing::DoAll;
using ::testing::Invoke;
using ::testing::WithArgs;
using ::testing::Return;
using ::testing::SetArgPointee;
using ::testing::SetArgReferee;

class MemoryEstimatorTests : public ::testing::Test {
protected:
    static void SetUpTestCase() {
    }
    static void TearDownTestCase() {}
};


TEST_F(MemoryEstimatorTests, test_memory_estimator_high_watermark) {
    bool b;
    LOG(INFO) << "====== test clear ==========";
    g_memory_estimator->clear();
    EXPECT_EQ(g_memory_estimator->_estimation_task_count, 0);
    EXPECT_EQ(g_memory_estimator->_estimation_memory, 0);
    EXPECT_EQ(g_memory_estimator->_system_memory, 0);

    LOG(INFO) << "====== to_string succeed ==========";
    auto str = g_memory_estimator->to_string();
    std::string expected_str = R"({"MemoryEstimator":{"estimation_task_count":0,"estimation_memory":0,"system_memory":0}})";
    EXPECT_EQ(str, expected_str); 

    LOG(INFO) << "====== refresh succeed ==========";
    b = g_memory_estimator->try_refresh_system_memory();
    EXPECT_TRUE(b);
    EXPECT_EQ(g_memory_estimator->_estimation_task_count, 0);
    EXPECT_EQ(g_memory_estimator->_estimation_memory, 0);
    EXPECT_TRUE(g_memory_estimator->_system_memory > 0);

    size_t memory_cost = 100;
    LOG(INFO) << "====== try add succeed ==========";
    // make sure try_add succeeds.
    FLAGS_datanode_memory_cost_water_mark_percentage = 1;
    b = g_memory_estimator->try_add(memory_cost);
    EXPECT_TRUE(b);
    EXPECT_EQ(g_memory_estimator->_estimation_task_count, 1);
    EXPECT_EQ(g_memory_estimator->_estimation_memory, memory_cost);
    EXPECT_TRUE(g_memory_estimator->_system_memory > 0);

    LOG(INFO) << "====== try add failed ==========";
    // make sure try_add fail.
    FLAGS_datanode_memory_cost_water_mark_percentage = 0;
    b = g_memory_estimator->try_add(memory_cost);
    EXPECT_FALSE(b);
    EXPECT_EQ(g_memory_estimator->_estimation_task_count, 1);
    EXPECT_EQ(g_memory_estimator->_estimation_memory, memory_cost);
    EXPECT_TRUE(g_memory_estimator->_system_memory > 0);

    LOG(INFO) << "====== remove succeed ==========";
    g_memory_estimator->remove(memory_cost);
    EXPECT_EQ(g_memory_estimator->_estimation_task_count, 0);
    EXPECT_EQ(g_memory_estimator->_estimation_memory, 0);
    EXPECT_TRUE(g_memory_estimator->_system_memory > 0);
}

TEST_F(MemoryEstimatorTests, test_memory_estimator_reserved_memory) {
    bool b;
    LOG(INFO) << "====== test clear ==========";
    g_memory_estimator->clear();
    EXPECT_EQ(g_memory_estimator->_estimation_task_count, 0);
    EXPECT_EQ(g_memory_estimator->_estimation_memory, 0);
    EXPECT_EQ(g_memory_estimator->_system_memory, 0);

    LOG(INFO) << "====== to_string succeed ==========";
    auto str = g_memory_estimator->to_string();
    std::string expected_str = R"({"MemoryEstimator":{"estimation_task_count":0,"estimation_memory":0,"system_memory":0}})";
    EXPECT_EQ(str, expected_str); 

    LOG(INFO) << "====== refresh succeed ==========";
    b = g_memory_estimator->try_refresh_system_memory();
    EXPECT_TRUE(b);
    EXPECT_EQ(g_memory_estimator->_estimation_task_count, 0);
    EXPECT_EQ(g_memory_estimator->_estimation_memory, 0);
    EXPECT_TRUE(g_memory_estimator->_system_memory > 0);

    size_t memory_cost = 100;
    LOG(INFO) << "====== try add fail ==========";
    // try_add would fail due to reserved memory not enough
    FLAGS_datanode_memory_cost_water_mark_percentage = 1;
    FLAGS_datanode_minimum_reserved_memory_in_byte = get_total_mem_size_in_mb() * baidu::sfl::MEBI; 
    b = g_memory_estimator->try_add(memory_cost);
    EXPECT_FALSE(b);
    EXPECT_EQ(g_memory_estimator->_estimation_task_count, 0);
    EXPECT_EQ(g_memory_estimator->_estimation_memory, 0);
    EXPECT_TRUE(g_memory_estimator->_system_memory > 0);

    LOG(INFO) << "====== try add succeed ==========";
    // try_add would fail due to reserved memory not enough
    FLAGS_datanode_memory_cost_water_mark_percentage = 1;
    FLAGS_datanode_minimum_reserved_memory_in_byte = 0;
    b = g_memory_estimator->try_add(memory_cost);
    EXPECT_TRUE(b);
    EXPECT_EQ(g_memory_estimator->_estimation_task_count, 1);
    EXPECT_EQ(g_memory_estimator->_estimation_memory, memory_cost);
    EXPECT_TRUE(g_memory_estimator->_system_memory > 0);
}

}  // namespace mochow::datanode
