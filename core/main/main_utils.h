/**
 * MOCHOW - A Vector Database System.
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON> (<EMAIL>)
 * Date: 2023/09/18
 * Desciption: Utils for main
 *
 */

#pragma once

#include <iomanip>
#include <memory>
#include <sstream>
#include <syscall.h>
#include <stdio.h>
#include <stdint.h>
#include <stdlib.h>
#include <sys/ioctl.h>
#include <sys/types.h>
#include <sys/wait.h>
#include <sys/prctl.h>
#include <sys/stat.h>
#include <unistd.h>
#include <gflags/gflags.h>
#include <base/file_util.h>
#include <base/logging.h>
#include <base/string_printf.h>
#include <base/file_util.h>
#include <base/fd_guard.h>
#include <base/threading/platform_thread.h>

#include "baidu/vdb/mochow/core/src/common/logging.h"

namespace baidu {
namespace rpc {
    DECLARE_int32(idle_timeout_second);
    DECLARE_int32(defer_close_second);
    DECLARE_string(trackme_server);
}
}

namespace mochow {
namespace common {

class FileLock {
public:
    FileLock(const std::string &name) : _name(name), _fd(-1) {}

    ~FileLock() {
        if (_fd >= 0) {
            unlock();
            unlink(_name.c_str());
            close(_fd);
            _fd = -1;
            LOG(WARNING) << "release filelock " << _name;
        }
    }

    int lock() {
        if (_fd >= 0) {
            return 0;
        }
        _fd = open(_name.c_str(), O_CREAT | O_WRONLY, 0644);
        if (_fd < 0) {
            LOG(WARNING) << "create " << _name << " failed," << " errno: " << errno;
            return -1;
        }
        int ret = lockf(_fd, F_TLOCK, 0);
        if (ret != 0) {
            LOG(WARNING) << "lock file lock " << _name << " failed: " << ret;
            close(_fd);
            _fd = -1;
            return -1;
        }
        if (ftruncate(_fd, 0) != 0) {
            LOG(WARNING) << "truncate " << _name << " failed," << " errno: " << errno;
            unlock();
            return -1;
        }
        pid_t pid = getpid();
        std::string pid_str = base::string_printf("%d", pid);
        int n = write(_fd, pid_str.c_str(), pid_str.length());
        if (n != (int) pid_str.length()) {
            LOG(WARNING) << "write " << _name << " failed," << " expect_write_size:" << pid_str.length()
                << " actual_write_size:" << n << " errno:" << errno;
            unlock();
            return -1;
        }
        return 0;
    }

    int unlock() {
        if (_fd < 0) {
            return -1;
        }
        int ret = lockf(_fd, F_ULOCK, 0);
        if (ret != 0) {
            LOG(WARNING) << "unlock file lock " << _name << " failed: " << ret;
        }
        return ret;
    }

private:
    std::string _name;
    int _fd;
};

class PidFile {
public:
    static pid_t read(const std::string &name) {
        char buffer[1024] = {0};
        pid_t pid = 0;
        int n = base::ReadFile(base::FilePath(name), buffer, sizeof(buffer));
        if (n == sizeof(buffer) || n <= 0) {
            LOG(WARNING) << "fail to read " << name << " failed, size:" << n << " too large,"
                << " errno: " << errno;
            return -1;
        }
        std::istringstream in(buffer);
        in >> pid;

        std::string pid_str = base::string_printf("/proc/%d", pid);
        if (!base::PathExists(base::FilePath(pid_str))) {
            LOG(WARNING) << "fail to stop " << name << " due to can not find pid " << pid << " in /proc";
            return -1;
        }
        return pid;
    }

public:
    PidFile(const std::string &name) : _name(name), _fd(-1) {}
    ~PidFile() {
        if (_fd >= 0) {
            unlink(_name.c_str());
            close(_fd);
            _fd = -1;
        }
    }

    int create() {
        pid_t pid = getpid();
        _fd = open(_name.c_str(), O_CREAT | O_WRONLY | O_TRUNC, 0644);
        if (_fd == -1) {
            LOG(WARNING) << "open " << _name << " failed," << " errno: " << errno;
            return -1;
        }
        std::string pid_str = base::string_printf("%d", pid);
        int n = write(_fd, pid_str.c_str(), pid_str.length());
        if (n != (int) pid_str.length()) {
            LOG(WARNING) << "write " << _name << " failed," << " expect_write_size:" << pid_str.length()
                << " actual_write_size:" << n << " errno:" << errno;
            unlink(_name.c_str());
            close(_fd);
            _fd = -1;
            return -1;
        }
        return 0;
    }

private:
    std::string _name;
    int _fd;
};

}
}

typedef void(*FunctionOnExit)();
typedef void(*FunctionOnReload)();

int g_mochow_main_thread_id;

class SignalHandler {
public:
    static std::string _module_name;
    static FunctionOnExit _func_on_exit;
    static FunctionOnReload _func_on_reload;
    static volatile int _saved_sig;

    static void set_module_name(const std::string& module_name) {
        _module_name = module_name;
    }

    static void set_func_on_exit(FunctionOnExit func) {
        _func_on_exit = func;
    }

    static void set_func_on_reload(FunctionOnReload func) {
        _func_on_reload = func;
    }

    static void signal_handler(int sig) {
        if (sig == SIGUSR2) {
            if (_func_on_reload) {
                LOG(NOTICE) << "receive signal " << sig << ", try reload " << _module_name;
                _func_on_reload();
            }
            return;
        }

        int last_signal = __sync_val_compare_and_swap(&_saved_sig, 0, sig);
        if (last_signal != 0) {
            // avoid repeating handle it
            return;
        }
        long int tid = syscall(__NR_gettid);
        LOG(FATAL) << "receive signal:" << sig << " thread_id:" << tid;
        fprintf(stderr, "recv signal:%d, thread_id:%ld\n", sig, tid);

        switch (sig) {
            case SIGHUP:
            case SIGINT:
            case SIGTERM:
            case SIGQUIT:
                LOG(FATAL) << _module_name << " exit, receive signal:" << sig;
                if (_func_on_exit) {
                    _func_on_exit();
                    return;
                }
                _exit(0);
                break;
            case SIGSEGV:
            case SIGILL:
            case SIGABRT:
            case SIGFPE:
            case SIGBUS:
                signal(SIGABRT, SIG_DFL);
                LOG(FATAL) << _module_name << " exit, receive signal:" << sig;
                abort();
                exit(0);
                break;
            default:
                LOG(FATAL) << _module_name << " exit, receive signal:" << sig;
                break;
        }
    }

    static void register_signal_handler() {
        signal(SIGABRT, signal_handler);
        signal(SIGFPE, signal_handler);
        signal(SIGSEGV, signal_handler);
        signal(SIGTERM, signal_handler);
        signal(SIGHUP, signal_handler);
        signal(SIGILL, signal_handler);
        signal(SIGINT, signal_handler);
        signal(SIGQUIT, signal_handler);
        signal(SIGBUS, signal_handler);

        // reload
        signal(SIGUSR2, signal_handler);
    }
};

std::string SignalHandler::_module_name;
FunctionOnExit SignalHandler::_func_on_exit = NULL;
FunctionOnReload SignalHandler::_func_on_reload = NULL;
volatile int SignalHandler::_saved_sig = 0;

struct Files {
    std::shared_ptr<mochow::common::FileLock> file_lock;
    std::shared_ptr<mochow::common::PidFile> pid_file;
    ~Files() {
        file_lock.reset();
        pid_file.reset();
    }
};

static int check_stop_and_reload(const char* module_name, bool is_stop, bool is_reload) {
    std::string lock_file = std::string(module_name) + ".lock";
    std::string pid_file = std::string(module_name) + ".pid";

    pid_t pid = mochow::common::PidFile::read(pid_file.c_str());
    if (pid < 0) {
        LOG(FATAL) << "fail to stop " << module_name
                << " due to open " << pid_file << " failed,"
                << " errno: " << errno;
        return -1;
    }
    if (is_stop) {
        kill(pid, SIGTERM);
        LOG(NOTICE) << "succ to kill pid " << pid;
    } else {
        kill(pid, SIGUSR2);
        LOG(NOTICE) << "succ to send SIGUSR2 to pid " << pid;
    }
    return 0;
}

static bool check_and_lock(const char* module_name, Files &files) {
    std::string lock_file = std::string(module_name) + ".lock";
    std::string pid_file = std::string(module_name) + ".pid";

    // lock
    std::shared_ptr<mochow::common::FileLock> flock(
                                new mochow::common::FileLock(lock_file));
    int ret = flock->lock();
    if (ret != 0) {
        std::stringstream ss;
        ss << "fail to start " << module_name
                << " due to lockf " << lock_file << " failed,"
                << " maybe means multi processes in the same directory!";
        fprintf(stderr, "%s\n", ss.str().c_str());
        return false;
    }

    std::shared_ptr<mochow::common::PidFile> pidfile(
                                new mochow::common::PidFile(pid_file));
    ret = pidfile->create();
    if (ret != 0) {
        std::stringstream ss;
        ss << "fail to start " << module_name
                << " due to open " << pid_file << " failed,"
                << " errno: " << errno;
        fprintf(stderr, "%s\n", ss.str().c_str());
        return false;
    }

    files.file_lock = flock;
    files.pid_file = pidfile;

    g_mochow_main_thread_id = syscall(__NR_gettid);

    return true;
}
