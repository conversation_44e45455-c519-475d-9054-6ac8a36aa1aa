/**
 * MOCHOW - A Vector Database System.
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON> (<EMAIL>)
 * Date: 2023/09/18
 * Desciption: Main for master
 *
 */

#include "baidu/vdb/mochow/core/src/common/config.h"
#include "baidu/vdb/mochow/core/src/common/config_reloader.h"
#include "baidu/vdb/mochow/core/main/main_utils.h"
#include "baidu/vdb/mochow/core/src/master/master.h"

#if defined(ENABLE_SANITIZE_ADDRESS)
extern "C" const char *__asan_default_options() {
    return "malloc_context_size=40 symbolize=1 detect_stack_use_after_return=1 "
           " verbosity=1 abort_on_error=1 debug=1 atexit=1 quarantine_size_mb=1024 alloc_dealloc_mismatch=1";
}
#endif

DECLARE_string(flagfile);
DEFINE_bool(h, false, "print help information");
DEFINE_bool(v, false, "print version information");
DEFINE_bool(stop, false, "stop master");
DEFINE_bool(reload, false, "reload master flags");

std::shared_ptr<mochow::master::Master> master;

static const char *s_proc_name = NULL;
#if defined( __DATE__ ) && defined ( __TIME__ )
static const char *s_build_time = __DATE__ " " __TIME__;
#else
static const char *s_build_time = "unknown";
#endif

#ifndef __MOCHOW_VERSION_ID__
#define __MOCHOW_VERSION_ID__ "unknown"
#endif

#ifndef __MOCHOW_REVISION_ID__
#define __MOCHOW_REVISION_ID__ "unknown"
#endif

#ifndef __MOCHOW_REPO_URL__
#define __MOCHOW_REPO_URL__   "unknown"
#endif

#ifndef __MOCHOW_BUILDHOST__
#define __MOCHOW_BUILDHOST__  "unknown"
#endif

void reload_master() {
    mochow::common::g_config_reloader->reload();
    master->update_conf();
}

void stop_master() {
    master->stop();
    _exit(0);
}

void register_signal_handler() {
    SignalHandler::set_module_name("mochow-master");
    SignalHandler::set_func_on_reload(reload_master);
    SignalHandler::set_func_on_exit(stop_master);
    SignalHandler::register_signal_handler();
}

int main(int argc, char** argv) {
    int ret = 0;

    // set version
    std::string version_str;
    base::string_printf(&version_str,
                        "Version: %s\n"
                        "Revision: %s\n"
                        "RepoURL: %s\n"
                        "Build: %s %s\n",
                        __MOCHOW_VERSION_ID__, __MOCHOW_REVISION_ID__, __MOCHOW_REPO_URL__,
                        s_build_time, __MOCHOW_BUILDHOST__);
    google::SetVersionString(version_str);

    // set usage
    s_proc_name = strrchr(argv[0], '/');
    if (s_proc_name == NULL) {
        s_proc_name = argv[0];
    } else {
        ++s_proc_name;
    }
    std::string help_str;
    base::string_printf(&help_str,
                        "Usage: %s [OPTIONS...]\n"
                        "Options:\n"
                        "  -h              Print this help message.\n"
                        "  -v              Print version number.\n"
                        "  -stop           stop gc.\n"
                        "  -flagfile=$path Load flags from file.",
                        s_proc_name);
    google::SetUsageMessage(help_str);

    struct stat st;
    if (0 == stat("conf/mochow-master.conf", &st) && FLAGS_flagfile.empty()) {
        FLAGS_flagfile = "conf/mochow-master.conf";
    }
    google::ParseCommandLineFlags(&argc, &argv, false);
    google::SetCommandLineOption("bvar_dump", "false");

    // check -v and -h
    if (FLAGS_v) {
        fprintf(stderr, "%s\n", version_str.c_str());
        _exit(0);
    }
    if (FLAGS_h) {
        fprintf(stderr, "%s\n", help_str.c_str());
        _exit(0);
    }

    // check -stop/-reload
    if (FLAGS_stop || FLAGS_reload) {
        assert(!(FLAGS_stop && FLAGS_reload));
        return check_stop_and_reload("mochow-master", FLAGS_stop, FLAGS_reload);
    }

    // check and lock
    Files files;
    if (!check_and_lock("mochow-master", files)) {
        _exit(-1);
    }

    // register signal handler
    register_signal_handler();

    // Init comlog
    if (::mochow::common::init_comlog() != 0) {
        LOG(FATAL) << "mochow-master startup failed due to init comlog failed";
        _exit(-1);
    }

    // Init local addr
    if (::mochow::common::init_local_addr() != 0) {
        LOG(FATAL) << "mochow-master startup failed due to init local addr failed";
        _exit(-1);
    }

    // Start Master
    master = std::make_shared<mochow::master::Master>();
    if (0 != master->start()) {
        LOG(FATAL) << "mochow-master startup failed";
        _exit(-1);
    }

    baidu::rpc::FLAGS_trackme_server = "";
    baidu::rpc::FLAGS_defer_close_second = 120 * 60;
    LOG(WARNING) << "mochow-master startup success, version:" << __MOCHOW_VERSION_ID__;

    // Join
    master->join();

    LOG(FATAL) << "mochow-master exit";
    _exit(0);
}

