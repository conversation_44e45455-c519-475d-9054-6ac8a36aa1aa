/***************************************************************************
 *
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

#include "core/src/master/service/database_service.h"

#include "core/src/common/index_auto_build_policy_type.h"
#include "core/src/schema/naming.h"
#include "core/src/schema/schema_builder.h"
#include "core/src/master/master_control.h"
#include "core/src/master/meta/meta_helper.h"
#include "core/src/schema/index_type.h"

namespace mochow::master {

#define DATABASE_SERVICE_CHECK_OR_BREAK \
        if (!check_token(request, response)) { break; } \
        if (!check_control_leader(response, g_master_control.get())) { break; }

#define DATABASE_SERVICE_AHTENTICATE_OR_BREAK \
        const auto& auth_info = request->auth_info(); \
        const auto auth_user = g_rbac_manager->get_user(auth_info.username()); \
        if (auth_user == nullptr \
                || !auth_user->authenticate(auth_info.username(), auth_info.password())) { \
            status->set_code(ERR_AUTHENTICATE_FAILED); \
            status->set_msg("Authenticate Failed"); \
            break; \
        }

#define DATABASE_SERVICE_AUTHORIZE_OR_BREAK(_DATABASE_, _TABLE_, _PRIVILEGE_ID_) \
        if (!auth_user->authorize(_DATABASE_, _TABLE_, Privilege::_PRIVILEGE_ID_)) { \
            status->set_code(ERR_PRIVILEGE_DENIED); \
            status->set_msg("Privilege Denied"); \
            break; \
        }

#define GET_AUTHORIZED_DATABASE_LIST(_PRIVILEGE_ID_) \
        std::unordered_set<std::string> authoried_db_list; \
        auth_user->list_authorized_database(authoried_db_list, Privilege::_PRIVILEGE_ID_); \
        if (authoried_db_list.size() == 0) { \
            status->set_code(OK); \
            break; \
        }

#define GET_AUTHORIZED_TABLE_LIST(_DATABASE_, _PRIVILEGE_ID_) \
        std::unordered_set<std::string> authoried_table_list;\
        auth_user->list_authorized_table(authoried_table_list, _DATABASE_, Privilege::_PRIVILEGE_ID_);\
        if (authoried_table_list.size() == 0) { \
            status->set_code(OK); \
            break; \
        }

static void check_index_auto_build_param(const mochow::pb::AutoBuildIndexParam& param, mochow::pb::Status *status) {
    // Check policy type and params
    const std::string policy_type = param.policy_type();
    const auto& policy_param = param.policy_param();

    if (policy_type == common::IndexAutoBuildPolicyType::TIMING) {
        if (!policy_param.has_timing()) {
            status->set_code(ERR_INVALID_ARGUMENT);
            status->set_msg("Policy param `timing` not specified for auto build policy type TIMING");
            return;
        }
        DateTime datetime;
        std::string timing = policy_param.timing();
        if (!datetime.compatible_parse(timing.c_str())) {
            status->set_code(ERR_INVALID_ARGUMENT);
            status->set_msg("Policy param `timing` is invalid for auto build policy type TIMING");
            return;
        }
    } else if (policy_type == common::IndexAutoBuildPolicyType::PERIODICAL) {
        if (!policy_param.has_period_s() || policy_param.period_s() <= 0) {
            status->set_code(ERR_INVALID_ARGUMENT);
            status->set_msg(
                    "Policy param `period_s` not specified or is invalid for auto build "
                    "policy type PERIODICAL");
            return;
        }
        if (policy_param.period_s() < FLAGS_auto_build_index_min_period_s) {
            status->set_code(ERR_INVALID_ARGUMENT);
            status->set_msg(
                    "Policy param `period_s` is too small for auto build policy type "
                    "PERIODICAL");
            return;
        }
    } else if (policy_type == common::IndexAutoBuildPolicyType::ROW_COUNT_INCREMENT) {
        if (!policy_param.has_row_count_increment() && !policy_param.has_row_count_increment_ratio()) {
            status->set_code(ERR_INVALID_ARGUMENT);
            status->set_msg(
                    "Policy param `row_count_increment` and `row_count_increment_ratio` are both set or not set, "
                    "auto build policy type ROW_COUNT_INCREMENT");
            return;
        }
        if (policy_param.has_row_count_increment()) {
            if (policy_param.row_count_increment() == 0 ||
                policy_param.row_count_increment() < FLAGS_auto_build_index_min_row_count_increment) {
                status->set_code(ERR_INVALID_ARGUMENT);
                status->set_msg(
                        "Policy param `row_count_increment` is invalid for auto build policy type ROW_COUNT_INCREMENT");
            }
            return;
        }
        if (policy_param.has_row_count_increment_ratio()) {
            if (policy_param.row_count_increment_ratio() <= 0) {
                status->set_code(ERR_INVALID_ARGUMENT);
                status->set_msg(
                        "Policy param `row_count_increment_ratio` is invalid for auto build policy type "
                        "ROW_COUNT_INCREMENT");
            }
            return;
        }
    } else {
        status->set_code(ERR_INVALID_ARGUMENT);
        status->set_msg("Invaid auto build policy type" + policy_type);
        return;
    }
}

void MasterDatabaseServiceImpl::create_database(::google::protobuf::RpcController* controller,
        const pb::CreateDatabaseRequest* request,
        pb::AckResponse* response,
        ::google::protobuf::Closure* done) {
    baidu::rpc::Controller* cntl = (baidu::rpc::Controller*)controller;
    baidu::rpc::ClosureGuard done_guard(done);
    uint64_t log_id = cntl->log_id();
    base::EndPoint remote_addr = cntl->remote_side();
    auto status = response->mutable_status();
    RPC_REQUEST_LOG(NOTICE);

    do {
        DATABASE_SERVICE_CHECK_OR_BREAK;
        DATABASE_SERVICE_AHTENTICATE_OR_BREAK;
        DATABASE_SERVICE_AUTHORIZE_OR_BREAK("*", "*", CREATE_DATABASE);

        if (!schema::NamingChecker::is_legal_database_name(request->db_info().db_name())) {
            status->set_code(ERR_INVALID_DB_NAME);
            status->set_msg("Invalid database name");
            break;
        }

        std::string db_name = request->db_info().db_name();
        auto db = g_db_manager->get_db(db_name);
        if (db) {
            status->set_code(ERR_DB_ALREADY_EXIST);
            status->set_msg("Database already exist");
            RPC_LOG(NOTICE) << "Fail to create database due to already exist, db_name:" << db_name;
            break;
        }

        auto db_id = g_metadata->alloc_db_id();

        pb::CreateDatabaseRequest new_request;
        new_request.CopyFrom(*request);
        new_request.set_log_id(log_id);
        new_request.mutable_db_info()->set_db_id(db_id);

        g_master_control->create_database(cntl, &new_request, response);

    } while (0);

    RPC_ACK_LOG(NOTICE);
}

void MasterDatabaseServiceImpl::drop_database(::google::protobuf::RpcController* controller,
        const pb::DropDatabaseRequest* request,
        pb::AckResponse* response,
        ::google::protobuf::Closure* done) {
    baidu::rpc::Controller* cntl = (baidu::rpc::Controller*)controller;
    baidu::rpc::ClosureGuard done_guard(done);
    uint64_t log_id = cntl->log_id();
    base::EndPoint remote_addr = cntl->remote_side();
    auto status = response->mutable_status();
    RPC_REQUEST_LOG(NOTICE);

    do {
        DATABASE_SERVICE_CHECK_OR_BREAK;
        DATABASE_SERVICE_AHTENTICATE_OR_BREAK;
        DATABASE_SERVICE_AUTHORIZE_OR_BREAK(request->db_name(), "*", DROP_DATABASE);

        pb::DropDatabaseRequest new_request;
        new_request.CopyFrom(*request);
        new_request.set_log_id(log_id);

        g_master_control->drop_database(cntl, &new_request, response);
    } while (0);

    RPC_ACK_LOG(NOTICE);
}

void MasterDatabaseServiceImpl::update_database(::google::protobuf::RpcController* controller,
        const pb::UpdateDatabaseRequest* request,
        pb::AckResponse* response,
        ::google::protobuf::Closure* done) {
    baidu::rpc::Controller* cntl = (baidu::rpc::Controller*)controller;
    baidu::rpc::ClosureGuard done_guard(done);
    uint64_t log_id = cntl->log_id();
    base::EndPoint remote_addr = cntl->remote_side();
    auto status = response->mutable_status();
    RPC_REQUEST_LOG(NOTICE);

    do {
        DATABASE_SERVICE_CHECK_OR_BREAK;
        DATABASE_SERVICE_AHTENTICATE_OR_BREAK;
        DATABASE_SERVICE_AUTHORIZE_OR_BREAK(request->db_name(), "*", ALTER_DATABASE);

        pb::UpdateDatabaseRequest new_request;
        new_request.CopyFrom(*request);
        new_request.set_log_id(log_id);

        g_master_control->update_database(cntl, &new_request, response);
    } while (0);

    RPC_ACK_LOG(NOTICE);
}

void MasterDatabaseServiceImpl::show_database(::google::protobuf::RpcController* controller,
        const pb::ShowDatabaseRequest* request,
        pb::ShowDatabaseResponse* response,
        ::google::protobuf::Closure* done) {
    baidu::rpc::Controller* cntl = (baidu::rpc::Controller*)controller;
    baidu::rpc::ClosureGuard done_guard(done);
    uint64_t log_id = cntl->log_id();
    base::EndPoint remote_addr = cntl->remote_side();
    auto status = response->mutable_status();
    RPC_REQUEST_LOG(TRACE);

    do {
        DATABASE_SERVICE_CHECK_OR_BREAK;
        DATABASE_SERVICE_AHTENTICATE_OR_BREAK;
        DATABASE_SERVICE_AUTHORIZE_OR_BREAK(request->db_name(), "*", SHOW_DATABASE);

        auto db = g_db_manager->get_db(request->db_name());
        if (!db) {
            status->set_code(ERR_DB_NOT_EXIST);
            status->set_msg("Database not exist");
        } else {
            status->set_code(OK);
            db->serialize(response->mutable_db_info());
        }

    } while (0);

    RPC_ACK_LOG(TRACE);
}

void MasterDatabaseServiceImpl::list_database(::google::protobuf::RpcController* controller,
        const pb::ListDatabaseRequest* request,
        pb::ListDatabaseResponse* response,
        ::google::protobuf::Closure* done) { 
    baidu::rpc::Controller* cntl = (baidu::rpc::Controller*)controller;
    baidu::rpc::ClosureGuard done_guard(done);
    uint64_t log_id = cntl->log_id();
    base::EndPoint remote_addr = cntl->remote_side();
    auto status = response->mutable_status();
    RPC_REQUEST_LOG(TRACE);

    do {
        DATABASE_SERVICE_CHECK_OR_BREAK;
        DATABASE_SERVICE_AHTENTICATE_OR_BREAK;
        GET_AUTHORIZED_DATABASE_LIST(SHOW_DATABASE);

        bool all_db = authoried_db_list.count("*") == 1;
        std::vector<DBRef> db_list;
        g_db_manager->list_db(&db_list);
        for (const auto& db : db_list) {
            auto it = authoried_db_list.find(db->name());
            if (!all_db && it == authoried_db_list.end()) {
                continue;
            }
            db->serialize(response->add_db_infos());
        }
        status->set_code(OK);
    } while (0);

    RPC_ACK_LOG(TRACE);
}

void MasterDatabaseServiceImpl::create_table(::google::protobuf::RpcController* controller,
        const pb::CreateTableRequest* request,
        pb::AckResponse* response,
        ::google::protobuf::Closure* done){
    baidu::rpc::Controller* cntl = (baidu::rpc::Controller*)controller;
    baidu::rpc::ClosureGuard done_guard(done);
    uint64_t log_id = cntl->log_id();
    base::EndPoint remote_addr = cntl->remote_side();
    auto status = response->mutable_status();
    RPC_REQUEST_LOG(NOTICE);

    do {
        DATABASE_SERVICE_CHECK_OR_BREAK;
        DATABASE_SERVICE_AHTENTICATE_OR_BREAK;
        DATABASE_SERVICE_AUTHORIZE_OR_BREAK(request->table_info().schema().db_name(), "*", CREATE_TABLE);

        auto& schema = request->table_info().schema();
        if (!schema::NamingChecker::is_legal_table_name(schema.table_name())) {
            status->set_code(ERR_INVALID_TABLE_NAME);
            status->set_msg("Invalid table name");
            break;
        }

        if (request->table_info().replication() <= 0) {
            status->set_code(ERR_INVALID_ARGUMENT);
            status->set_msg("Invalid replication");
            break;
        }

        // only support hash partition type now
        if (!common::PartitionType::is_hash_partition_type(request->table_info().partition().type())) {
            status->set_code(ERR_INVALID_ARGUMENT);
            status->set_msg("Invalid partition type");
            break;
        }

        for (int i = 0; i < request->table_info().vector_indexes_size(); i++) {
            const mochow::pb::PVectorIndex& index = request->table_info().vector_indexes(i);
            if (!index.is_auto_build()) {
                continue;
            }
            const mochow::pb::AutoBuildIndexParam param = index.auto_build_index_param();
            check_index_auto_build_param(param, status);
            if (status->code() != OK) {
                break;
            }
        }

        if (status->code() != OK) {
            break;
        }

        auto db_name = request->table_info().schema().db_name();
        auto table_name = request->table_info().schema().table_name();
        auto db = g_db_manager->get_db(db_name);
        if (!db) {
            status->set_code(ERR_DB_NOT_EXIST);
            status->set_msg("Database not exist");
            RPC_LOG(NOTICE) << "Fail to create table due to database not exist,"
                            << " db_name:" << db_name
                            << " table_name:" << table_name;
            break;
        }

        auto table = db->get_table(table_name);
        if (table != nullptr) {
            status->set_code(ERR_TABLE_ALREADY_EXIST);
            status->set_msg("Table already exist");
            RPC_LOG(NOTICE) << "Fail to create table due to table already exist,"
                            << " db_name:" << db_name
                            << " table_name:" << table_name;
            break;
        }

        bool need_encryption = false;
        if (!request->table_info().schema().has_need_encryption()) {
            need_encryption = FLAGS_enable_encryption;
        } else {
            need_encryption = request->table_info().schema().need_encryption();
        }
        if (need_encryption && !g_datanode_manager->enable_encryption()) {
            status->set_code(ERR_INVALID_ARGUMENT);
            status->set_msg("Encryption is not available, please open encryption or wait encryption initialization");
            RPC_LOG(NOTICE) << "Fail to create table due to encryption is not ready,"
                            << " db_name:" << db_name
                            << " table_name:" << table_name;
            break;
        }

        auto table_id = g_metadata->alloc_table_id();

        pb::CreateTableRequest new_request;
        new_request.CopyFrom(*request);
        new_request.set_log_id(log_id);
        new_request.mutable_table_info()->set_create_time(base::gettimeofday_us());
        new_request.mutable_table_info()->mutable_schema()->set_table_id(table_id);
        new_request.mutable_table_info()->mutable_schema()->set_db_id(db->id());
        new_request.mutable_table_info()->mutable_schema()->set_need_encryption(need_encryption);

        auto column_id_allocator = std::make_shared<schema::IdAllocator>();
        auto index_id_allocator = std::make_shared<schema::IdAllocator>();
        if (!complete_ptable_for_create_table(*new_request.mutable_table_info(),
                                              column_id_allocator,
                                              index_id_allocator)) {
            status->set_code(ERR_INVALID_TABLE_SCHEMA);
            status->set_msg("Invalid table schema");
            RPC_LOG(NOTICE) << "Fail to create table due to invalid table info,"
                << " db_name:" << db_name
                << " table_name:" << table_name
                << " table_info:" << common::pb2json(new_request);
            break;
        }

        g_master_control->create_table(cntl, &new_request, response);

    } while (0);

    RPC_ACK_LOG(NOTICE);
}

void MasterDatabaseServiceImpl::drop_table(::google::protobuf::RpcController* controller,
        const pb::DropTableRequest* request,
        pb::AckResponse* response,
        ::google::protobuf::Closure* done){
    baidu::rpc::Controller* cntl = (baidu::rpc::Controller*)controller;
    baidu::rpc::ClosureGuard done_guard(done);
    uint64_t log_id = cntl->log_id();
    base::EndPoint remote_addr = cntl->remote_side();
    auto status = response->mutable_status();
    RPC_REQUEST_LOG(NOTICE);

    do {
        DATABASE_SERVICE_CHECK_OR_BREAK;
        DATABASE_SERVICE_AHTENTICATE_OR_BREAK;
        DATABASE_SERVICE_AUTHORIZE_OR_BREAK(request->db_name(), request->table_name(), DROP_TABLE);

        pb::DropTableRequest new_request;
        new_request.CopyFrom(*request);
        new_request.set_log_id(log_id);

        g_master_control->drop_table(cntl, &new_request, response);
    } while (0);

    RPC_ACK_LOG(NOTICE);
}

void MasterDatabaseServiceImpl::update_table(::google::protobuf::RpcController* controller,
        const pb::UpdateTableRequest* request,
        pb::AckResponse* response,
        ::google::protobuf::Closure* done) {
    baidu::rpc::Controller* cntl = (baidu::rpc::Controller*)controller;
    baidu::rpc::ClosureGuard done_guard(done);
    uint64_t log_id = cntl->log_id();
    base::EndPoint remote_addr = cntl->remote_side();
    auto status = response->mutable_status();
    RPC_REQUEST_LOG(NOTICE);

    do {
        DATABASE_SERVICE_CHECK_OR_BREAK;
        DATABASE_SERVICE_AHTENTICATE_OR_BREAK;
        DATABASE_SERVICE_AUTHORIZE_OR_BREAK(request->db_name(), request->table_name(), ALTER_TABLE);

        auto table = g_db_manager->get_table(request->db_name(), request->table_name());
        if (!table) {
            status->set_code(ERR_TABLE_NOT_EXIST);
            status->set_msg("Table not exist");
            break;
        }

        pb::UpdateTableRequest new_request;
        new_request.CopyFrom(*request);
        new_request.set_log_id(log_id);

        g_master_control->update_table(cntl, &new_request, response);
    } while (0);

    RPC_ACK_LOG(NOTICE);
}

void MasterDatabaseServiceImpl::alias_table(::google::protobuf::RpcController* controller,
        const pb::AliasTableRequest* request,
        pb::AckResponse* response,
        ::google::protobuf::Closure* done) {
    baidu::rpc::Controller* cntl = (baidu::rpc::Controller*)controller;
    baidu::rpc::ClosureGuard done_guard(done);
    uint64_t log_id = cntl->log_id();
    base::EndPoint remote_addr = cntl->remote_side();
    auto status = response->mutable_status();
    RPC_REQUEST_LOG(NOTICE);

    do {
        DATABASE_SERVICE_CHECK_OR_BREAK;
        DATABASE_SERVICE_AHTENTICATE_OR_BREAK;
        DATABASE_SERVICE_AUTHORIZE_OR_BREAK(request->db_name(), request->table_name(), ALIAS);

        if (!schema::NamingChecker::is_legal_table_name(request->alias())) {
            status->set_code(ERR_INVALID_ALIAS);
            status->set_msg("Invalid table alias");
            break;
        }

        MASTER_SERVICE_CHECK_DB_OR_BREAK;
        MASTER_SERVICE_CHECK_TABLE_OR_BREAK;

        pb::AliasTableRequest new_request;
        new_request.CopyFrom(*request);
        new_request.set_log_id(log_id);

        g_master_control->alias_table(cntl, &new_request, response);

    } while (0);

    RPC_ACK_LOG(NOTICE);
}

void MasterDatabaseServiceImpl::unalias_table(::google::protobuf::RpcController* controller,
        const pb::UnaliasTableRequest* request,
        pb::AckResponse* response,
        ::google::protobuf::Closure* done) {
    baidu::rpc::Controller* cntl = (baidu::rpc::Controller*)controller;
    baidu::rpc::ClosureGuard done_guard(done);
    uint64_t log_id = cntl->log_id();
    base::EndPoint remote_addr = cntl->remote_side();
    auto status = response->mutable_status();

    do {
        DATABASE_SERVICE_CHECK_OR_BREAK;
        DATABASE_SERVICE_AHTENTICATE_OR_BREAK;
        DATABASE_SERVICE_AUTHORIZE_OR_BREAK(request->db_name(), request->table_name(), ALIAS);

        MASTER_SERVICE_CHECK_DB_OR_BREAK;
        MASTER_SERVICE_CHECK_TABLE_OR_BREAK;

        pb::UnaliasTableRequest new_request;
        new_request.CopyFrom(*request);
        new_request.set_log_id(log_id);

        g_master_control->unalias_table(cntl, &new_request, response);

    } while (0);

    RPC_ACK_LOG(NOTICE);
}

void MasterDatabaseServiceImpl::add_column(::google::protobuf::RpcController* controller,
        const pb::AddColumnRequest* request,
        pb::AckResponse* response,
        ::google::protobuf::Closure* done){
    baidu::rpc::Controller* cntl = (baidu::rpc::Controller*)controller;
    baidu::rpc::ClosureGuard done_guard(done);
    uint64_t log_id = cntl->log_id();
    base::EndPoint remote_addr = cntl->remote_side();
    auto status = response->mutable_status();
    RPC_REQUEST_LOG(NOTICE);

    do {
        DATABASE_SERVICE_CHECK_OR_BREAK;
        DATABASE_SERVICE_AHTENTICATE_OR_BREAK;
        DATABASE_SERVICE_AUTHORIZE_OR_BREAK(request->db_name(), request->table_name(), ALTER_TABLE);

        MASTER_SERVICE_CHECK_DB_OR_BREAK;
        MASTER_SERVICE_CHECK_TABLE_OR_BREAK;

        pb::AddColumnRequest new_request;
        new_request.CopyFrom(*request);
        new_request.set_log_id(log_id);

        g_master_control->add_column(cntl, &new_request, response);
    } while (0);

    RPC_ACK_LOG(NOTICE);
}

void MasterDatabaseServiceImpl::show_table(::google::protobuf::RpcController* controller,
        const pb::ShowTableRequest* request,
        pb::ShowTableResponse* response,
        ::google::protobuf::Closure* done){
    baidu::rpc::Controller* cntl = (baidu::rpc::Controller*)controller;
    baidu::rpc::ClosureGuard done_guard(done);
    uint64_t log_id = cntl->log_id();
    base::EndPoint remote_addr = cntl->remote_side();
    auto status = response->mutable_status();
    RPC_REQUEST_LOG(TRACE);

    do {
        DATABASE_SERVICE_CHECK_OR_BREAK;
        DATABASE_SERVICE_AHTENTICATE_OR_BREAK;

        TableRef table = nullptr;
        if (request->has_table_id()) {
            table = g_db_manager->get_table(request->table_id());
        } else {
            DATABASE_SERVICE_AUTHORIZE_OR_BREAK(request->db_name(), request->table_name(), SHOW_TABLE);
            table = g_db_manager->get_table(request->db_name(), request->table_name());
        }

        if (!table) {
            status->set_code(ERR_TABLE_NOT_EXIST);
            status->set_msg("Table not exist");
            break;
        }
        table->serialize(response->mutable_table_info());
        if (!request->has_table_id()) {
            response->set_table_id(table->id());
        }
        status->set_code(OK);

    } while (0);

    RPC_ACK_LOG(TRACE);
}

void MasterDatabaseServiceImpl::list_table(::google::protobuf::RpcController* controller,
        const pb::ListTableRequest* request,
        pb::ListTableResponse* response,
        ::google::protobuf::Closure* done){
    baidu::rpc::Controller* cntl = (baidu::rpc::Controller*)controller;
    baidu::rpc::ClosureGuard done_guard(done);
    uint64_t log_id = cntl->log_id();
    base::EndPoint remote_addr = cntl->remote_side();
    auto status = response->mutable_status();
    RPC_REQUEST_LOG(TRACE);

    do {
        DATABASE_SERVICE_CHECK_OR_BREAK;
        DATABASE_SERVICE_AHTENTICATE_OR_BREAK;
        GET_AUTHORIZED_TABLE_LIST(request->db_name(), SHOW_TABLE);

        auto db = g_db_manager->get_db(request->db_name());
        if (!db) {
            status->set_code(ERR_DB_NOT_EXIST);
            status->set_msg("Database not exist");
        } else {
            bool all_table = authoried_table_list.count("*") == 1;
            std::vector<TableRef> table_list;
            db->list_table(&table_list);
            for (const auto& table : table_list) {
                auto it = authoried_table_list.find(table->name());
                if (!all_table && it == authoried_table_list.end()) {
                    continue;
                }
                table->serialize(response->add_table_infos());
            }
            status->set_code(OK);
        }
    } while (0);

    RPC_ACK_LOG(TRACE);
}

void MasterDatabaseServiceImpl::create_index(::google::protobuf::RpcController* controller,
        const pb::CreateIndexRequest* request,
        pb::AckResponse* response,
        ::google::protobuf::Closure* done){
    baidu::rpc::Controller* cntl = (baidu::rpc::Controller*)controller;
    baidu::rpc::ClosureGuard done_guard(done);
    uint64_t log_id = cntl->log_id();
    base::EndPoint remote_addr = cntl->remote_side();
    auto status = response->mutable_status();
    RPC_REQUEST_LOG(NOTICE);

    do {
        DATABASE_SERVICE_CHECK_OR_BREAK;
        DATABASE_SERVICE_AHTENTICATE_OR_BREAK;
        DATABASE_SERVICE_AUTHORIZE_OR_BREAK(request->db_name(), request->table_name(), ALTER_TABLE);

        MASTER_SERVICE_CHECK_DB_OR_BREAK;
        MASTER_SERVICE_CHECK_TABLE_OR_BREAK;

        if (request->vector_indexes_size() + request->scalar_indexes_size() == 0) {
            status->set_code(ERR_INVALID_ARGUMENT);
            status->set_msg("No any index specified");
            break;
        }

        for (int i = 0; i < request->vector_indexes_size(); i++) {
            const mochow::pb::PVectorIndex& index = request->vector_indexes(i);
            if (!index.is_auto_build()) {
                continue;
            }
            const mochow::pb::AutoBuildIndexParam param = index.auto_build_index_param();
            check_index_auto_build_param(param, status);
            if (status->code() != OK) {
                break;
            }
        }

        if (status->code() != OK) {
            break;
        }

        // check whether scalar index async build is enabled
        if (!FLAGS_enable_async_build_scalar_index && request->scalar_indexes_size() > 0) {
            status->set_code(ERR_NOT_SUPPORT);
            status->set_msg("Not support create scalar index");
            break;
        }

        for (int i = 0; i < request->scalar_indexes_size(); i++) {
            const mochow::pb::PScalarIndex& index = request->scalar_indexes(i);
            const std::string& index_type = index.schema().index_param().index_type();
            if (index_type != schema::ScalarIndexType::FILTERING) {
                status->set_code(ERR_NOT_SUPPORT);
                status->set_msg("Only support create filtering scalar index");
                break;
            }
        }

        if (status->code() != OK) {
            break;
        }

        pb::CreateIndexRequest new_request;
        new_request.CopyFrom(*request);
        new_request.set_log_id(log_id);

        g_master_control->create_index(cntl, &new_request, response);
    } while (0);

    RPC_ACK_LOG(NOTICE);
}

void MasterDatabaseServiceImpl::drop_index(::google::protobuf::RpcController* controller,
        const pb::DropIndexRequest* request,
        pb::AckResponse* response,
        ::google::protobuf::Closure* done){
    baidu::rpc::Controller* cntl = (baidu::rpc::Controller*)controller;
    baidu::rpc::ClosureGuard done_guard(done);
    uint64_t log_id = cntl->log_id();
    base::EndPoint remote_addr = cntl->remote_side();
    auto status = response->mutable_status();
    RPC_REQUEST_LOG(NOTICE);

    do {
        DATABASE_SERVICE_CHECK_OR_BREAK;
        DATABASE_SERVICE_AHTENTICATE_OR_BREAK;
        DATABASE_SERVICE_AUTHORIZE_OR_BREAK(request->db_name(), request->table_name(), ALTER_TABLE);

        MASTER_SERVICE_CHECK_DB_OR_BREAK;
        MASTER_SERVICE_CHECK_TABLE_OR_BREAK;

        if (!request->has_index_name() || !request->has_index_id()) {
            status->set_code(ERR_INVALID_ARGUMENT);
            status->set_msg("Index name or id not specified");
            break;
        }

        pb::DropIndexRequest new_request;
        new_request.CopyFrom(*request);
        new_request.set_log_id(log_id);

        g_master_control->drop_index(cntl, &new_request, response);
    } while (0);

    RPC_ACK_LOG(NOTICE);
}

void MasterDatabaseServiceImpl::rebuild_index(::google::protobuf::RpcController* controller,
        const pb::RebuildIndexRequest* request,
        pb::AckResponse* response,
        ::google::protobuf::Closure* done){
    baidu::rpc::Controller* cntl = (baidu::rpc::Controller*)controller;
    baidu::rpc::ClosureGuard done_guard(done);
    uint64_t log_id = cntl->log_id();
    base::EndPoint remote_addr = cntl->remote_side();
    auto status = response->mutable_status();
    RPC_REQUEST_LOG(NOTICE);

    do {
        DATABASE_SERVICE_CHECK_OR_BREAK;
        DATABASE_SERVICE_AHTENTICATE_OR_BREAK;
        DATABASE_SERVICE_AUTHORIZE_OR_BREAK(request->db_name(), request->table_name(), BUILD_INDEX);

        MASTER_SERVICE_CHECK_DB_OR_BREAK;
        MASTER_SERVICE_CHECK_TABLE_OR_BREAK;

        pb::RebuildIndexRequest new_request;
        new_request.CopyFrom(*request);
        new_request.set_log_id(log_id);

        g_master_control->rebuild_index(cntl, &new_request, response);
    } while (0);

    RPC_ACK_LOG(NOTICE);
}

void MasterDatabaseServiceImpl::show_index(::google::protobuf::RpcController* controller,
        const pb::ShowIndexRequest* request,
        pb::ShowIndexResponse* response,
        ::google::protobuf::Closure* done) {
    baidu::rpc::Controller* cntl = (baidu::rpc::Controller*)controller;
    baidu::rpc::ClosureGuard done_guard(done);
    uint64_t log_id = cntl->log_id();
    base::EndPoint remote_addr = cntl->remote_side();
    auto status = response->mutable_status();
    status->set_code(OK);
    RPC_REQUEST_LOG(TRACE);

    do {
        DATABASE_SERVICE_CHECK_OR_BREAK;
        DATABASE_SERVICE_AHTENTICATE_OR_BREAK;
        DATABASE_SERVICE_AUTHORIZE_OR_BREAK(request->db_name(), request->table_name(), SHOW_TABLE);

        MASTER_SERVICE_CHECK_DB_OR_BREAK;
        MASTER_SERVICE_CHECK_TABLE_OR_BREAK;

        auto index = table->show_index(request->index_name());
        if (index == nullptr) {
            status->set_code(ERR_INDEX_NOT_EXIST);
            status->set_msg("Index not found");
            break;
        }

        if (index->index_species() == schema::IndexSpecies::VECTOR) {
            VectorIndexRef vindex = std::static_pointer_cast<VectorIndex>(index);
            vindex->serialize(response->mutable_vector_index());
            response->set_index_species(schema::IndexSpecies::VECTOR);
        } else if (schema::IndexSpecies::is_compatible_scalar_index_species(index->index_species())) {
            ScalarIndexRef sindex = std::static_pointer_cast<ScalarIndex>(index);
            sindex->serialize(response->mutable_scalar_index());
            response->set_index_species(schema::IndexSpecies::SCALAR);
        } else if (index->index_species() == schema::IndexSpecies::INVERTED) {
            InvertedIndexRef inverted_index = std::static_pointer_cast<InvertedIndex>(index);
            inverted_index->serialize(response->mutable_inverted_index());
            response->set_index_species(schema::IndexSpecies::INVERTED);
    	}

    } while (0);

    RPC_ACK_LOG(TRACE);
}

void MasterDatabaseServiceImpl::list_index(::google::protobuf::RpcController* controller,
        const pb::ListIndexRequest* request,
        pb::ListIndexResponse* response,
        ::google::protobuf::Closure* done) {
    baidu::rpc::Controller* cntl = (baidu::rpc::Controller*)controller;
    baidu::rpc::ClosureGuard done_guard(done);
    uint64_t log_id = cntl->log_id();
    base::EndPoint remote_addr = cntl->remote_side();
    auto status = response->mutable_status();
    status->set_code(OK);
    RPC_REQUEST_LOG(TRACE);

    do {
        DATABASE_SERVICE_CHECK_OR_BREAK;
        DATABASE_SERVICE_AHTENTICATE_OR_BREAK;
        DATABASE_SERVICE_AUTHORIZE_OR_BREAK(request->db_name(), request->table_name(), SHOW_TABLE);

        MASTER_SERVICE_CHECK_DB_OR_BREAK;
        MASTER_SERVICE_CHECK_TABLE_OR_BREAK;

        std::vector<VectorIndexRef> vindexes;
        std::vector<ScalarIndexRef> sindexes;
	std::vector<InvertedIndexRef>  inverted_indexes;
        table->list_vector_index(&vindexes);
        table->list_scalar_index(&sindexes);
        table->list_inverted_index(&inverted_indexes);
        for (const auto index : vindexes) {
            index->serialize(response->add_vector_indexes());
        }
        for (const auto index : sindexes) {
            index->serialize(response->add_scalar_indexes());
        }
        for (const auto& index : inverted_indexes) {
            index->serialize(response->add_inverted_indexes());
        }

    } while (0);

    RPC_ACK_LOG(TRACE);
}

void MasterDatabaseServiceImpl::modify_index(::google::protobuf::RpcController* controller,
        const pb::ModifyIndexRequest* request,
        pb::AckResponse* response,
        ::google::protobuf::Closure* done) {
    baidu::rpc::Controller* cntl = (baidu::rpc::Controller*)controller;
    baidu::rpc::ClosureGuard done_guard(done);
    uint64_t log_id = cntl->log_id();
    base::EndPoint remote_addr = cntl->remote_side();
    auto status = response->mutable_status();
    RPC_REQUEST_LOG(NOTICE);

    do {
        DATABASE_SERVICE_CHECK_OR_BREAK;
        DATABASE_SERVICE_AHTENTICATE_OR_BREAK;
        DATABASE_SERVICE_AUTHORIZE_OR_BREAK(request->db_name(), request->table_name(), CONFIG_INDEX);

        if (!request->has_is_auto_build()) {
            status->set_code(ERR_INVALID_ARGUMENT);
            status->set_msg("Attribute `is_auto_build` is not specified");
            break;
        }

        // Disable auto build, just directly step into state machine
        if (!request->is_auto_build()) {
            pb::ModifyIndexRequest new_request;
            new_request.CopyFrom(*request);
            new_request.set_log_id(log_id);
            g_master_control->modify_index(cntl, &new_request, response);
            break;
        }

        // Enable auto build, need check many params
        if (!request->has_auto_build_index_param()) {
            status->set_code(ERR_INVALID_ARGUMENT);
            status->set_msg("Auto build param not specified");
            break;
        }
        if (!request->auto_build_index_param().has_policy_type()) {
            status->set_code(ERR_INVALID_ARGUMENT);
            status->set_msg("Policy type for auto build not specified");
            break;
        }
        if (!request->auto_build_index_param().has_policy_param()) {
            status->set_code(ERR_INVALID_ARGUMENT);
            status->set_msg("Policy param for auto build not specified");
            break;
        }
        const mochow::pb::AutoBuildIndexParam param = request->auto_build_index_param();
        check_index_auto_build_param(param, status);
        if (status->code() != OK) {
            break;
        }

        pb::ModifyIndexRequest new_request;
        new_request.CopyFrom(*request);
        new_request.set_log_id(log_id);

        g_master_control->modify_index(cntl, &new_request, response);

    } while (0);

    RPC_ACK_LOG(NOTICE);
}

#define DATABASE_SERVICE_CHECK_USER_OR_BREAK(_USERNAME_) \
        if (_USERNAME_.empty()) { \
            status->set_code(ERR_INVALID_USERNAME); \
            status->set_msg("Username is empty"); \
            break; \
        } \
        if (!g_rbac_manager->is_user_exist(_USERNAME_)) { \
            status->set_code(ERR_USER_NOT_EXIST); \
            status->set_msg("User not exist"); \
            break; \
        }

#define DATABASE_SERVICE_CHECK_ROLE_OR_BREAK(_ROLENAME_) \
        if (_ROLENAME_.empty()) { \
            status->set_code(ERR_INVALID_ROLE_NAME); \
            status->set_msg("Role name is empty"); \
            break; \
        } \
        if (!g_rbac_manager->is_role_exist(_ROLENAME_)) { \
            status->set_code(ERR_ROLE_NOT_EXIST); \
            status->set_msg("Role not exist"); \
            break; \
        }

static bool translate_priv_string_to_priv_bitset(
                    const std::string& privilege,
                    PrivilegeBitset& system_priv_bitset,
                    PrivilegeBitset& priv_bitset) {

    const PrivilegeBitset* priv_group_bitset = get_privilege_group_bitset(privilege);
    if (priv_group_bitset != nullptr) {
        if (is_all_privilege_group(privilege)) {
            /* Carefully, testing priv_id from 1, not 0 (USAGE) */
            for (PrivilegeId priv_id = 1; priv_id < MOCHOW_PRIVILEGE_COUNT; ++priv_id) {
                if (is_system_privilege(priv_id)) {
                    system_priv_bitset.set(priv_id);
                } else {
                    priv_bitset.set(priv_id);
                }
            }
        } else if (is_system_privilege_group(privilege)) {
            system_priv_bitset |= *priv_group_bitset;
        } else {
            priv_bitset |= *priv_group_bitset;
        }
    } else {
        const auto priv_id = get_privilege_id_by_name(privilege);
        if (priv_id == INVALID_PRIVILEGE_ID) {
            return false;
        }
        if (is_system_privilege(priv_id)) {
            system_priv_bitset.set(priv_id);
        } else {
            priv_bitset.set(priv_id);
        }
    }

    return true;
}

template<
    typename T,
    typename baidu::sfl::enable_if_t<
        baidu::sfl::disjunction<
            std::is_same<T, pb::GrantUserPrivilegesRequest>,
            std::is_same<T, pb::RevokeUserPrivilegesRequest>,
            std::is_same<T, pb::GrantRolePrivilegesRequest>,
            std::is_same<T, pb::RevokeRolePrivilegesRequest>
        >::value
    >* = nullptr>
static bool parse_and_construct_privilege_tuples(
                const T* request, T* new_request,
                mochow::pb::Status* status) {

    PrivilegeBitset system_priv_bitset;

    // check for each priv tuple
    for (int i = 0; i < request->priv_tuples_size(); ++i) {
        const auto& priv_tuple = request->priv_tuples(i);
        const std::string database = priv_tuple.object().database();
        const std::string table = priv_tuple.object().table();

        // check rbac object
        common::RBACObject object(database, table);
        if (!common::RBACObject::is_valid_rbac_object(object)) {
            status->set_code(ERR_INVALID_RBAC_OBJECT);
            status->set_msg("Invalid rbac object");
            return false;
        }

        // check db and table
        if (database != "*") {
            auto db = g_db_manager->get_db(database);
            if (db == nullptr) {
                status->set_code(ERR_DB_NOT_EXIST);
                status->set_msg("Database not found");
                return false;
            }
            if (table != "*" && db->get_table(table) == nullptr) {
                status->set_code(ERR_TABLE_NOT_EXIST);
                status->set_msg("Table not found");
                return false;
            }
        }

        // check for each privilege
        PrivilegeBitset priv_bitset;
        for (int j = 0; j < priv_tuple.privileges_size(); ++j) {
            if (!translate_priv_string_to_priv_bitset(priv_tuple.privileges(j),
                                                      system_priv_bitset,
                                                      priv_bitset)) {
                status->set_code(ERR_PRIVILEGE_NOT_FOUND);
                status->set_msg("Privilege not found");
                return false;
            }
        }

        if (priv_bitset.any()) {
            auto *new_priv_tuple = new_request->add_priv_tuples();
            object.serialize(new_priv_tuple->mutable_object());
            new_priv_tuple->set_priv_bitset(priv_bitset.to_string());
        }
    }

    if (system_priv_bitset.any()) {
        auto *priv_tuple = new_request->add_priv_tuples();
        priv_tuple->mutable_object()->set_database("*");
        priv_tuple->mutable_object()->set_table("*");
        priv_tuple->set_priv_bitset(system_priv_bitset.to_string());
    }

    return true;
}

void MasterDatabaseServiceImpl::create_user(::google::protobuf::RpcController* controller,
        const pb::CreateUserRequest* request,
        pb::AckResponse* response,
        ::google::protobuf::Closure* done) {
    baidu::rpc::Controller* cntl = (baidu::rpc::Controller*)controller;
    baidu::rpc::ClosureGuard done_guard(done);
    uint64_t log_id = cntl->log_id();
    base::EndPoint remote_addr = cntl->remote_side();
    auto status = response->mutable_status();
    RPC_REQUEST_LOG(NOTICE);

    do {
        DATABASE_SERVICE_CHECK_OR_BREAK;
        DATABASE_SERVICE_AHTENTICATE_OR_BREAK;
        DATABASE_SERVICE_AUTHORIZE_OR_BREAK("*", "*", CREATE_USER);

        // Check new user's username & password
        const auto& user = request->target_user();
        if (user.username().empty()) {
            status->set_code(ERR_INVALID_USERNAME);
            status->set_msg("Username is empty");
            break;
        }

        if (user.password().empty()) {
            status->set_code(ERR_INVALID_PASSWORD);
            status->set_msg("Password is empty");
            break;
        }

        if (g_rbac_manager->is_user_exist(user.username())) {
            status->set_code(ERR_USER_ALREADY_EXIST);
            status->set_msg("User already exist");
            break;
        }

        pb::CreateUserRequest new_request;
        new_request.CopyFrom(*request);
        new_request.set_log_id(log_id);

        g_master_control->create_user(cntl, &new_request, response);

    } while (0);

    RPC_ACK_LOG(NOTICE);
}

void MasterDatabaseServiceImpl::drop_user(::google::protobuf::RpcController* controller,
        const pb::DropUserRequest* request,
        pb::AckResponse* response,
        ::google::protobuf::Closure* done) {
    baidu::rpc::Controller* cntl = (baidu::rpc::Controller*)controller;
    baidu::rpc::ClosureGuard done_guard(done);
    uint64_t log_id = cntl->log_id();
    base::EndPoint remote_addr = cntl->remote_side();
    auto status = response->mutable_status();
    RPC_REQUEST_LOG(NOTICE);

    do {
        DATABASE_SERVICE_CHECK_OR_BREAK;
        DATABASE_SERVICE_AHTENTICATE_OR_BREAK;
        DATABASE_SERVICE_AUTHORIZE_OR_BREAK("*", "*", DROP_USER);

        const auto& user = request->target_user();
        DATABASE_SERVICE_CHECK_USER_OR_BREAK(user.username());

        if (is_system_user(user.username()) || is_root_user(user.username())) {
            status->set_code(ERR_REJECTED);
            status->set_msg("Cannot drop builtin user");
            break;
        }

        // Verify auth user is the ancestor of the target user
        if (!g_rbac_manager->is_ancestor_to_user(auth_info.username(), user.username())) {
            status->set_code(ERR_REJECTED);
            status->set_msg("Invalid bloodline");
            break;
        }

        pb::DropUserRequest new_request;
        new_request.CopyFrom(*request);
        new_request.set_log_id(log_id);

        g_master_control->drop_user(cntl, &new_request, response);

    } while (0);

    RPC_ACK_LOG(NOTICE);
}

void MasterDatabaseServiceImpl::change_password(::google::protobuf::RpcController* controller,
        const pb::ChangePasswordRequest* request,
        pb::AckResponse* response,
        ::google::protobuf::Closure* done) {
    baidu::rpc::Controller* cntl = (baidu::rpc::Controller*)controller;
    baidu::rpc::ClosureGuard done_guard(done);
    uint64_t log_id = cntl->log_id();
    base::EndPoint remote_addr = cntl->remote_side();
    auto status = response->mutable_status();
    RPC_REQUEST_LOG(NOTICE);

    do {
        DATABASE_SERVICE_CHECK_OR_BREAK;

        // Athenticate
        const auto& auth_info = request->auth_info();
        const auto auth_user = g_rbac_manager->get_user(auth_info.username());
        if (auth_user == nullptr
                || !auth_user->authenticate(auth_info.username(), auth_info.password())) {
            status->set_code(ERR_AUTHENTICATE_FAILED);
            status->set_msg("Authenticate Failed");
            break;
        }

        // Check target user
        DATABASE_SERVICE_CHECK_USER_OR_BREAK(request->target_user().username());

        // Check new password
        if (request->target_user().password().empty()) {
            status->set_code(ERR_INVALID_PASSWORD);
            status->set_msg("New password is empty");
            return;
        }

        // Password check matrix:
        //    1) vertical axis are auth users, horizontal axis are target users
        //    2) 'x' = denied, 'y' = passed, 'P' = need check privilege of auth user
        //    3) 'y / AP' means conditional, if same user then directly pass, or else:
        //        auth user has privilege and is the ancestor of target user
        //
        // RBAC Promises:
        //    1) '__system' and 'root' always have the PASSWORD privilege.
        //    2) '__system' is always the ancestor of 'root'.
        //    3) 'root' is always the ancestor of any other common user.
        //
        // Thus, the check matrix can be simplified as 2 conditions:
        //    1) If auth user is the same of target user, directly pass;
        //    2) Else if auth user is the ancestor of target user and has PASSWORD privilege,
        //       just pass.
        //
        // +----------+----------+----------+----------+
        // |          | __system |   root   |  common  |
        // +----------+----------+----------+----------+
        // | __system |     y    |     y    |     y    |
        // +----------+----------+----------+----------+
        // |   root   |     x    |     y    |     P    |
        // +----------+----------+----------+----------+
        // |  common  |     x    |     x    |   y / AP |
        // +----------+----------+----------+----------+

        bool passed = false;

        do {
            // Any user can change himself's password without any authorization
            if (auth_info.username() == request->target_user().username()) {
                passed = true;
                break;
            }

            // Else if auth user is the ancestor of target user and auth user has PASSWORD privilege,
            // then auth user can change password of the target user
            if (g_rbac_manager->is_ancestor_to_user(auth_info.username(),
                                                    request->target_user().username())
                    && auth_user->authorize("*", "*", Privilege::PASSWORD)) {
                passed = true;
                break;
            }
        } while (0);

        if (!passed) {
            status->set_code(ERR_PRIVILEGE_DENIED);
            status->set_msg("Privilege Denied");
            break;
        }

        pb::ChangePasswordRequest new_request;
        new_request.CopyFrom(*request);
        new_request.set_log_id(log_id);

        g_master_control->change_password(cntl, &new_request, response);

    } while (0);

    RPC_ACK_LOG(NOTICE);
}

void MasterDatabaseServiceImpl::create_role(::google::protobuf::RpcController* controller,
        const pb::CreateRoleRequest* request,
        pb::AckResponse* response,
        ::google::protobuf::Closure* done) {
    baidu::rpc::Controller* cntl = (baidu::rpc::Controller*)controller;
    baidu::rpc::ClosureGuard done_guard(done);
    uint64_t log_id = cntl->log_id();
    base::EndPoint remote_addr = cntl->remote_side();
    auto status = response->mutable_status();
    RPC_REQUEST_LOG(NOTICE);

    do {
        DATABASE_SERVICE_CHECK_OR_BREAK;
        DATABASE_SERVICE_AHTENTICATE_OR_BREAK;
        DATABASE_SERVICE_AUTHORIZE_OR_BREAK("*", "*", CREATE_ROLE);

        if (request->role().empty()) {
            status->set_code(ERR_INVALID_ROLE_NAME);
            status->set_msg("Role name is empty");
            break;
        }

        if (g_rbac_manager->is_role_exist(request->role())) {
            status->set_code(ERR_ROLE_ALREADY_EXIST);
            status->set_msg("Role already exist");
            break;
        }

        pb::CreateRoleRequest new_request;
        new_request.CopyFrom(*request);
        new_request.set_log_id(log_id);

        g_master_control->create_role(cntl, &new_request, response);

    } while (0);

    RPC_ACK_LOG(NOTICE);
}

void MasterDatabaseServiceImpl::drop_role(::google::protobuf::RpcController* controller,
        const pb::DropRoleRequest* request,
        pb::AckResponse* response,
        ::google::protobuf::Closure* done) {
    baidu::rpc::Controller* cntl = (baidu::rpc::Controller*)controller;
    baidu::rpc::ClosureGuard done_guard(done);
    uint64_t log_id = cntl->log_id();
    base::EndPoint remote_addr = cntl->remote_side();
    auto status = response->mutable_status();
    RPC_REQUEST_LOG(NOTICE);

    do {
        DATABASE_SERVICE_CHECK_OR_BREAK;
        DATABASE_SERVICE_AHTENTICATE_OR_BREAK;
        DATABASE_SERVICE_AUTHORIZE_OR_BREAK("*", "*", DROP_ROLE);

        if (request->role().empty()) {
            status->set_code(ERR_INVALID_ROLE_NAME);
            status->set_msg("Role name is empty");
            break;
        }

        if (!g_rbac_manager->is_role_exist(request->role())) {
            status->set_code(ERR_ROLE_NOT_EXIST);
            status->set_msg("Role not exist");
            break;
        }

        if (is_builtin_admin_role(request->role())) {
            status->set_code(ERR_REJECTED);
            status->set_msg("Cannot drop builtin role");
            break;
        }

        // Verify auth user is the ancestor of the target role
        if (!g_rbac_manager->is_ancestor_to_role(auth_info.username(), request->role())) {
            status->set_code(ERR_REJECTED);
            status->set_msg("Invalid bloodline");
            break;
        }

        pb::DropRoleRequest new_request;
        new_request.CopyFrom(*request);
        new_request.set_log_id(log_id);

        g_master_control->drop_role(cntl, &new_request, response);

    } while (0);

    RPC_ACK_LOG(NOTICE);
}

void MasterDatabaseServiceImpl::grant_user_roles(::google::protobuf::RpcController* controller,
        const pb::GrantUserRolesRequest* request,
        pb::AckResponse* response,
        ::google::protobuf::Closure* done) {
    baidu::rpc::Controller* cntl = (baidu::rpc::Controller*)controller;
    baidu::rpc::ClosureGuard done_guard(done);
    uint64_t log_id = cntl->log_id();
    base::EndPoint remote_addr = cntl->remote_side();
    auto status = response->mutable_status();
    RPC_REQUEST_LOG(NOTICE);

    do {
        DATABASE_SERVICE_CHECK_OR_BREAK;
        DATABASE_SERVICE_AHTENTICATE_OR_BREAK;
        DATABASE_SERVICE_AUTHORIZE_OR_BREAK("*", "*", GRANT_REVOKE);

        DATABASE_SERVICE_CHECK_USER_OR_BREAK(request->username());

        // Verify auth user is the ancestor of the target user
        if (!g_rbac_manager->is_ancestor_to_user(auth_info.username(), request->username())) {
            status->set_code(ERR_REJECTED);
            status->set_msg("Invalid bloodline");
            break;
        }

        if (request->roles_size() == 0) {
            status->set_code(ERR_INVALID_ARGUMENT);
            status->set_msg("No any role specified");
            break;
        }

        int i = 0;
        for (; i < request->roles_size(); ++i) {
            DATABASE_SERVICE_CHECK_ROLE_OR_BREAK(request->roles(i));
        }

        if (i < request->roles_size()) {
            break;
        }

        pb::GrantUserRolesRequest new_request;
        new_request.CopyFrom(*request);
        new_request.set_log_id(log_id);

        g_master_control->grant_user_roles(cntl, &new_request, response);

    } while (0);

    RPC_ACK_LOG(NOTICE);
}

void MasterDatabaseServiceImpl::revoke_user_roles(::google::protobuf::RpcController* controller,
        const pb::RevokeUserRolesRequest* request,
        pb::AckResponse* response,
        ::google::protobuf::Closure* done) {
    baidu::rpc::Controller* cntl = (baidu::rpc::Controller*)controller;
    baidu::rpc::ClosureGuard done_guard(done);
    uint64_t log_id = cntl->log_id();
    base::EndPoint remote_addr = cntl->remote_side();
    auto status = response->mutable_status();
    RPC_REQUEST_LOG(NOTICE);

    do {
        DATABASE_SERVICE_CHECK_OR_BREAK;
        DATABASE_SERVICE_AHTENTICATE_OR_BREAK;
        DATABASE_SERVICE_AUTHORIZE_OR_BREAK("*", "*", GRANT_REVOKE);

        DATABASE_SERVICE_CHECK_USER_OR_BREAK(request->username());

        if (is_system_user(request->username()) || is_root_user(request->username())) {
            status->set_code(ERR_REJECTED);
            status->set_msg("Cannot revoke any role from builtin user");
            break;
        }

        // Verify auth user is the ancestor of the target user
        if (!g_rbac_manager->is_ancestor_to_user(auth_info.username(), request->username())) {
            status->set_code(ERR_REJECTED);
            status->set_msg("Invalid bloodline");
            break;
        }

        if (request->roles_size() == 0) {
            status->set_code(ERR_INVALID_ARGUMENT);
            status->set_msg("No any role specified");
            break;
        }

        int i = 0;
        for (; i < request->roles_size(); ++i) {
            DATABASE_SERVICE_CHECK_ROLE_OR_BREAK(request->roles(i));
        }

        if (i < request->roles_size()) {
            break;
        }

        pb::RevokeUserRolesRequest new_request;
        new_request.CopyFrom(*request);
        new_request.set_log_id(log_id);

        g_master_control->revoke_user_roles(cntl, &new_request, response);

    } while (0);

    RPC_ACK_LOG(NOTICE);
}

void MasterDatabaseServiceImpl::grant_user_privileges(::google::protobuf::RpcController* controller,
        const pb::GrantUserPrivilegesRequest* request,
        pb::AckResponse* response,
        ::google::protobuf::Closure* done) {
    baidu::rpc::Controller* cntl = (baidu::rpc::Controller*)controller;
    baidu::rpc::ClosureGuard done_guard(done);
    uint64_t log_id = cntl->log_id();
    base::EndPoint remote_addr = cntl->remote_side();
    auto status = response->mutable_status();
    RPC_REQUEST_LOG(NOTICE);

    do {
        DATABASE_SERVICE_CHECK_OR_BREAK;
        DATABASE_SERVICE_AHTENTICATE_OR_BREAK;
        DATABASE_SERVICE_AUTHORIZE_OR_BREAK("*", "*", GRANT_REVOKE);

        DATABASE_SERVICE_CHECK_USER_OR_BREAK(request->username());

        // Verify auth user is the ancestor of the target user
        if (!g_rbac_manager->is_ancestor_to_user(auth_info.username(), request->username())) {
            status->set_code(ERR_REJECTED);
            status->set_msg("Invalid bloodline");
            break;
        }

        if (request->priv_tuples_size() == 0) {
            status->set_code(ERR_INVALID_ARGUMENT);
            status->set_msg("No any privilege tuple specified");
            break;
        }

        pb::GrantUserPrivilegesRequest new_request;
        new_request.CopyFrom(*request);
        new_request.set_log_id(log_id);
        new_request.clear_priv_tuples();

        // check all the privilege tuples
        if (!parse_and_construct_privilege_tuples(request, &new_request, status)) {
            LOG_AND_ASSERT(status->code() != OK);
            LOG_AND_ASSERT(!status->msg().empty());
            break;
        }

        g_master_control->grant_user_privileges(cntl, &new_request, response);

    } while (0);

    RPC_ACK_LOG(NOTICE);
}

void MasterDatabaseServiceImpl::revoke_user_privileges(::google::protobuf::RpcController* controller,
        const pb::RevokeUserPrivilegesRequest* request,
        pb::AckResponse* response,
        ::google::protobuf::Closure* done) {
    baidu::rpc::Controller* cntl = (baidu::rpc::Controller*)controller;
    baidu::rpc::ClosureGuard done_guard(done);
    uint64_t log_id = cntl->log_id();
    base::EndPoint remote_addr = cntl->remote_side();
    auto status = response->mutable_status();
    RPC_REQUEST_LOG(NOTICE);

    do {
        DATABASE_SERVICE_CHECK_OR_BREAK;
        DATABASE_SERVICE_AHTENTICATE_OR_BREAK;
        DATABASE_SERVICE_AUTHORIZE_OR_BREAK("*", "*", GRANT_REVOKE);

        DATABASE_SERVICE_CHECK_USER_OR_BREAK(request->username());

        if (is_root_user(request->username()) || is_system_user(request->username())) {
            status->set_code(ERR_REJECTED);
            status->set_msg("Cannot revoke any privilege from builtin user");
            break;
        }

        // Verify auth user is the ancestor of the target user
        if (!g_rbac_manager->is_ancestor_to_user(auth_info.username(), request->username())) {
            status->set_code(ERR_REJECTED);
            status->set_msg("Invalid bloodline");
            break;
        }

        if (request->priv_tuples_size() == 0) {
            status->set_code(ERR_INVALID_ARGUMENT);
            status->set_msg("No any privilege tuple specified");
            break;
        }

        pb::RevokeUserPrivilegesRequest new_request;
        new_request.CopyFrom(*request);
        new_request.set_log_id(log_id);
        new_request.clear_priv_tuples();

        // check all the privilege tuples
        if (!parse_and_construct_privilege_tuples(request, &new_request, status)) {
            LOG_AND_ASSERT(status->code() != OK);
            LOG_AND_ASSERT(!status->msg().empty());
            break;
        }

        g_master_control->revoke_user_privileges(cntl, &new_request, response);

    } while (0);

    RPC_ACK_LOG(NOTICE);
}

void MasterDatabaseServiceImpl::grant_role_privileges(::google::protobuf::RpcController* controller,
        const pb::GrantRolePrivilegesRequest* request,
        pb::AckResponse* response,
        ::google::protobuf::Closure* done) {
    baidu::rpc::Controller* cntl = (baidu::rpc::Controller*)controller;
    baidu::rpc::ClosureGuard done_guard(done);
    uint64_t log_id = cntl->log_id();
    base::EndPoint remote_addr = cntl->remote_side();
    auto status = response->mutable_status();
    RPC_REQUEST_LOG(NOTICE);

    do {
        DATABASE_SERVICE_CHECK_OR_BREAK;
        DATABASE_SERVICE_AHTENTICATE_OR_BREAK;
        DATABASE_SERVICE_AUTHORIZE_OR_BREAK("*", "*", GRANT_REVOKE);

        DATABASE_SERVICE_CHECK_ROLE_OR_BREAK(request->role());

        // Verify auth user is the ancestor of the target role
        if (!g_rbac_manager->is_ancestor_to_role(auth_info.username(), request->role())) {
            status->set_code(ERR_REJECTED);
            status->set_msg("Invalid bloodline");
            break;
        }

        if (request->priv_tuples_size() == 0) {
            status->set_code(ERR_INVALID_ARGUMENT);
            status->set_msg("No any privilege tuple specified");
            break;
        }

        pb::GrantRolePrivilegesRequest new_request;
        new_request.CopyFrom(*request);
        new_request.set_log_id(log_id);
        new_request.clear_priv_tuples();

        // check all the privilege tuples
        if (!parse_and_construct_privilege_tuples(request, &new_request, status)) {
            LOG_AND_ASSERT(status->code() != OK);
            LOG_AND_ASSERT(!status->msg().empty());
            break;
        }

        g_master_control->grant_role_privileges(cntl, &new_request, response);

    } while (0);

    RPC_ACK_LOG(NOTICE);
}

void MasterDatabaseServiceImpl::revoke_role_privileges(::google::protobuf::RpcController* controller,
        const pb::RevokeRolePrivilegesRequest* request,
        pb::AckResponse* response,
        ::google::protobuf::Closure* done) {
    baidu::rpc::Controller* cntl = (baidu::rpc::Controller*)controller;
    baidu::rpc::ClosureGuard done_guard(done);
    uint64_t log_id = cntl->log_id();
    base::EndPoint remote_addr = cntl->remote_side();
    auto status = response->mutable_status();
    RPC_REQUEST_LOG(NOTICE);

    do {
        DATABASE_SERVICE_CHECK_OR_BREAK;
        DATABASE_SERVICE_AHTENTICATE_OR_BREAK;
        DATABASE_SERVICE_AUTHORIZE_OR_BREAK("*", "*", GRANT_REVOKE);

        DATABASE_SERVICE_CHECK_ROLE_OR_BREAK(request->role());

        if (is_builtin_admin_role(request->role())) {
            status->set_code(ERR_REJECTED);
            status->set_msg("Cannot revoke any privilege from builtin role");
            break;
        }

        // Verify auth user is the ancestor of the target role
        if (!g_rbac_manager->is_ancestor_to_role(auth_info.username(), request->role())) {
            status->set_code(ERR_REJECTED);
            status->set_msg("Invalid bloodline");
            break;
        }

        if (request->priv_tuples_size() == 0) {
            status->set_code(ERR_INVALID_ARGUMENT);
            status->set_msg("No any privilege tuple specified");
            break;
        }

        pb::RevokeRolePrivilegesRequest new_request;
        new_request.CopyFrom(*request);
        new_request.set_log_id(log_id);
        new_request.clear_priv_tuples();

        // check all the privilege tuples
        if (!parse_and_construct_privilege_tuples(request, &new_request, status)) {
            LOG_AND_ASSERT(status->code() != OK);
            LOG_AND_ASSERT(!status->msg().empty());
            break;
        }

        g_master_control->revoke_role_privileges(cntl, &new_request, response);

    } while (0);

    RPC_ACK_LOG(NOTICE);
}

void MasterDatabaseServiceImpl::show_user_privileges(
        ::google::protobuf::RpcController* controller,
        const pb::ShowUserPrivilegesRequest* request,
        pb::ShowUserPrivilegesResponse* response,
        ::google::protobuf::Closure* done) {
    baidu::rpc::Controller* cntl = (baidu::rpc::Controller*)controller;
    baidu::rpc::ClosureGuard done_guard(done);
    uint64_t log_id = cntl->log_id();
    base::EndPoint remote_addr = cntl->remote_side();
    auto status = response->mutable_status();
    status->set_code(OK);
    RPC_REQUEST_LOG(TRACE);

    do {
        DATABASE_SERVICE_CHECK_OR_BREAK;
        DATABASE_SERVICE_AHTENTICATE_OR_BREAK;

        // Only verify authorization when auth user is not or not the ancestor of the target user
        if (request->auth_info().username() != request->username()
                && !g_rbac_manager->is_ancestor_to_user(request->auth_info().username(),
                                                        request->username())) {
            DATABASE_SERVICE_AUTHORIZE_OR_BREAK("*", "*", SHOW_USER);
        }

        const auto user = g_rbac_manager->get_user(request->username());
        if (user == nullptr) {
            status->set_code(ERR_USER_NOT_EXIST);
            status->set_msg("User not exist");
            break;
        }

        std::vector<RoleRef> roles;
        user->list_role(&roles);
        for (const auto role : roles) {
            auto* prole = response->add_roles();
            prole->set_role(role->name());
            std::map<common::RBACObject, PrivilegeBitset> privileges;
            role->show_privileges(&privileges);
            for (const auto& [object, priv_bitset] : privileges) {
                auto* priv_tuple = prole->add_priv_tuples();
                object.serialize(priv_tuple->mutable_object());
                priv_tuple->set_priv_bitset(priv_bitset.to_string());
            }
        }

        std::map<common::RBACObject, PrivilegeBitset> privileges;
        user->show_privileges(&privileges);
        for (auto& [object, priv_bitset] : privileges) {
            if (!common::RBACObject::is_root_rbac_object(object)) {
                priv_bitset.reset(0); // mask USAGE
                for (PrivilegeId priv_id = 1; priv_id < MOCHOW_PRIVILEGE_COUNT; ++priv_id) {
                    if (is_system_privilege(priv_id)) {
                        priv_bitset.reset(priv_id); // mask system privlege
                    }
                }
            }
            auto* priv_tuple = response->add_priv_tuples();
            object.serialize(priv_tuple->mutable_object());
            priv_tuple->set_priv_bitset(priv_bitset.to_string());
        }

        response->set_parent(user->get_parent());
        auto child_users = user->list_child_user();
        for (const auto username : child_users) {
            response->add_child_users(username);
        }
        auto child_roles = user->list_child_role();
        for (const auto role_name : child_roles) {
            response->add_child_roles(role_name);
        }

        response->set_username(request->username());

    } while (0);

    RPC_ACK_LOG(TRACE);
}

void MasterDatabaseServiceImpl::show_role_privileges(
        ::google::protobuf::RpcController* controller,
        const pb::ShowRolePrivilegesRequest* request,
        pb::ShowRolePrivilegesResponse* response,
        ::google::protobuf::Closure* done) {
    baidu::rpc::Controller* cntl = (baidu::rpc::Controller*)controller;
    baidu::rpc::ClosureGuard done_guard(done);
    uint64_t log_id = cntl->log_id();
    base::EndPoint remote_addr = cntl->remote_side();
    auto status = response->mutable_status();
    status->set_code(OK);
    RPC_REQUEST_LOG(TRACE);

    do {
        DATABASE_SERVICE_CHECK_OR_BREAK;
        DATABASE_SERVICE_AHTENTICATE_OR_BREAK;

        // Only verify authorization when auth user is not the ancestor of the target role
        if (!g_rbac_manager->is_ancestor_to_role(request->auth_info().username(),
                                                 request->role())) {
            DATABASE_SERVICE_AUTHORIZE_OR_BREAK("*", "*", SHOW_ROLE);
        }

        const auto role = g_rbac_manager->get_role(request->role());
        if (role == nullptr) {
            status->set_code(ERR_ROLE_NOT_EXIST);
            status->set_msg("Role not exist");
            break;
        }

        std::vector<UserRef> users;
        g_rbac_manager->select_user_by_role(request->role(), &users);
        for (const auto user : users) {
            response->add_usernames(user->username());
        }

        std::map<common::RBACObject, PrivilegeBitset> privileges;
        role->show_privileges(&privileges);
        for (auto& [object, priv_bitset] : privileges) {
            if (!common::RBACObject::is_root_rbac_object(object)) {
                priv_bitset.reset(0); // mask USAGE
                for (PrivilegeId priv_id = 1; priv_id < MOCHOW_PRIVILEGE_COUNT; ++priv_id) {
                    if (is_system_privilege(priv_id)) {
                        priv_bitset.reset(priv_id); // mask system privlege
                    }
                }
            }
            auto* priv_tuple = response->add_priv_tuples();
            object.serialize(priv_tuple->mutable_object());
            priv_tuple->set_priv_bitset(priv_bitset.to_string());
        }

        response->set_parent(role->get_parent());

        response->set_role(request->role());

    } while (0);

    RPC_ACK_LOG(TRACE);
}

void MasterDatabaseServiceImpl::select_user(
        ::google::protobuf::RpcController* controller,
        const pb::SelectUserRequest* request,
        pb::SelectUserResponse* response,
        ::google::protobuf::Closure* done) {
    baidu::rpc::Controller* cntl = (baidu::rpc::Controller*)controller;
    baidu::rpc::ClosureGuard done_guard(done);
    uint64_t log_id = cntl->log_id();
    base::EndPoint remote_addr = cntl->remote_side();
    auto status = response->mutable_status();
    status->set_code(OK);
    RPC_REQUEST_LOG(TRACE);

    do {
        DATABASE_SERVICE_CHECK_OR_BREAK;
        DATABASE_SERVICE_AHTENTICATE_OR_BREAK;
        DATABASE_SERVICE_AUTHORIZE_OR_BREAK("*", "*", SHOW_USER);

        // Construct privilege filters
        int i = 0;
        PrivilegeBitset system_priv_bitset;
        std::map<common::RBACObject, PrivilegeBitset> priv_filters;
        for (; i < request->priv_tuples_size(); ++i) {
            const auto& priv_tuple = request->priv_tuples(i);
            common::RBACObject object;
            if (!object.deserialize(priv_tuple.object())) {
                status->set_code(ERR_INVALID_RBAC_OBJECT);
                status->set_msg("Invalid rbac object");
                break;
            }

            int p = 0;
            PrivilegeBitset priv_bitset;
            for (; p < priv_tuple.privileges_size(); ++p) {
                if (!translate_priv_string_to_priv_bitset(priv_tuple.privileges(p),
                                                          system_priv_bitset,
                                                          priv_bitset)) {
                    status->set_code(ERR_PRIVILEGE_NOT_FOUND);
                    status->set_msg("Privilege not found");
                    break;
                }
            }

            if (p < priv_tuple.privileges_size()) {
                break;
            }

            priv_filters[object] |= priv_bitset;
        }

        if (i < request->priv_tuples_size()) {
            break;
        }

        if (system_priv_bitset.any()) {
            common::RBACObject root_object("*", "*");
            priv_filters[root_object] |= system_priv_bitset;
        }

        // Check roles
        for (i = 0; i < request->roles_size(); ++i) {
            if (!g_rbac_manager->is_role_exist(request->roles(i))) {
                status->set_code(ERR_ROLE_NOT_EXIST);
                status->set_msg("Role not exist");
                break;
            }
        }
        if (i < request->roles_size()) {
            break;
        }

        // Select users
        std::vector<UserRef> users;
        g_rbac_manager->list_user(&users);
        for (const auto user : users) {
            // Check role matches
            {
                bool matched = true;
                for (int i = 0; i < request->roles_size(); ++i) {
                    if (!user->is_role_granted(request->roles(i))) {
                        matched = false;
                        break;
                    }
                }
                if (!matched) {
                    continue;
                }
            }

            // Check privilege matches
            {
                bool matched = true;
                for (const auto& [object, priv_bitset] : priv_filters) {
                    if (!user->authorize(object.database, object.table, priv_bitset)) {
                        matched = false;
                        break;
                    }
                }
                if (!matched) {
                    continue;
                }
            }

            response->add_usernames(user->username());
        }

    } while (0);

    RPC_ACK_LOG(TRACE);
}

void MasterDatabaseServiceImpl::select_role(
        ::google::protobuf::RpcController* controller,
        const pb::SelectRoleRequest* request,
        pb::SelectRoleResponse* response,
        ::google::protobuf::Closure* done) {
    baidu::rpc::Controller* cntl = (baidu::rpc::Controller*)controller;
    baidu::rpc::ClosureGuard done_guard(done);
    uint64_t log_id = cntl->log_id();
    base::EndPoint remote_addr = cntl->remote_side();
    auto status = response->mutable_status();
    status->set_code(OK);
    RPC_REQUEST_LOG(TRACE);

    do {
        DATABASE_SERVICE_CHECK_OR_BREAK;
        DATABASE_SERVICE_AHTENTICATE_OR_BREAK;
        DATABASE_SERVICE_AUTHORIZE_OR_BREAK("*", "*", SHOW_ROLE);

        // Construct privilege filters
        int i = 0;
        PrivilegeBitset system_priv_bitset;
        std::map<common::RBACObject, PrivilegeBitset> priv_filters;
        for (; i < request->priv_tuples_size(); ++i) {
            const auto& priv_tuple = request->priv_tuples(i);
            common::RBACObject object;
            if (!object.deserialize(priv_tuple.object())) {
                status->set_code(ERR_INVALID_RBAC_OBJECT);
                status->set_msg("Invalid rbac object");
                break;
            }

            int p = 0;
            PrivilegeBitset priv_bitset;
            for (; p < priv_tuple.privileges_size(); ++p) {
                if (!translate_priv_string_to_priv_bitset(priv_tuple.privileges(p),
                                                          system_priv_bitset,
                                                          priv_bitset)) {
                    status->set_code(ERR_PRIVILEGE_NOT_FOUND);
                    status->set_msg("Privilege not found");
                    break;
                }
            }

            if (p < priv_tuple.privileges_size()) {
                break;
            }

            priv_filters[object] |= priv_bitset;
        }

        if (i < request->priv_tuples_size()) {
            break;
        }

        if (system_priv_bitset.any()) {
            common::RBACObject root_object("*", "*");
            priv_filters[root_object] |= system_priv_bitset;
        }

        // Select roles
        std::vector<RoleRef> roles;
        g_rbac_manager->list_role(&roles);
        for (const auto role : roles) {
            bool matched = true;
            for (const auto& [object, priv_bitset] : priv_filters) {
                if (!role->authorize(object.database, object.table, priv_bitset)) {
                    matched = false;
                    break;
                }
            }
            if (matched) {
                response->add_roles(role->name());
            }
        }

    } while (0);

    RPC_ACK_LOG(TRACE);
}

}
