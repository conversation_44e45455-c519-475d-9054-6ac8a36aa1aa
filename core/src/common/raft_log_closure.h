/**
 * MOCHOW - A Vector Database System.
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON> (<EMAIL>)
 * Date: 2023/10/18
 * Desciption: Definitions for Raft Log Closure
 *
 */

#pragma once

#include <raft/raft.h>
#include <baidu/rpc/controller.h>
#include <bthread/countdown_event.h>

#include "core/src/common/logging.h"
#include "core/src/common/pbutils.h"

namespace mochow::common {

template <typename Request, typename Response>
struct RaftLogClosure : public raft::Closure {
    RaftLogClosure(::baidu::rpc::Controller* controller_,
                        const Request* request_,
                        Response* response_)
            : log_id(0)
            , controller(controller_)
            , request(request_)
            , response(response_) {
        timestamp = base::monotonic_time_us();
        if (controller) {
            log_id = controller->log_id();
            req_addr = controller->remote_side();
        }
        LOG(TRACE) << "recv_cmd:" << request->GetTypeName()
                   << " log_id:" << log_id
                   << " req_addr:" << base::endpoint2str(req_addr).c_str()
                   << " request:" << common::pb2json(*request);
    }
    ~RaftLogClosure() = default;

    void wait() {
        event.wait();
    }

    void Run() {
        if (!status().ok()) {
            response->mutable_status()->set_code(status().error_code());
            response->mutable_status()->set_msg(status().error_cstr());
        } else {
            // Success
            response->mutable_status()->set_code(0);
            response->mutable_status()->set_msg("Success");
        }
        LOG(TRACE) << "recv_cmd:" << request->GetTypeName()
                   << " log_id:" << log_id
                   << " from_addr:" << base::endpoint2str(req_addr).c_str()
                   << " time_us: " << base::monotonic_time_us() - timestamp
                   << " error_code:" << status().error_code()
                   << " response: " << common::pb2json(*response);
        event.signal();
    }

    uint64_t log_id;
    int64_t timestamp;
    baidu::rpc::Controller* controller;
    const Request* request;
    Response* response;
    bthread::CountdownEvent event;
    base::EndPoint req_addr;
};

class RaftSynchronizedClosure : public raft::Closure {
public:
    RaftSynchronizedClosure() {
    }
    virtual void Run() override {
        _event.signal();
    }
    void wait() {
        _event.wait();
    }
private:
    bthread::CountdownEvent _event;
};
}
