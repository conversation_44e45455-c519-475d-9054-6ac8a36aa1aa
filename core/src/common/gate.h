#pragma once

#include "base/bthread/bthread.h"
#include "core/src/common/butex.h"
#include "sfl/class_helper.h"
#include "core/src/common/likely.h"
#include "sfl/logging.h"

namespace mochow::common {

// Facility to stop new requests, and to tell when existing requests are done.
// When stopping a service,  we are faced with two problems:
// 1. preventing new requests from coming in;
// 2. knowing when existing requests have completed;
// The Gate class provides a solution for graceful exit.
class Gate {
public:
    Gate() = default;
    DISABLE_COPY_AND_MOVE(Gate);

    ~Gate() {
        if (_count.load() != 0) {
            LOG(FATAL) << "Gate destroyed with outstanding requests, count:" << _count.load();
        }
    }

    // Tries to register an in-progress request.
    // If the gate is not closed, the request is registered and the function returns `true`,
    // Otherwise the function just returns `false` and has no other effect.
    bool try_enter() {
        bool opened = !_stopped.load();
        likely_if(opened) {
            ++_count;
            opened = !_stopped.load();
            unlikely_if(!opened) {
                --_count;
            }
        }

        return opened;
    }

    // Try enter until timeout.
    bool enter(int64_t max_wait_us) {
        // fast path: return if gate is opened
        likely_if(try_enter()) {
            return true;
        }

        // slow path: sleep and retry
        int64_t start_us = base::gettimeofday_us();
        do {
            bthread_yield();
            if (base::gettimeofday_us() - start_us >= max_wait_us) {
                LOG(WARNING) << "Fail to enter gate after timeout,"
                             << " max_wait_us:" << max_wait_us;
                return false;
            }

            if (try_enter()) {
                return true;  // success
            } // else retry

        } while (true);

        return false;
    }

    // Unregisters an in-progress request.
    // If the gate is closed, and there are no more in-progress requests,
    void leave() {
        LOG_AND_ASSERT(_count.fetch_sub(1) > 0);
    }

    // Closes the gate.
    // Blocking wait until all current requests call leave().
    // After executed, future calls to try_enter() will always return false.
    // Returns the number of in-progress requests.
    size_t close(int64_t max_wait_us = 60 * 1000 * 1000)  { // default wait 60s
        common::ScopedMutexLock lock(_close_mutex);

        if (_stopped.exchange(true)) {
            // gate was closed
            LOG(FATAL) << "Gate is already closed";
            return _count;
        }

        int32_t count = _count.load();
        if (count > 0) {
            // wait all request leave, wait max 60s
            int64_t start_us = base::gettimeofday_us();
            do {
                // sleep and check count
                bthread_yield();
                count = _count.load();
                if (count <= 0) {
                    return 0;
                }

                // return if timeout
                if (base::gettimeofday_us() - start_us >= max_wait_us) {
                    LOG(WARNING) << "Close gate timeout when wait in-progress request,"
                                 << " max_wait_us:" << max_wait_us;
                    return count;
                }  // else retry
            } while (true);
        }

        return 0;
    }

    void reopen() {
        common::ScopedMutexLock lock(_close_mutex);
        if (!_stopped.exchange(false)) {
            LOG(FATAL) << "Gate is already opened";
        }
    }

private:
    // Returns a current number of registered in-progress requests.
    uint32_t get_count() const  {
        return _count.load();
    }

    // Returns whether the gate is closed.
    bool is_closed() const  {
        return _stopped.load();
    }

    std::atomic<int32_t> _count{0};
    std::atomic<bool> _stopped{false};
    common::MutexLock _close_mutex;
};
}
