/**
 * MOCHOW - A Vector Database System.
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON> (<EMAIL>)
 * Date: 2023/12/21
 * Desciption: Definitions for Read Consistency
 *
 */

#pragma once

#include <string>

namespace mochow::common {

class ReadConsistency final {
public:
    static constexpr const char* STRONG = "STRONG";
    static constexpr const char* EVENTUAL = "EVENTUAL";

    static inline bool is_valid_read_consistency(const std::string& read_consistency) {
    return read_consistency == STRONG
            || read_consistency == EVENTUAL;
    }

    static inline bool is_strong_consistency(const std::string& consistency_str) {
        return consistency_str == STRONG;
    }

private:
    ReadConsistency() = default;
    ~ReadConsistency() = default;
};

}
