
#include "core/src/common/flags.h"

namespace mochow {

DEFINE_bool(standalone_mode, false, "Whether run as standalone mode");

DEFINE_string(token, "default_token", "cluster token to verify rpc source");
DEFINE_string(master_address, "127.0.0.1:8288", "master address");
DEFINE_int32(raft_election_timeout_ms, 3000, "raft election time out(ms)");
DEFINE_int32(raft_snapshot_interval_s, 10 * 60, "raft snapshot interval(s), raft default value is one hour(3600s)");
DEFINE_uint64(idle_timeout_s, 3600 * 2, "tcp connection will close when time out");

DEFINE_uint64(os_page_size, 0, "page size of operating system. 0 for local os page size.");
DEFINE_uint64(default_bimap_bucket_count, 0, "default bucket count for bucketed bimap.");

DEFINE_int32(datanode_total_thread_count, 0, "total thread count of a datanode. 0 for auto configuration.");
DEFINE_double(datanode_load_from_disk_thread_percentage,       1, "percentage of threads for loading tablet from disk.");
DEFINE_double(datanode_tablet_index_builder_thread_percentage, 0.25, "percentage of threads for tablet index builder.");
DEFINE_double(zhuque_posix_file_system_thread_percentage,      0.25, "percentage of threads held by zhuque posix file system to datanode total thread count");
DEFINE_double(vindex_global_search_thread_percentage,          1, "percentage of threads held by vindex global search thread pool to datanode total thread count");
DEFINE_double(moss_dumper_and_loader_thread_percentage,        0.1, "percentage of threads held by moss dumper and loader to datanode total thread count");
DEFINE_double(moss_compactor_thread_percentage,                0.2, "percentage of threads held by moss compactor to datanode total thread count");
DEFINE_double(vindex_global_build_thread_percentage,           0.2, "percentage of threads held by vindex global build thread pool to datanode total thread count");
DEFINE_int32(hnsw_search_ef_upper_limit, 10000, "upper limit of search ef for hnsw index search parameter");

DEFINE_string(encryption_algorithm_name, "", "db encryption algorithm name, support aes_ctr");
DEFINE_string(encryption_key, "", "db encryption key");

DEFINE_uint64(build_thread_pool_max_capacity, 128, "Max capacity of build thread pool.");
DEFINE_uint64(search_thread_pool_max_capacity, 1024, "Max capacity of search thread pool.");

DEFINE_uint64(datanode_memory_quota_in_mb, 0, "Memory quota of datanode which setted by platform.");
DEFINE_double(default_post_filter_amplification_factor, 1.0, "default amplication factor for post filter. set it to 1.0 due to MPP requests have already amplified search.");
DEFINE_double(datanode_memory_reserved_in_gb, 0.0, "Memory reservation of datanode in gb");

DEFINE_uint64(thread_pool_auto_adjust_check_interval_s, 10, "Interval second of thread pool auto adjust worker size");

DEFINE_bool(enable_token_bucket, false, "enable token bucket");
DEFINE_uint64(token_bucket_max_tokens, 5000, "max tokens in token bucket");
DEFINE_uint64(token_bucket_fill_rate, 300, "tokens per second for filling token bucket");

}
