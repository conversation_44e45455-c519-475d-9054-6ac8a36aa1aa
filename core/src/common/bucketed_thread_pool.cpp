/**
 * MOCHOW - A Vector Database System.
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON><PERSON><PERSON><PERSON> (f<PERSON><PERSON><PERSON><PERSON>@baidu.com)
 * Date: 2023/10/20
 */

#include "sfl/random.h"
#include "core/src/common/bucketed_thread_pool.h"
#include "baidu/vdb/mochow/core/src/proto/error_code.pb.h"

namespace mochow::common {

BucketedThreadpool::BucketedThreadpool(const std::string& name) :
    _name(name),
    _state(State::UNINITIALIZED),
    _probability_thresholds({0, 0}),
    _pending_task_count(0) {
}

BucketedThreadpool::~BucketedThreadpool() {
    stop();
}

int BucketedThreadpool::start(const IStartOption* const option) {
    const StartOption* const start_option = dynamic_cast<const StartOption*>(option);
    if (start_option == nullptr) {
        return ERR_INVALID_ARGUMENT;
    }

    const uint64_t normal_thread_count = start_option->get_normal_thread_count();
    if (normal_thread_count == 0) {
        return ERR_INVALID_ARGUMENT;
    }

    const uint64_t realtime_thread_count = start_option->get_realtime_thread_count();
    
    _bucket_count = start_option->get_bucket_count();
    if (_bucket_count == 0) {
        return ERR_INVALID_ARGUMENT;
    }

    double high_priority_weight = 0;
    double medium_priority_weight = 0;
    start_option->get_priority_weight(high_priority_weight, medium_priority_weight);

    if (high_priority_weight <= 0 || medium_priority_weight <= 0) {
        return ERR_INVALID_ARGUMENT;
    }

    if (high_priority_weight >= 1 || medium_priority_weight >= 1) {
        return ERR_INVALID_ARGUMENT;
    }

    if (high_priority_weight + medium_priority_weight >= 1) {
        return ERR_INVALID_ARGUMENT;
    }

    {
        const common::ScopedButex guard(_threadpool_lock);

        if (_state == State::RUNNING) {
            return ERR_UNEXPECTED;
        }

        if (_state == State::STOPPED) {
            return ERR_UNEXPECTED;
        }

        assert(_state == State::UNINITIALIZED);

        _probability_thresholds.at(0) = high_priority_weight;
        _probability_thresholds.at(1) = high_priority_weight + medium_priority_weight;

        // After we create the following worker threads, they will start execution
        // immediately. But they have to acquire _threadpool_lock before they can
        // do anything. Because we still hold the mutex, these worker threads will
        // be blocked until we finish the initialization work.
        for (uint64_t i = 0; i < normal_thread_count; ++i) {
            LOG(NOTICE) << "Threadpool:" << _name << " start normal worker:" << i;
            _normal_workers.emplace_back(&BucketedThreadpool::execute_task, this, false);
            const std::thread::id id = _normal_workers.back().get_id();
            assert(_start_times.find(id) == _start_times.end());
            _start_times[id] = 0;
        }

        for (uint64_t i = 0; i < realtime_thread_count; ++i) {
            LOG(NOTICE) << "Threadpool:" << _name << " start realtime worker:" << i;
            _realtime_workers.emplace_back(&BucketedThreadpool::execute_task, this, true);
            const std::thread::id id = _realtime_workers.back().get_id();
            assert(_start_times.find(id) == _start_times.end());
            _start_times[id] = 0;
        }

        for (uint64_t i = 0; i < _bucket_count; ++i) {
            _task_queues.emplace_back();
            _bucket_pending_task.push_back(0);
        }

        _state = State::RUNNING;
    }

    return OK;
}

void BucketedThreadpool::stop() {
    const common::ScopedButex guard1(_threadpool_stop_lock);

    {
        const common::ScopedButex guard2(_threadpool_lock);

        if (_state == State::UNINITIALIZED) {
            _state = State::STOPPED;
            return;
        }

        if (_state == State::STOPPED) {
            return;
        }

        assert(_state == State::RUNNING);

        // Threadpool thread cannot stop the threadpool itself.
        const std::thread::id id = std::this_thread::get_id();
        assert(_start_times.find(id) == _start_times.end());

        _state = State::STOPPED;
    }

    _realtime_task_event.signal();

    _all_task_event.signal();

    for (auto& element : _normal_workers) {
        element.join();
    }

    for (auto& element : _realtime_workers) {
        element.join();
    }

    assert(_pending_task_count == 0);

    // All the worker threads have been stopped. No one will update _task_queues and
    // _start_times. So we do not need to acquire _threadpool_lock before we access
    // _task_queues and _start_times.
    for (const auto& bucket_queues : _task_queues) {
        for (const auto& element : bucket_queues) {
            assert(element.empty());
        }
    }

    // Do not clear _start_times because the get_max_task_execution_time_us function
    // can still be called concurrently, which reads the map.
    for (const auto& element : _start_times) {
        assert(element.second == 0);
    }
}

int BucketedThreadpool::submit(Task task, 
        const ITaskOption* const option) {
    
    if (task == nullptr) {
        return ERR_INVALID_ARGUMENT;
    }

    TaskOption task_option;
    const TaskOption* actual_task_option = &task_option;

    if (option != nullptr) {
        actual_task_option = dynamic_cast<const TaskOption*>(option);
        if (actual_task_option == nullptr) {
            return ERR_INVALID_ARGUMENT;
        }
    }

    const Priority priority = actual_task_option->get_priority();
    uint64_t bucket_id = actual_task_option->get_bucket_id();

    auto internal_task = std::make_shared<InternalTask>(std::move(task), 
            std::move(actual_task_option->get_event()));

    {
        const common::ScopedButex guard(_threadpool_lock);

        if (_state == State::UNINITIALIZED) {
            return ERR_UNINITIALIZED;
        }

        if (_state == State::STOPPED) {
            return ERR_UNEXPECTED;
        }

        assert(_state == State::RUNNING);

        _pending_task_count++;
        
        std::hash<uint64_t> hasher;
        uint64_t bucket_index = hasher(bucket_id) % _bucket_count;
        const uint64_t priority_index = baidu::sfl::enum_to_integer(priority);
        //LOG(TRACE) << "Threadpool:" << _name << " submit task with bucket_index:" << bucket_index
        //           << " priority_index:" << priority_index;
        _task_queues.at(bucket_index).at(priority_index).push(std::move(internal_task));
        _bucket_pending_task[bucket_index]++;
    }

    if (priority == Priority::REAL_TIME) {
        _realtime_task_event.signal();
    }

    _all_task_event.signal();
    
    //_queue_size_counter->set(_pending_task_count);

    return OK;
}

uint64_t BucketedThreadpool::get_max_task_execution_time_us() const {
    if (_state == State::UNINITIALIZED) {
        return 0;
    }

    // If threadpool is not in uninitialized state, _start_times map's
    // structure is immutable. We do not need a lock to access the map.

    uint64_t earliest_start_time_us = UINT64_MAX;

    for (const auto& element : _start_times) {
        if (element.second != 0 && element.second < earliest_start_time_us) {
            earliest_start_time_us = element.second;
        }
    }

    if (earliest_start_time_us != UINT64_MAX) {
        const uint64_t now_us = baidu::sfl::get_current_wall_time_us();
        return (now_us >= earliest_start_time_us) ? now_us - earliest_start_time_us : 0;
    }

    return 0;
}

// Caller of this function must acquire _threadpool_lock.
BucketedThreadpool::InternalTaskPtr
BucketedThreadpool::get_task_from_one_queue(uint64_t bucket_index, const Priority priority) {
    const uint64_t index = baidu::sfl::enum_to_integer(priority);
    //LOG(TRACE) << "Threadpool:" << _name << " get task from one queue,"
    //           << " bucket_index:" << bucket_index << " priority:" << index;
    auto& task_queue = _task_queues.at(bucket_index).at(index);

    if (task_queue.empty()) {
        return nullptr;
    }

    auto internal_task = std::move(task_queue.front());
    task_queue.pop();
    _pending_task_count--;

    return internal_task;
}

// Caller of this function must acquire _threadpool_lock.
BucketedThreadpool::InternalTaskPtr BucketedThreadpool::get_task_from_all_queues(
        uint64_t bucket_index) {
    auto internal_task = get_task_from_one_queue(bucket_index, Priority::REAL_TIME);

    if (internal_task != nullptr) {
        return internal_task;
    }

    Priority priority_that_has_been_checked = Priority::REAL_TIME;

    const double probability = baidu::sfl::get_tls_probability();

    if (probability < _probability_thresholds.at(0)) {
        internal_task = get_task_from_one_queue(bucket_index, Priority::HIGH);
        priority_that_has_been_checked = Priority::HIGH;
    } else if (probability < _probability_thresholds.at(1)) {
        internal_task = get_task_from_one_queue(bucket_index, Priority::MEDIUM);
        priority_that_has_been_checked = Priority::MEDIUM;
    } else {
        internal_task = get_task_from_one_queue(bucket_index, Priority::LOW);
        priority_that_has_been_checked = Priority::LOW;
    }

    if (internal_task != nullptr) {
        return internal_task;
    }

    static constexpr std::array<Priority, 4> priorities = {
        Priority::HIGH,
        Priority::MEDIUM,
        Priority::LOW,
        Priority::IDLE
    };

    for (const auto element : priorities) {
        if (element != priority_that_has_been_checked) {
            internal_task = get_task_from_one_queue(bucket_index, element);
            if (internal_task != nullptr) {
                return internal_task;
            }
        }
    }

    return nullptr;
}

BucketedThreadpool::InternalTaskPtr
BucketedThreadpool::get_task(const bool realtime_task) {
    baidu::sfl::Event* event = realtime_task ? &_realtime_task_event : &_all_task_event;

    while (true) {
        {
            const common::ScopedButex guard(_threadpool_lock);

            assert(_state != State::UNINITIALIZED);
             
            int64_t bucket_index = -1;
            for (uint64_t index = 0; index < _bucket_pending_task.size(); index++) {
                if (_bucket_pending_task[index]) {
                    bucket_index = index;
                    break;
                }
            }
            if (bucket_index >= 0) {
                //LOG(TRACE) << "Threadpool:" << _name << " try to find one task with bucket_index:" 
                //           << bucket_index;
                auto internal_task = realtime_task ?
                                     get_task_from_one_queue(bucket_index, Priority::REAL_TIME) :
                                     get_task_from_all_queues(bucket_index);

                if (internal_task != nullptr) {
                    _bucket_pending_task[bucket_index]--;
                    return internal_task;
                }
            }

            if (_state == State::STOPPED) {
                return nullptr;
            } else {
                // This event reset and the previous check (task queues are empty)
                // must be in one critical section. Otherwise, a concurrent submit
                // could happen in between them and this event reset can overwrite
                // the event signal during the submit.
                event->reset();
            }
        }
        //LOG(TRACE) << "thread:" << _name << " wait task";
        event->wait();
        //LOG(TRACE) << "thread:" << _name << " wake up";
    }
}

void BucketedThreadpool::execute_task(const bool realtime_task) {
    const std::thread::id id = std::this_thread::get_id();

    while (true) {
        auto internal_task = get_task(realtime_task);

        if (internal_task != nullptr) {
            //_queue_size_counter->set(_pending_task_count);

            const uint64_t start_time_us = baidu::sfl::get_current_wall_time_us();
            //const uint64_t creation_time_us = internal_task->get_creation_time();
            //if (start_time_us >= creation_time_us) {
            //    _wait_in_queue_time_counter->set(start_time_us - creation_time_us);
            //}
            //LOG(TRACE) << "Threadpool:" << _name << " execute task";
            _start_times.at(id) = start_time_us;
            internal_task->execute(_state == State::STOPPED);
            _start_times.at(id) = 0;

            if (internal_task->get_event()) {
                internal_task->get_event()->signal();
            }
            
            //const uint64_t end_time_us = get_current_wall_time_us();
            //if (end_time_us >= start_time_us) {
            //    _task_execution_time_counter->set(end_time_us - start_time_us);
            //}

            //_task_execution_rate_counter->add(1);
        } else {
            assert(_state == State::STOPPED);
            break;
        }
    }
}

} // mochow::engine
