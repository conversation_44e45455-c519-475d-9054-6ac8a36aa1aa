/**
 * MOCHOW - A Vector Database System.
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON> (<EMAIL>)
 * Date: 2023/09/19
 * Desciption: Implementations for host address functions
 *
 */

#include <set>
#include <arpa/inet.h>
#include <netdb.h>
#include <net/if.h>
#include <netinet/in.h>
#include <sys/socket.h>
#include <sys/ioctl.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <ifaddrs.h>
#include <naming.pb.h>
#include <webfoot_naming.h>
#include <base/logging.h>
#include <base/string_splitter.h>
#include "core/src/common/host_address.h"

namespace mochow::common {

// Get host IP list from DNS name
int get_host_ips_by_dns(const std::string& dns_name, std::vector<uint32_t>* ips) {
    std::string hostname = dns_name;
    struct hostent host;
    struct hostent *phost = NULL;
    char buf[4096];
    int ret = 0;
    struct in_addr addr;
    ips->clear();
    if (inet_aton(hostname.c_str(), &addr)) {
        ips->push_back(addr.s_addr);
    } else if (gethostbyname_r(hostname.c_str(), &host, buf, sizeof(buf), &phost, &ret) == 0
                && phost != NULL) {
        for (int i = 0; host.h_addr_list[i]; i++) {
            addr = *((struct in_addr *)host.h_addr_list[i]);
            ips->push_back(addr.s_addr);
        }
    }

    return ips->size() > 0 ? 0 : -1;
}

// Get host IP list form BNS name
int get_host_ips_by_bns(const std::string& service_name, std::vector<base::EndPoint>* addrs) {
    addrs->clear();
    BnsInput input;
    input.set_service_name(service_name);
    BnsOutput output;
    const int rc = webfoot::get_instance_by_service(input, &output);
    if (rc != webfoot::WEBFOOT_RET_SUCCESS) {
        if (rc != webfoot::WEBFOOT_SERVICE_BEYOND_THRSHOLD) {
            return -1;
        }
        // if rc == webfoot::WEBFOOT_SERVICE_BEYOND_THRSHOL,
        // output is still valid
        LOG(NOTICE) << "bns instance beyond theshold, instance_num:" << output.instance_size();
    }
    const int instance_number = output.instance_size();
    for (int i = 0; i < instance_number; i++) {
        const BnsInstance& instance = output.instance(i);
        base::EndPoint addr;
        addr.ip = base::int2ip(htonl(instance.host_ip_uint()));
        addr.port = instance.port();
        addrs->push_back(addr);
    }
    return 0;
}

// Get host IP list form List name
int get_host_ips_by_list(const std::string& service_name, std::vector<base::EndPoint>* addrs) {
    addrs->clear();
    std::string line;
    for (base::StringSplitter sp(service_name.c_str(), ','); sp != NULL; ++sp) {
        line.assign(sp.field(), sp.length());
        base::StringPiece addr;
        size_t i = 0;
        for (; i < line.size() && isspace(line[i]); ++i) {}
        if (i == line.size()) {  // all is space
            continue;
        }
        const char* const addr_start = line.data() + i;
        for (; i < line.size() && !isspace(line[i]); ++i) {}
        addr.set(addr_start, line.data() + i - addr_start);

        for (; i < line.size() && isspace(line[i]); ++i) {}
        if (i < line.size()) { // not space exlude addr
            continue;
        }
        const_cast<char*>(addr.data())[addr.size()] = '\0'; // safe
        base::EndPoint point;
        if (str2endpoint(addr.data(), &point) != 0 &&
            hostname2endpoint(addr.data(), &point) != 0) {
            LOG(WARNING) << "Skip invalid address " << addr;
            continue;
        }
        addrs->push_back(point);
    }
    return addrs->empty() ? -1 : 0;
}

// Parse address like '[[http://][https://][bns://][list://]]$DNS_NAME_OR_BNS_NAME:$PORT', eg.:
//   BNS  - address should starts with 'bns://'
//   LIST - address should starts with 'list://'
//   DNS  - otherwise, also support address starts with 'http://' or 'https://'
int parse_address(const std::string& _address, std::string* hostname,
                  NameServiceProtocol* ns_protocol) {
    hostname->clear();
    if (ns_protocol) {
        *ns_protocol = NameServiceProtocol::NONE;
    }
    std::string address = _address;
    if (strncmp(_address.c_str(), "http://", 7) == 0) {
        address = _address.substr(7);
        if (ns_protocol) {
            *ns_protocol = NameServiceProtocol::DNS;
        }
    } else if (strncmp(_address.c_str(), "https://", 8) == 0) {
        address = _address.substr(8);
        if (ns_protocol) {
            *ns_protocol = NameServiceProtocol::DNS;
        }
    } else if (strncmp(_address.c_str(), "bns://", 6) == 0) {
        address = _address.substr(6);
        if (ns_protocol) {
            *ns_protocol = NameServiceProtocol::BNS;
        }
    } else if (strncmp(_address.c_str(), "list://", 7) == 0) {
        address = _address.substr(7);
        *hostname = address;
        if (ns_protocol) {
            *ns_protocol = NameServiceProtocol::LIST;
        }
        return hostname->empty() ? -1 : 0;
    } else {
        if (ns_protocol) {
            LOG(WARNING) << "Unknown name service protocol, see address as"
                         << " NameServiceProtocol:" << NameServiceProtocol::DNS
                         << " address:" << _address;
            *ns_protocol = NameServiceProtocol::DNS;
        }
    }

    size_t pos = address.find_first_of(':');
    if (pos == std::string::npos) {
        *hostname = address;
    } else {
        *hostname = address.substr(0, pos);
    }

    return hostname->empty() ? -1 : 0;
}

int parse_address(const std::string& _address, std::string* hostname, uint16_t *port,
                  NameServiceProtocol* ns_protocol) {
    if (ns_protocol) {
        *ns_protocol = NameServiceProtocol::NONE;
    }
    std::string address = _address;
    if (strncmp(_address.c_str(), "http://", 7) == 0) {
        address = _address.substr(7);
        if (ns_protocol) {
            *ns_protocol = NameServiceProtocol::DNS;
        }
    } else if (strncmp(_address.c_str(), "https://", 8) == 0) {
        address = _address.substr(8);
        if (ns_protocol) {
            *ns_protocol = NameServiceProtocol::DNS;
        }
    } else if (strncmp(_address.c_str(), "bns://", 6) == 0) {
        address = _address.substr(6);
        if (ns_protocol) {
            *ns_protocol = NameServiceProtocol::BNS;
        }
    } else if (strncmp(_address.c_str(), "list://", 7) == 0) {
        address = _address.substr(7);
        *hostname = address;
        if (ns_protocol) {
            *ns_protocol = NameServiceProtocol::LIST;
        }
        return 0;
    } else {
        if (ns_protocol) {
            LOG(WARNING) << "Unknown name service protocol, see address as"
                         << " NameServiceProtocol:" << NameServiceProtocol::DNS
                         << " address:" << _address;
            *ns_protocol = NameServiceProtocol::DNS;
        }
    }

    size_t pos = address.find_first_of(':');
    if (pos == std::string::npos) {
        if (ns_protocol && (*ns_protocol) == NameServiceProtocol::BNS) {
            *hostname = address;
            *port = 0;
            return 0;
        } else {
            LOG(WARNING) << "Fail to parse address from address " << _address;
            return -1;
        }
    }
    if (address.find_last_of(':') != pos) {
        LOG(WARNING) << "Fail to parse address from address " << _address;
        return -1;
    }

    *hostname = address.substr(0, pos);
    std::string port_str = address.substr(pos + 1);

    if (hostname->empty() || port_str.empty()) {
        LOG(WARNING) << "Fail to parse address from address " << _address;
        return -1;
    }

    for (size_t i = 0; i < port_str.size(); ++i) {
        char c = port_str[i];
        if (c < '0' || c > '9') {
            LOG(WARNING) << "Fail to parse address due to invalid port " << port_str;
            return -1;
        }
    }

    long long int large_port = atoll(port_str.c_str());
    if (large_port < 0 || large_port > 65535) {
        LOG(WARNING) << "Fail to parse address due to invalid port " << port_str;
        return -1;
    }

    *port = static_cast<uint16_t>(large_port);
    return 0;
}

int parse_address(const std::string& address, std::vector<base::EndPoint> *addrs,
                  NameServiceProtocol* ns_protocol) {
    addrs->clear();
    if (ns_protocol) {
        *ns_protocol = NameServiceProtocol::NONE;
    }

    NameServiceProtocol inside_ns_protocol;
    std::string hostname;
    uint16_t port = 0;

    if (0 != parse_address(address, &hostname, &port, &inside_ns_protocol)) {
        return -1;
    }
    if (ns_protocol) {
        *ns_protocol = inside_ns_protocol;
    }

    if (inside_ns_protocol == NameServiceProtocol::BNS) {
        // See as BNS
        std::string pure_name = hostname;
        int ret = get_host_ips_by_bns(pure_name, addrs);
        if (ret == 0 && addrs->size() > 0 && (*addrs)[0].port == 0) {
            for (size_t i = 0; i < addrs->size(); ++i) {
                (*addrs)[i].port = port;
            }
            LOG(WARNING) << "Parse address: change port to configured one due to bns instance has no port";
        }
        if (ret != 0) {
            LOG(WARNING) << "Fail to parse address from " << address;
            return ret;
        }
    } else if (inside_ns_protocol == NameServiceProtocol::LIST) {
        // See as LIST
        int ret = get_host_ips_by_list(hostname, addrs);
        if (ret != 0) {
            LOG(WARNING) << "Fail to parse address from " << address;
            return ret;
        }
    } else if (inside_ns_protocol == NameServiceProtocol::DNS) {
        // See as DNS
        std::vector<uint32_t> ips;
        if (0 != get_host_ips_by_dns(hostname, &ips)) {
            LOG(WARNING) << "Fail to parse address from " << address;
            return -1;
        }
        for (size_t i = 0; i < ips.size(); ++i) {
            base::EndPoint addr;
            addr.ip = base::int2ip(ips[i]);
            addr.port = port;
            addrs->push_back(addr);
        }
    } else {
        LOG(WARNING) << "Fail to parse address from " << address;
        return -1;
    }
    return 0;
}

// Get the IP from specificed interface
uint32_t get_host_ip_by_interface(const char* interface) {
    int sockfd = 0;
    struct ::ifreq req;
    uint32_t ip = 0;
    if ((sockfd = socket(PF_INET, SOCK_DGRAM, 0)) < 0) {
        return 0;
    }
    memset(&req, 0, sizeof(struct ::ifreq));
    snprintf(req.ifr_name, sizeof(req.ifr_name), "%s", interface);
    if (!ioctl(sockfd, SIOCGIFADDR, (char*)&req)) {
        struct in_addr ip_addr;
        ip_addr.s_addr = ((sockaddr_in *) &req.ifr_addr)->sin_addr.s_addr;
        ip = ip_addr.s_addr;
    }
    close(sockfd);
    return ip;
}

// Get first valid IP from interface list splitted by ','
uint32_t get_host_ip_by_interfaces(const char* _interfaces) {
    std::string interfaces(_interfaces);
    std::vector<std::string> interface_list;
    std::set<std::string> interface_set;
    ssize_t last_delim_pos = -1;
    for (size_t i = 0; i < interfaces.size(); ++i) {
        size_t delim_pos = interfaces.find(",", i);
        if (delim_pos < interfaces.size()) {
            std::string interface = interfaces.substr(i, delim_pos - i);
            interface_list.push_back(interface);
            interface_set.insert(interface);
            i = delim_pos;
        }
        last_delim_pos = delim_pos;
    }
    if (last_delim_pos < (ssize_t)(interfaces.size()) - 1) {
        std::string interface = interfaces.substr(last_delim_pos + 1);
        interface_list.push_back(interface);
        interface_set.insert(interface);
    }
    struct ifaddrs* if_addr_first = NULL;
    getifaddrs(&if_addr_first);

    for (ifaddrs* if_addr = if_addr_first; if_addr != NULL; if_addr = if_addr->ifa_next) {
        if (if_addr->ifa_addr == nullptr) {
            continue;
        }
        std::string interface = if_addr->ifa_name;
        if (if_addr->ifa_addr->sa_family == AF_INET
#if !defined(_UNIT_TEST) && !defined(_UNITTEST)
                && interface != "lo"
#endif
                && interface_set.count(interface) == 0) {
            interface_list.push_back(interface);
            interface_set.insert(interface);
        }
    }
    uint32_t ip = 0;
    for (size_t i = 0; i < interface_list.size(); ++i) {
        ip = get_host_ip_by_interface(interface_list[i].c_str());
        if (0 != ip) {
            break;
        }
    }
    freeifaddrs(if_addr_first);
    return ip;
}

}
