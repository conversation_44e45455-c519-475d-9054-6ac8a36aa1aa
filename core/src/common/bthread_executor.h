/***************************************************************************
 *
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

#pragma once

#include <bthread.h>
#include <functional>
#include <iostream>
#include <string.h>
#include <bthread/countdown_event.h>
#include <memory>

#include "core/src/common/status.h"

namespace mochow::common {

class BthreadExecutor {
public:
    using Func = std::function<void()>;

    inline static BthreadExecutor &instance() noexcept {
        static BthreadExecutor bthread_executor;
        return bthread_executor;
    }

    BthreadExecutor() = default;

    virtual ~BthreadExecutor() = default;

    int add(Func func);

    int add_urgent(Func func);

private:
    inline static void* bthread_run(void* args) noexcept {
        auto function = reinterpret_cast<Func*>(args);
        (*function)();
        delete function;
        return nullptr;
    }
};

class BthreadCountdownExecutor {
public:
    using Func = std::function<void()>;

    BthreadCountdownExecutor(size_t initial_count) {
        _cond = new bthread::CountdownEvent(initial_count);
    }
    ~BthreadCountdownExecutor() {
        if (_cond != nullptr) {
            delete _cond;
        }
    }

    int add(Func func);

    int add_urgent(Func func);

    int wait() {
        return _cond->wait();
    }

    int timed_wait(const timespec& timeout_ms) {
        return _cond->timed_wait(timeout_ms);
    }

    Status timed_wait_and_exit(const timespec& timeout_ms) {
        int rc = _cond->timed_wait(timeout_ms);
        if (rc != 0) {
            if (rc == ETIMEDOUT) {
                for (bthread_t tid : _tids) {
                    bthread_stop(tid);
                }
                for (bthread_t tid : _tids) {
                    bthread_join(tid, nullptr);
                }
                return Status(ERR_TIMEOUT, "bthread countdown executor timeout");
            }
            return Status(ERR_DB_INTERNAL_ERROR, "bthread countdown executor error");
        }

        return Status();
    }

private:
    struct CountdownArg {
        Func* func;
        bthread::CountdownEvent* cond; //lifecycle managed externally

        ~CountdownArg() {
            if (func) {
                delete func;
                func = nullptr;
            }
        }
    };

    bthread::CountdownEvent* _cond;
    std::vector<bthread_t> _tids;

private:
    inline static void* bthread_run(void* args) noexcept {
        auto countdown_arg = (CountdownArg*) args;
        (*(countdown_arg->func))();
        countdown_arg->cond->signal();
        delete countdown_arg;
        return nullptr;
    }
};

}
