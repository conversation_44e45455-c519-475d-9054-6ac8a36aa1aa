/**
 * MOCHOW - A Vector Database System.
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON> (<EMAIL>)
 * Date: 2023/09/19
 * Desciption: Declarations for Protobuf util functions
 *
 */

#pragma once

#include <base/iobuf.h>
#include <base/status.h>
#include <google/protobuf/stubs/common.h>
#include <google/protobuf/message.h>

namespace google::protobuf {
class Message;
}

namespace mochow::common {

// Protobuf -> Json
bool pb2json(const google::protobuf::Message& message, std::string *json, std::string *error);

// Protobuf -> Json
std::string pb2json(const google::protobuf::Message& message);

// Serialize |message| to |out|.
// Returns true on success , false otherwise and detailed infomation would be
// assign to |*st| if st is non-NULl
bool serialize_message_to_iobuf(const ::google::protobuf::Message* message,
                                base::IOBuf* out, base::Status* st);

// Parse |message| from |out|.
// Returns true on success , false otherwise and detailed infomation would be
// assign to |*st| if st is non-NULl
bool parse_message_from_iobuf(::google::protobuf::Message* message,
                              const base::IOBuf& in, base::Status* st);


inline int request2data(uint32_t log_type,
        const ::google::protobuf::Message* request,
        base::IOBuf* data) {
    int ret = 0;
    data->append(&log_type, sizeof(log_type));
    base::IOBufAsZeroCopyOutputStream wrapper(data);
    if (!request->SerializeToZeroCopyStream(&wrapper)) {
        ret = -1;
    }
    return ret;
}

}
