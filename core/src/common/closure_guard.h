#pragma once

#include <functional>
#include "base/macros.h"

namespace mochow::common {

class ClosureGuard {
public:
    ClosureGuard(std::function<void()> done) : _done(std::move(done)) {}
    ~ClosureGuard() {
        if (_done) {
            _done();
        }
    }

    ClosureGuard(ClosureGuard&& other) : _done(other.release()) {}
    void operator=(ClosureGuard&& other) = delete;

    DISALLOW_COPY_AND_ASSIGN(ClosureGuard);

    // Return and set internal closure to NULL.
    std::function<void()> release() {
        std::function<void()> const prev_done = _done;
        _done = NULL;
        return prev_done;
    }
private:
    std::function<void()> _done;
};
}
