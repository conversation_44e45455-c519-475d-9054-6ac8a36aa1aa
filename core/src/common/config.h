/**
 * MOCHOW - A Vector Database System.
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON> (<EMAIL>)
 * Date: 2023/10/01
 * Desciption: Declarations for basic configs and comlog
 *
 */

#pragma once

#include <gflags/gflags.h>
#include <base/endpoint.h>

namespace mochow::common {

DECLARE_int32(port);
DECLARE_string(interfaces);

extern base::EndPoint s_local_addr;
extern base::EndPoint s_master_addr;
int init_local_addr();
base::EndPoint get_local_addr();
base::EndPoint get_master_addr();

}
