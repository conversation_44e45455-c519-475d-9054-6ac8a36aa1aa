/**
 * MOCHOW - A Vector Database System.
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved.
 *
 * Date: 2024/11/21
 * Desciption: Definitions for Vindex Build Type
 *
 */

#pragma once

#include <string>

namespace mochow::common {

class VIndexBuildType final {
public:
    static constexpr const char* STABLE = "STABLE";
    static constexpr const char* DELTA = "DELTA";

    static inline bool is_valid(const std::string& type) {
        return type == STABLE || type == DELTA;
    }

private:
    VIndexBuildType() = default;
    ~VIndexBuildType() = default;
};

}
