/***************************************************************************
 *
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

#pragma once

#include <string>

namespace mochow::common {

class TableState final {
public:
    static constexpr const char* NORMAL = "NORMAL";
    static constexpr const char* DELETING = "DELETING";
    static constexpr const char* CREATING = "CREATING";

    static inline bool is_valid(const std::string& state) {
        return state == CREATING || state == NORMAL || state == DELETING;
    }

private:
    TableState() = default;
    ~TableState() = default;
};

}
