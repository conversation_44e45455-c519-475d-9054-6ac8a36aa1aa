/**
 * MOCHOW - A Vector Database System.
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON> (<EMAIL>)
 * Date: 2023/09/19
 * Desciption: Implementations for Protobuf util functions
 *
 */

#include <pb_to_json.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/message.h>
#include "core/src/common/pbutils.h"

namespace mochow::common {

// Protobuf -> Json
bool pb2json(const google::protobuf::Message& message, std::string *json, std::string *error) {
    if (ProtoMessageToJson(message, json, error)) {
        return true;
    }
    return false;
}

// Protobuf -> Json
std::string pb2json(const google::protobuf::Message& message) {
    std::string json;
    std::string error;
    pb2json(message, &json, &error);
    return json;
}

// Serialize |message| to |out|.
// Returns true on success , false otherwise and detailed infomation would be
// assign to |*st| if st is non-NULl
bool serialize_message_to_iobuf(const ::google::protobuf::Message* message,
                                base::IOBuf* out, base::Status* st) {
    if (!message->IsInitialized()) {
        if (st) {
            st->set_error(EINVAL, "Missing required fields in message=%s: %s",
                          message->GetDescriptor()->full_name().c_str(),
                          message->InitializationErrorString().c_str());
        }
        return false;
    }
    base::IOBufAsZeroCopyOutputStream wrapper(out);
    if (!message->SerializeToZeroCopyStream(&wrapper)) {
        if (st) {
            st->set_error(EINVAL, "Fail to serialize message=%s",
                          message->GetDescriptor()->full_name().c_str());
        }
        return false;
    }
    return true;
}

// Parse |message| from |out|.
// Returns true on success , false otherwise and detailed infomation would be
// assign to |*st| if st is non-NULl
bool parse_message_from_iobuf(::google::protobuf::Message* message,
                              const base::IOBuf& in, base::Status* st) {
    base::IOBufAsZeroCopyInputStream wrapper(in);
    if (!message->ParseFromZeroCopyStream(&wrapper)) {
        if (st) {
            st->set_error(EINVAL, "Fail to parse message=%s",
                          message->GetDescriptor()->full_name().c_str());
        }
        return false;
    }
    return true;
}

}
