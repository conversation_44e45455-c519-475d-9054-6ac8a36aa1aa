/**
 * MOCHOW - A Vector Database System.
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON> (<EMAIL>)
 * Date: 2023/10/17
 * Desciption: Definitions for Partition Type
 *
 */

#pragma once

#include <string>

namespace mochow::common {

class PartitionType final {
public:
    static constexpr const char* INVALID = "INVALID";
    static constexpr const char* HASH = "HASH";
    static constexpr const char* RANGE = "RANGE";

    static inline bool is_valid_partition_type(const std::string& ptype) {
        return ptype == HASH || ptype == RANGE;
    }

    static inline bool is_hash_partition_type(const std::string& ptype) {
        return ptype == HASH;
    }

private:
    PartitionType() = default;
    ~PartitionType() = default;
};

}
