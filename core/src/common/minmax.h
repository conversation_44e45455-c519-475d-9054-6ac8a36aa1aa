/**
 * MOCHOW - A Vector Database System.
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON> (<EMAIL>)
 * Date: 2023/11/25
 * Desciption: Definitions for Min or Max Values
 *
 */

#pragma once

#include "core/src/common/using.h"
#include "core/src/types/data_type.h"
#include "core/src/common/flags.h"

namespace mochow {

constexpr const size_t PRIMARY_KEYS_COUNT_MAX = 10;

constexpr const DIM FLOAT_VECTOR_DIMENSION_MIN = 2;
constexpr const DIM FLOAT_VECTOR_DIMENSION_MAX = 4096;

constexpr const DIM BINARY_VECTOR_DIMENSION_MAX = FLOAT_VECTOR_DIMENSION_MAX;

constexpr inline bool is_valid_dimension(DataType data_type, DIM dimension) {
    if (data_type == DataType::FLOAT_VECTOR) {
        return dimension >= FLOAT_VECTOR_DIMENSION_MIN && dimension <= FLOAT_VECTOR_DIMENSION_MAX;
    } else if (data_type == DataType::BINARY_VECTOR) {
        // Binary vector's dimension needs to be a multiple of 8.
        return dimension > 0 && dimension <= BINARY_VECTOR_DIMENSION_MAX && (dimension % 8) == 0;
    } else {
        return false;
    }
}

constexpr const size_t REPLICATION_MIN = 2;
constexpr const size_t REPLICATION_MAX = 10;

constexpr inline bool is_valid_replication(const size_t replication, bool is_standalone_mode) {
    if (is_standalone_mode) {
        return replication == 1;
    }
    return replication >= REPLICATION_MIN && replication <= REPLICATION_MAX;
}

constexpr const size_t HASH_PARTITION_NUM_MIN = 1;
constexpr const size_t HASH_PARTITION_NUM_MAX = 100000;

constexpr inline bool is_valid_hash_partition_num(const size_t partition_num) {
    return partition_num >= HASH_PARTITION_NUM_MIN
            && partition_num <= HASH_PARTITION_NUM_MAX;
}

constexpr const size_t HNSW_M_MIN = 4;
constexpr const size_t HNSW_M_MAX = 128;

constexpr inline bool is_valid_hnsw_m(const size_t m) {
    return m >= HNSW_M_MIN && m <= HNSW_M_MAX;
}

constexpr const size_t HNSW_EF_CONSTRUCTION_MIN = 8;
constexpr const size_t HNSW_EF_CONSTRUCTION_MAX = 1024;

constexpr inline bool is_valid_hnsw_ef_construction(const size_t ef_construction) {
    return ef_construction >= HNSW_EF_CONSTRUCTION_MIN
            && ef_construction <= HNSW_EF_CONSTRUCTION_MAX;
}

constexpr const int32_t HNSW_EF_MIN = 1;
constexpr inline bool is_valid_hnsw_ef(const int32_t ef) {
    return ef >= HNSW_EF_MIN && ef <= FLAGS_hnsw_search_ef_upper_limit;
}

constexpr const size_t PUCK_CLUSTER_MIN = 1;
constexpr const size_t PUCK_CLUSTER_MAX = 5000;
constexpr const uint32_t MAX_SAMPLERATE_FOR_PUCKPQ = 10U;
constexpr const uint32_t MIN_SAMPLERATE_FOR_PUCKPQ = 1U;

constexpr inline bool is_valid_puck_cluster_count(
        const size_t coarse_cluster_count,
        const size_t fine_cluster_count) {
    return coarse_cluster_count >= PUCK_CLUSTER_MIN && coarse_cluster_count <= PUCK_CLUSTER_MAX &&
            fine_cluster_count >= PUCK_CLUSTER_MIN && fine_cluster_count <= PUCK_CLUSTER_MAX;
}

constexpr inline bool is_valid_hnswpq_nsq(const size_t NSQ) {
    return NSQ >= 1;
}

constexpr inline bool is_valid_hnswpq_sample_rate(const float sample_rate) {
    return (sample_rate >= 0.0f) && (sample_rate <= 1.0f);
}

constexpr inline bool is_valid_hnswsq_qt_bits(const uint32_t qt_bits) {
    return (qt_bits == 4U) || (qt_bits == 8U) || (qt_bits == 16U);
}
}
