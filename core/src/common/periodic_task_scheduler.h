#pragma once

#include <functional>
#include <memory>
#include <queue>
#include <thread>
#include <unordered_map>
#include "core/src/common/status.h"
#include "core/src/common/butex.h"
#include "core/src/common/using.h"

namespace mochow::common {

using PeriodicTaskFunc = std::function<void()>;

static constexpr uint64_t MICROS_IN_SECOND = 1000U * 1000U;

class PeriodicTaskScheduler {
private:
    struct TaskInfo {
        PeriodicTaskFunc func;
        std::string name;
        uint64_t next_run_time_us;
        uint64_t repeat_period_us;
        bool valid;

        TaskInfo(
                PeriodicTaskFunc _func,
                const std::string& _name,
                const uint64_t _next_run_time_us,
                const uint64_t _repeat_period_us)
            : func(std::move(_func))
            , name(_name)
            , next_run_time_us(_next_run_time_us)
            , repeat_period_us(_repeat_period_us)
            , valid(true) {}

        void cancel() {
            valid = false;
        }

        bool is_valid() const {
            return valid;
        }
    };

    using TaskInfoRef = std::shared_ptr<TaskInfo>;

private:
    struct RunTimeOrder {
        bool operator()(const TaskInfoRef f1, const TaskInfoRef f2) {
            return f1->next_run_time_us > f2->next_run_time_us;
        }
    };

    class task_priority_queue : public std::priority_queue<
                                        TaskInfoRef,
                                        std::vector<TaskInfoRef>,
                                        RunTimeOrder> {
    public:
        bool remove(const TaskInfoRef& info) {
            auto it = std::find_if(
                    this->c.begin(), this->c.end(), [info](const TaskInfoRef& i) {
                        if (i == nullptr || info == nullptr) {
                            return false;
                        }
                        return i->name == info->name;
                    });

            if (it == this->c.end()) {
                return false;
            }
            if (it == this->c.begin()) {
                // deque the top element
                this->pop();
            } else {
                // remove element and re-heap
                this->c.erase(it);
                std::make_heap(this->c.begin(), this->c.end(), this->comp);
            }
            return true;
        }
    };

public:
    PeriodicTaskScheduler() = default;
    virtual ~PeriodicTaskScheduler() {
        stop();
    }

    Status register_task(
            const std::string& task_name,
            PeriodicTaskFunc task_func,
            const uint64_t repeat_period_seconds);

    Status cancel_task(
            const std::string& task_name);

    bool start() {
        // return if already running
        bool expected_is_running = false;
        if (!_is_running.compare_exchange_strong(expected_is_running, true)) {
            return true;
        }

        _thread = std::thread{std::bind(&PeriodicTaskScheduler::run, this)};
        return true;
    }

    void stop() {
        // return if already stopped
        bool expected_is_running = true;
        if (!_is_running.compare_exchange_strong(expected_is_running, false)) {
            return;
        }

        if (_thread.joinable()) {
            _thread.join();
        }
    }

private:
    void run();

    std::atomic<bool> _is_running = false;
    std::thread _thread;
    common::MutexLock _mutex;
    std::unordered_map<std::string, TaskInfoRef> _task_map;
    task_priority_queue _heap;
};

using PeriodicTaskSchedulerRef = std::shared_ptr<PeriodicTaskScheduler>;

}
