#pragma once

namespace mochow::common {

// The Hamming distance type
using hamdis_t = uint32_t;

namespace {

// popcount
inline int popcount8(uint8_t x) {
    return __builtin_popcount(x);
}

inline int popcount64(uint64_t x) {
    return __builtin_popcountll(x);
}

inline hamdis_t hamming_distance(const uint64_t* __restrict bs1, const uint64_t* __restrict bs2, size_t nwords) {
    hamdis_t h = 0;
    for (size_t i = 0; i < nwords; i++) {
        h += popcount64(bs1[i] ^ bs2[i]);
    }
    return h;
}

}

// Hamming distance calculation. 'bs1' and 'bs2' are bit-packed arrays.
inline hamdis_t hamming_distance(const uint8_t* __restrict bs1, const uint8_t* __restrict bs2, size_t nbytes) {
    size_t nwords = (nbytes / sizeof(uint64_t));
    hamdis_t h = hamming_distance(reinterpret_cast<const uint64_t*>(bs1), reinterpret_cast<const uint64_t*>(bs2), nwords);

    size_t idx = nwords * sizeof(uint64_t);
    while (idx < nbytes) {
        h += popcount8(bs1[idx] ^ bs2[idx]);
        idx++;
    }
    return h;
}

}
