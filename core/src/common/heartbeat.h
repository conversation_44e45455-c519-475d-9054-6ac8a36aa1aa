/**
 * MOCHOW - A Vector Database System.
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved.
 *
 */

#pragma once

#include <atomic>
#include <base/endpoint.h>
#include <bthread.h>
#include <bthread_unstable.h>

namespace mochow::pb {
class BasicMetrics;
}

namespace mochow::common {

DECLARE_int32(heartbeat_interval_s);

class Heartbeat {
public:
    virtual ~Heartbeat(){}
    void start(base::EndPoint listen_addr, uint32_t heartbeat_interval_second);
    void stop();

    static void on_heartbeat_timer(void* args);
    static void* heartbeat_rpc_func(void* args);
    virtual void heartbeat_rpc() = 0;

    std::atomic<uint32_t> interval_second;

protected:
    std::atomic<bool> _is_stopped;
    base::EndPoint _listen_addr;
    base::EndPoint _remote_addr;
    bthread_timer_t _heartbeat_timer;
};

void add_basic_metrics_in_heartbeat(pb::BasicMetrics* basic_metrics);

}
