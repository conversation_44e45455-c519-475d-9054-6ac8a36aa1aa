#pragma once

#include <optional>
#include "base/bthread/bthread_types.h"
#include "core/src/common/using.h"
#include <base/logging.h>

namespace mochow {

namespace trace_internal {
struct InvalidType {};
}  // namespace trace_internal

// Provide a global `log_id` and `trace_id` for the macro `RPC_TRACE_LOG`.
//
// NOTE: you are not expected to use them directly, or the compiler will report error.
inline const trace_internal::InvalidType log_id;
inline const trace_internal::InvalidType trace_id;
//

namespace trace_internal {
struct TraceContext {
    TraceContext() = delete;
    TraceContext(uint64_t log_id, TRACEID trace_id) : log_id(log_id), trace_id(trace_id) {}
    ~TraceContext();

    uint64_t log_id;
    TRACEID trace_id;

    DISALLOW_COPY_AND_ASSIGN(TraceContext);
};

// Keep current task in a thread-local variable for easy retrieve of
// tracing information. Supports both bthread and pthread.
class CurrTask {
public:
    CurrTask() = delete;

    // Set `task` to be thread-local.
    static void reset(TraceContext* ctx);

    // Get the thread-local task. Returns NULL if user had not called `reset`.
    static TraceContext* get();
};

struct TracePrintingHelper {
    TracePrintingHelper() = delete;
    TracePrintingHelper(const uint64_t* _log_id, const TRACEID* _trace_id)
            : log_id(_log_id), trace_id(_trace_id) {
        if (_log_id == reinterpret_cast<const uint64_t*>(&mochow::log_id)) {
            log_id = nullptr;
        }
        if (_trace_id == reinterpret_cast<const TRACEID*>(&mochow::trace_id)) {
            trace_id = nullptr;
        }
    }

    std::pair<uint64_t, TRACEID> get_ids() const {
        std::pair<uint64_t, TRACEID> res = {0, 0};
        if (log_id != nullptr) {
            res.first = *log_id;
        }
        if (trace_id != nullptr) {
            res.second = *trace_id;
        }

        if ((log_id == nullptr) || (trace_id == nullptr)) {
            TraceContext* curr_task = CurrTask::get();
            if (curr_task == nullptr) {
                return res;
            }

            if (res.first == 0) {
                res.first = curr_task->log_id;
            }

            if (res.second == 0) {
                res.second = curr_task->trace_id;
            }
        }

        return res;
    }

    const uint64_t* log_id;
    const TRACEID* trace_id;
};

inline std::ostream& operator<<(std::ostream& os, const TracePrintingHelper& helper) {
    std::pair<uint64_t, TRACEID> ids = helper.get_ids();

    if (ids.first != 0) {
        os << "[log_id:" << ids.first << "] ";
    }
    if (ids.second != 0) {
        os << "[trace_id:" << ids.second << "] ";
    }

    return os;
}

}  // namespace trace_internal

// Set log_id and trace_id as thread-local variables, which are used by `MOCHOW_TRACE_LOG`.
#define SET_SCOPED_TASK_TRACE_ID(log_id, trace_id)                 \
    trace_internal::TraceContext __TASK_CTX_VAR(log_id, trace_id); \
    trace_internal::CurrTask::reset(&__TASK_CTX_VAR)

// You can always replace `LOG` with `MOCHOW_TRACE_LOG`.
//
// It will print log_id and trace_id, if they are defined.
//
// It first looks for any local variables named `log_id` and `trace_id`, if not found, it uses the log_id and
// trace_id set by `SET_SCOPED_TASK_TRACE_ID`. If both are not found or the value is 0, it behaves the same
// with `LOG`.
//
#define MOCHOW_TRACE_LOG(level)                                         \
    LOG(level) << trace_internal::TracePrintingHelper(                  \
                          reinterpret_cast<const uint64_t*>(&log_id),   \
                          reinterpret_cast<const TRACEID*>(&trace_id))

#define RPC_TRACE_LOG MOCHOW_TRACE_LOG

// Get the thread-local log_id and trace_id set by `SET_SCOPED_TASK_TRACE_ID`.
#define GET_CURRENT_TRACE_ID()                                        \
        trace_internal::TracePrintingHelper(                          \
                reinterpret_cast<const uint64_t*>(&mochow::log_id),   \
                reinterpret_cast<const TRACEID*>(&mochow::trace_id)) \
                .get_ids()

void init_bthread_attr_for_trace(bthread_attr_t* attr);

}  // namespace mochow
