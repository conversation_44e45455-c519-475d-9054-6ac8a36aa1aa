/***************************************************************************
 *
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

#pragma once

#include <string>

namespace mochow::common {

class TabletState final {
public:
    static constexpr const char* CREATING = "CREATING";
    static constexpr const char* NORMAL = "NORMAL";
    static constexpr const char* BALANCING = "BALANCING";

    static inline bool is_valid(const std::string& state) {
        return state == CREATING || state == NORMAL || state == BALANCING;
    }

private:
    TabletState() = default;
    ~TabletState() = default;
};

}
