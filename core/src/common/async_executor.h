#pragma once

#include <optional>
#include <memory>
#include <bthread.h>
#include "sfl/attributes.h"
#include "baidu/vdb/mochow/core/src/common/logging.h"

namespace mochow::common {

DECLARE_int32(async_executor_max_task_num);

// AsyncExecutor can be used as a threadpool, where the tasks are run by bthreads.
// `add_task` will fail if the number of added task exceeds `async_executor_max_task_num`.
class AsyncExecutor {
public:
    // opaque to user
    class ID {
    private:
        ID() = delete;
        explicit ID(bthread_t tid) : _tid(tid) {}
        bthread_t _tid;
        friend class AsyncExecutor;
    };

public:
    // Returns nullopt if add task fail due to: 1) FLAGS_async_executor_max_task_num is reached, or
    // 2) create bthread failed.
    template <typename Func, typename = std::enable_if_t<std::is_invocable_r_v<void, Func>>>
    NODISCARD static std::optional<ID> add_task(Func&& func) {
        std::optional<ID> res;

        const auto [log_id, trace_id] = GET_CURRENT_TRACE_ID();

        bthread_t tid = instance()->add_task_impl(std::forward<Func>(func), log_id, trace_id);
        if (tid != INVALID_BTHREAD) {
            res = ID(tid);
        }
        return res;
    }

    static int64_t task_num() {
        return instance()->_task_num.load(std::memory_order_relaxed);
    }

    static int stop(ID id) {
        return bthread_stop(id._tid);
    }

    static int join(ID id) {
        return bthread_join(id._tid, nullptr);
    }

private:
    template <typename Func>
    struct BthreadContext {
        template <typename F>  // F and Func are same type
        BthreadContext(AsyncExecutor* _executor, F&& _func, uint64_t _log_id, uint64_t _trace_id)
                : executor(_executor), func(std::forward<F>(_func)), log_id(_log_id), trace_id(_trace_id) {}

        AsyncExecutor* executor;
        Func func;
        uint64_t log_id;
        uint64_t trace_id;
    };

    AsyncExecutor() : _task_num(0) {
        LOG(NOTICE) << "AsyncExecutor(" << (void*)this << ") startup";
    }

    static AsyncExecutor* instance() {
        static AsyncExecutor executor;
        return &executor;
    }

    template <typename Func>
    bthread_t add_task_impl(Func&& _func, uint64_t log_id, uint64_t trace_id) {
        int64_t task_num = _task_num.fetch_add(1, std::memory_order_relaxed);
        if (task_num >= FLAGS_async_executor_max_task_num) {
            _task_num.fetch_sub(1, std::memory_order_relaxed);
            RPC_LOG(WARNING) << "Fail to add task to async executor due to task_num exceeds limit,"
                             << " task_num:" << task_num
                             << " limit:" << FLAGS_async_executor_max_task_num;
            return INVALID_BTHREAD;
        }

        // Allocate a copy of the ‘_func’ object on heap, which will be deleted after the task finish.
        using ContextType = BthreadContext<std::decay_t<Func>>;
        auto* bthread_context = new ContextType(this, std::forward<Func>(_func), log_id, trace_id);

        bthread_attr_t attr = BTHREAD_ATTR_NORMAL;
        init_bthread_attr_for_trace(&attr);

        bthread_t tid{INVALID_BTHREAD};
        if (bthread_start_background(&tid, &attr, bthread_wrapper_func<ContextType>, bthread_context) != 0) {
            RPC_LOG(WARNING) << "Fail to add task to async executor due to create bthread failed";
            _task_num.fetch_sub(1, std::memory_order_relaxed);
            delete bthread_context;
        }

        return tid;
    }

    template <typename ContextType>
    static void* bthread_wrapper_func(void* _context) {
        auto* context = reinterpret_cast<ContextType*>(_context);

        // Attach tracing information to the bthread.
        SET_SCOPED_TASK_TRACE_ID(context->log_id, context->trace_id);

        // Call the user function
        context->func();

        // Decrease ‘_task_num’ after ‘func’ finish.
        context->executor->_task_num.fetch_sub(1, std::memory_order_release);

        delete context;

        return nullptr;
    }

    void inc_task_num(size_t num) {
        if (num + _task_num.fetch_add(num) >= FLAGS_async_executor_max_task_num) {
            LOG(WARNING) << "task number exceeds limit " << FLAGS_async_executor_max_task_num;
        }
    }

    void dec_task_num(size_t num) {
        if (_task_num.fetch_sub(num) < num) {
            LOG(FATAL) << "task number becomes negative after dec_task_num";
        }
    }

private:
    std::atomic<int64_t> _task_num;
    friend class AsyncTaskTracker;
};

class AsyncTaskTracker {
private:
    class Closure {
    public:
        Closure(AsyncExecutor* executor, size_t num) : _executor(executor), _num(num) {
            _executor->inc_task_num(_num);
        }

        ~Closure() {
            _executor->dec_task_num(_num);
        }

    private:
        AsyncExecutor* const _executor;
        const size_t _num;
    };

public:
    explicit AsyncTaskTracker(size_t num)
            : _closure(new Closure(AsyncExecutor::instance(), num)) {}
    AsyncTaskTracker() : AsyncTaskTracker(1) {}

private:
    std::unique_ptr<Closure> _closure;
};

}  // namespace mochow::common
