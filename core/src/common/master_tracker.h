/**
 * MOCHOW - A Vector Database System.
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved.
 *
 */

#pragma once

#include <string>
#include <vector>

#include <gflags/gflags.h>
#include <boost/atomic.hpp>
#include <base/endpoint.h>
#include <base/scoped_lock.h>
#include <base/memory/ref_counted.h>
#include <base/memory/singleton.h>
#include <base/threading/simple_thread.h>
#include <bthread.h>
#include <bthread_unstable.h>

#include "core/src/common/flags.h"
#include "core/src/common/thread_pool.h"
#include "core/src/common/leader_tracker.h"

namespace mochow::common {

DECLARE_int32(refresh_master_interval_s);

class MasterTracker : public base::RefCountedThreadSafe<MasterTracker>, public LeaderTracker {
public:
    static MasterTracker* GetInstance() { 
        return Singleton<MasterTracker>::get();
    }

    MasterTracker() {}
    virtual ~MasterTracker() {}

    int start(const base::EndPoint& local_addr, std::string token = "",
            std::string master_addr = "", uint32_t refresh_master_interval_s = 30);
    base::EndPoint get_master();
    std::vector<base::EndPoint> get_master_list();
    void get_backup_masters(const base::EndPoint& addr, std::vector<base::EndPoint>* backup_addrs);
    void set_master(const base::EndPoint& addr, std::vector<base::EndPoint>* addrs = nullptr);

protected:
    void check_leader(const std::vector<base::EndPoint>& addrs, std::map<base::EndPoint, int>* candidates);

private:
    friend struct DefaultSingletonTraits<MasterTracker>;
    friend class base::RefCountedThreadSafe<MasterTracker>;
};

#define g_master_tracker ::mochow::common::MasterTracker::GetInstance()

bool is_master_tracker_started();
int start_master_tracker(const base::EndPoint& local_addr,
                         std::string token = "",
                         std::string master_addr = "",
                         int32_t refresh_master_interval_s = 30);
void stop_master_tracker();

}

