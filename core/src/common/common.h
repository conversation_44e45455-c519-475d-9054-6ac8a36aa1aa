/***************************************************************************
 *
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

#pragma once

#include <map>
#include <set>
#include <string>
#include <vector>
#include <memory>
#include <cstdlib>
#include <cstdint>

#include "core/src/common/config.h"
#include "core/src/common/endpoint.h"
#include "core/src/common/status.h"
#include "core/src/common/logging.h"
#include "core/src/common/utils.h"
#include "core/src/common/likely.h"
#include "core/src/common/using.h"
#include "core/src/common/pbutils.h"
#include "core/src/common/host_address.h"
#include "core/src/common/bvar_define.h"
#include "core/src/common/time_measure.h"
#include "baidu/vdb/mochow/core/src/proto/error_code.pb.h"
#include "baidu/vdb/mochow/core/src/proto/common.pb.h"
#include "baidu/vdb/mochow/core/src/proto/schema.pb.h"

namespace mochow {

using SSMap = std::map<std::string, std::string>;

#if defined(UNIT_TEST)
#define UT_VIRTUAL virtual
#else
#define UT_VIRTUAL
#endif

}
