/**
 * MOCHOW - A Vector Database System.
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON> (<PERSON><PERSON><PERSON><PERSON>@baidu.com)
 * Description: bounded thread pool implemented by bthread condition variable.
 */

#pragma once

#include <queue>
#include <thread>
#include <string>
#include <base/logging.h>
#include <atomic>

#include <bthread.h>
#include <bthread/mutex.h>
#include <bthread/condition_variable.h>

#include "core/src/common/time_measure.h"
#include "core/src/common/using.h"
#include "sfl/attributes.h"
#include "sfl/logging.h"

#include "core/src/common/ithreadpool.h"
#include "core/src/common/butex.h"

namespace mochow::common {

template<typename T>
class ConcurrentBoundedQueue final {
public:
    ConcurrentBoundedQueue(size_t max_count = 0)
        : _max_count(max_count) {
    }

    ~ConcurrentBoundedQueue() = default;

    void push(T&& t) {
        std::unique_lock<bthread::Mutex> lck(_lock);
        if (_closed) {
            return;
        }

        while (_queue.size() == _max_count) {
            _full_cv.wait(lck);
            if (_closed) {
                return;
            }
        }

        _queue.push(std::forward<T>(t));
        _empty_cv.notify_one();
    }

    bool pop(T& t) {
        std::unique_lock<bthread::Mutex> lck(_lock);
        if (_closed) {
            return false;
        }

        while (_queue.empty()) {
            _empty_cv.wait(lck);
            if (_closed) {
                return false;
            }
        }

        t = std::move(_queue.front());
        _queue.pop();
        _full_cv.notify_one();
        return true;
    }

    size_t size() const {
        std::unique_lock<bthread::Mutex> lck(_lock);
        return _queue.size();
    }

    size_t capacity() const {
        std::unique_lock<bthread::Mutex> lck(_lock);
        return _max_count;
    }

    bool full() const {
        std::unique_lock<bthread::Mutex> lck(_lock);
        return _queue.size() == _max_count;
    }

    void close() {
        {
            std::unique_lock<bthread::Mutex> lck(_lock);
            _closed = true;
        }

        // unblock all pushes
        _full_cv.notify_all();
        // unblock all pops
        _empty_cv.notify_all();
    }

private:
    std::queue<T> _queue;

    size_t _max_count = 0;
    bool _closed = false;

    mutable bthread::Mutex _lock;
    bthread::ConditionVariable _full_cv;
    bthread::ConditionVariable _empty_cv;
};

template<typename T>
using ConcurrentBoundedQueuePtr = std::unique_ptr<ConcurrentBoundedQueue<T>>;

class BoundedThreadpool : public IThreadpool {
public:
    class StartOption;

    class TaskOption;

    BoundedThreadpool() = delete;

    // `name` is used as a prefix in bvar.
    explicit BoundedThreadpool(const std::string& name)
            : _name(name), _state(State::UNINITIALIZED), _bvar_recorder(_name) {}

    ~BoundedThreadpool() override;

    DISABLE_COPY_AND_MOVE(BoundedThreadpool)

    NODISCARD int start(const IStartOption* const option) override;

    void stop() override;

    NODISCARD int submit(Task task, const ITaskOption* const option) override;

    uint64_t get_pending_task_count() const override {
        return _task_queue->size();
    }

    uint64_t get_capacity() const {
        return _task_queue->capacity();
    }

    bool full() const {
        return _task_queue->full();
    }

    uint64_t get_max_task_execution_time_us() const override {
        LOG(ERROR) << "Unimplemented function get_max_task_execution_time_us() in class "
                   << "ConcurrentBoundedQueue.";
        ::abort();
    }

    int64_t get_enqueue_latency() const {
        return _bvar_recorder.get_enqueue_latency();
    }

    int64_t get_execute_latency() const {
        return _bvar_recorder.get_execute_latency();
    }

private:
    struct TaskQueueItem {
        Task task;
        const char* name;
        common::TimeStamp enqueue_time;
        uint64_t log_id;
        TRACEID trace_id;
    };

    class BvarRecorder {
    public:
        explicit BvarRecorder(const std::string& name)
        : _enqueue_latency_recorder(name, "enqueue"), _execute_latency_recorder(name, "execute") {
            LOG(NOTICE) << "Create enqueue latency recorder:" << _enqueue_latency_recorder.latency_name();
            LOG(NOTICE) << "Create execute latency recorder:" << _execute_latency_recorder.latency_name();
        }

        void add_enqueue_latency(int64_t latency_us) {
            _enqueue_latency_recorder << latency_us;
        }

        void add_execute_latency(int64_t latency_us) {
            _execute_latency_recorder << latency_us;
        }

        int64_t get_enqueue_latency() const {
            return _enqueue_latency_recorder.latency();
        }

        int64_t get_execute_latency() const {
            return _execute_latency_recorder.latency();
        }

    private:
        bvar::LatencyRecorder _enqueue_latency_recorder;
        bvar::LatencyRecorder _execute_latency_recorder;
    };

    void execute_task();

private:
    enum class State : uint8_t {
        UNINITIALIZED = 0,
        RUNNING       = 1,
        STOPPED       = 2
    };

    const std::string _name;

    std::atomic<State> _state;

    BvarRecorder _bvar_recorder;

    ConcurrentBoundedQueuePtr<TaskQueueItem> _task_queue = nullptr;

    std::vector<std::thread> _workers;

    // This mutex is to protect _state and _workers, and it is used in
    // the start function to block worker threads until start finishes.
    mutable common::Butex _threadpool_lock;

    // This mutex is to enforce sequential execution of the stop function.
    mutable common::Butex _threadpool_stop_lock;
};

class BoundedThreadpool::StartOption final : public IStartOption {
public:
    StartOption(size_t max_queue_size = 0, size_t worker_count = 0) :
        _max_queue_size(max_queue_size), _worker_count(worker_count) {}

    DISABLE_COPY_AND_MOVE(StartOption)

    NODISCARD size_t get_max_queue_size() const {
        return _max_queue_size;
    }

    void set_max_queue_size(size_t max_queue_size) {
        _max_queue_size = max_queue_size;
    }

    NODISCARD size_t get_worker_count() const {
        return _worker_count;
    }

    void set_worker_count(size_t worker_count) {
        _worker_count = worker_count;
    }

private:
    size_t _max_queue_size;
    size_t _worker_count;
};

class BoundedThreadpool::TaskOption final : public ITaskOption {
public:
    TaskOption() : _name(nullptr) {}

    // Will print the task's enqueue and execute latency at task finish, if `name` is not null.
    explicit TaskOption(const char* name) : _name(name) {}

    const char* name() const {
        return _name;
    }

    DISABLE_COPY_AND_MOVE(TaskOption);

private:
    const char* _name;
};

}   // namespace mochow::common
