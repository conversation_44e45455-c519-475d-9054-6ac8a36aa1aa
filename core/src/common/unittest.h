#pragma once

#include <base/memory/singleton.h>
#include <bthread/countdown_event.h>

namespace mochow::common {

class ThreadTestUtil {
public:
    static ThreadTestUtil* GetInstance() {
        return Singleton<ThreadTestUtil>::get();
    }

    void start(int count = 1) {
        _event.reset(count);
    }
    void wait() {
        _event.wait();
    }
    void complete() {
        _event.signal();
    }

private:
    ThreadTestUtil() {}
    ~ThreadTestUtil() {}
    DISALLOW_COPY_AND_ASSIGN(ThreadTestUtil);
    friend struct DefaultSingletonTraits<ThreadTestUtil>;

    bthread::CountdownEvent _event;
};

}

#define UT_START() mochow::common::ThreadTestUtil::GetInstance()->start();
#define UT_START_MULTI(count) mochow::common::ThreadTestUtil::GetInstance()->start(count);
#define UT_WAIT() mochow::common::ThreadTestUtil::GetInstance()->wait();
#define UT_COMPLETE() mochow::common::ThreadTestUtil::GetInstance()->complete();
