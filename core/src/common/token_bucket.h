/***************************************************************************
 *
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

#pragma once

#include <memory>
#include <chrono>
#include <algorithm>

#include "core/src/common/flags.h"
#include "core/src/common/butex.h"

namespace mochow::common {

class TokenBucket {
public:
    TokenBucket() {
        _tokens = FLAGS_token_bucket_max_tokens;
        _last_refill = std::chrono::steady_clock::now();
    }
    ~TokenBucket() = default;

    bool consume(uint64_t tokens_requested) {
        if (!FLAGS_enable_token_bucket) {
            return true;
        }

        ScopedButex lock(_butex);
        refill();
        if (_tokens >= tokens_requested) {
            _tokens -= tokens_requested;
            return true;
        }
        return false;
    }

private:
    // this function is **NOT** thread safe
    void refill() {
        auto now = std::chrono::steady_clock::now();
        auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(now - _last_refill).count();
        auto increment_token = elapsed * FLAGS_token_bucket_fill_rate;
        if (increment_token > 0) {
            _tokens = std::min(FLAGS_token_bucket_max_tokens, _tokens + increment_token);
            _last_refill = now;
        }
    }

    uint64_t _tokens;

    Butex _butex;

    std::chrono::steady_clock::time_point _last_refill;
};

inline std::unique_ptr<TokenBucket> g_token_bucket = std::make_unique<TokenBucket>();

}   // namespace mochow::common
