/**
 * MOCHOW - A Vector Database System.
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON> (<EMAIL>)
 * Date: 2023/09/18
 * Desciption: Implementations for sync point
 *
 */

#pragma once

#include <base/time.h>
#include <bthread.h>
#include "core/src/common/butex.h"

namespace mochow::common {

/////////////////////////////////////// bthread //////////////////////////////////////////
/////////////////////////////////////// bthread //////////////////////////////////////////
/////////////////////////////////////// bthread //////////////////////////////////////////

class SyncPoint {
public:
    explicit SyncPoint(int64_t count) : _count(count) {}

    int count() {
        ScopedMutexLock lock(_count_lock);
        return _count;
    }

    void wait() {
        ScopedMutexLock lock(_count_lock);
        while (_count > 0) {
            _count_lock.wait();
        }
    }

    int wait_until(const timespec* ts) {
        ScopedMutexLock lock(_count_lock);
        if (_count > 0) {
            return _count_lock.wait_until(ts);
        }
        return 0;
    }

    int wait_ms(const uint64_t ms) {
        ScopedMutexLock lock(_count_lock);
        if (_count > 0) {
            return _count_lock.wait_ms(ms);
        }
        return 0;
    }

    int signal(const int64_t decrement = 1) {
        ScopedMutexLock lock(_count_lock);
        if (_count < decrement) {
            _count = 0;
        } else {
            _count -= decrement;
        }
        if (_count == 0) {
            _count_lock.signal_all();
        }
        return _count;
    }

    void add_count(const int64_t v = 1) {
        ScopedMutexLock lock(_count_lock);
        _count += v;
    }
private:
    int64_t _count;
    ConditionLock _count_lock;
private:
    DISALLOW_COPY_AND_ASSIGN(SyncPoint);
};

class StatefulSyncPoint {
public:
    explicit StatefulSyncPoint(int32_t count = 0, int32_t wait_num = 0) : 
            _count(count), _wait_num(wait_num) {}

    int count() {
        ScopedMutexLock lock(_count_lock);
        return _count;
    }

    void wait() {
        ScopedMutexLock lock(_count_lock);
        while (_count > 0 && _wait_num > 0) {
            _count_lock.wait();
        }
    }
    bool wait(int millis) {
        timespec ts = base::milliseconds_from_now(millis);
        ScopedMutexLock lock(_count_lock);
        if (millis > 0) {
            while (_count > 0 && _wait_num > 0) {
                if (_count_lock.wait_until(&ts)) {
                    break;
                }
            }
        }
        return (_count <= 0);
    }

    bool wait_signal(int millis) {
        timespec ts = base::milliseconds_from_now(millis);
        ScopedMutexLock lock(_count_lock);
        if (millis > 0) {
            if (_wait_num > 0) {
                _count_lock.wait_until(&ts);
            }
        }
        return (_wait_num <= 0);
    }

    bool signal(bool is_change_count) {
        ScopedMutexLock lock(_count_lock);
        --_wait_num;
        if (is_change_count) {
            --_count;
        }
        if (_count <= 0 || _wait_num <= 0) {
            _count_lock.signal_all();
        }
        return (_wait_num <= 0);
    }

private:
    int32_t _count;
    int32_t _wait_num;
    common::ConditionLock _count_lock;

private:
    DISALLOW_COPY_AND_ASSIGN(StatefulSyncPoint);
};

class ConcurrencyLimitSyncPoint {
public:
    explicit ConcurrencyLimitSyncPoint(int32_t count = 0, int32_t wait_num = 0, int32_t max_concurrency = -1) : 
            _max_concurrency(max_concurrency), _count(count), _wait_num(wait_num) {}

    int count() {
        ScopedMutexLock lock(_count_lock);
        return _count;
    }

    void add_concurrency() {
        ScopedMutexLock lock(_concurrency_lock);
        while (_max_concurrency > 0 && _running_num >= _max_concurrency) {
            _concurrency_lock.wait();
        }
        ++_running_num;
    }
    void wait() {
        ScopedMutexLock lock(_count_lock);
        while (_count > 0 && _wait_num > 0) {
            _count_lock.wait();
        }
    }
    bool wait(int millis) {
        timespec ts = base::milliseconds_from_now(millis);
        ScopedMutexLock lock(_count_lock);
        if (millis > 0) {
            while (_count > 0 && _wait_num > 0) {
                if (_count_lock.wait_until(&ts)) {
                    break;
                }
            }
        }
        return (_count <= 0);
    }
    bool wait_signal(int millis) {
        timespec ts = base::milliseconds_from_now(millis);
        ScopedMutexLock lock(_count_lock);
        if (millis > 0) {
            if (_wait_num > 0) {
                _count_lock.wait_until(&ts);
            }
        }
        return (_wait_num <= 0);
    }
    bool signal(bool succ) {
        {
            ScopedMutexLock lock(_concurrency_lock);
            --_running_num;
            _concurrency_lock.signal_all();
        }
        ScopedMutexLock lock(_count_lock);
        --_wait_num;
        if (succ) {
            --_count;
        }
        if (_count <= 0 || _wait_num <= 0) {
            _count_lock.signal_all();
        }
        return (_wait_num <= 0);
    }

private:
    const int32_t _max_concurrency;
    int32_t _count = 0;
    int32_t _wait_num = 0;
    int32_t _running_num = 0;
    common::ConditionLock _count_lock;
    common::ConditionLock _concurrency_lock;

private:
    DISALLOW_COPY_AND_ASSIGN(ConcurrencyLimitSyncPoint);
};

class SyncPointGuard {
public:
    SyncPointGuard(SyncPoint* sync_point) : _sync_point(sync_point) {}

    ~SyncPointGuard() {
        if (_sync_point) {
            _sync_point->signal();
        }
    }

    SyncPoint* relese() {
        SyncPoint* ret = _sync_point;
        _sync_point = nullptr;
        return ret;
    }

private:
    SyncPoint* _sync_point = nullptr;
};

}
