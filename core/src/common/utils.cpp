/***************************************************************************
 *
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

#include <string>
#include <sstream>
#include <fstream>
#include <iomanip>
#include <cstdlib>

#include <uuid/uuid.h>
#include <bvar/bvar.h>
#include <boost/any.hpp>
#include <base/strings/string_util.h>

#include "core/src/common/utils.h"
#include "core/src/common/logging.h"
#include "core/src/common/system_metric_reader.h"

namespace mochow {

std::string mochow_logo() {
    return R"(
  __  __   ____    _____  _    _   ____ __          __
 |  \/  | / __ \  / ____|| |  | | / __ \\ \        / /
 | \  / || |  | || |     | |__| || |  | |\ \  /\  / / 
 | |\/| || |  | || |     |  __  || |  | | \ \/  \/ /  
 | |  | || |__| || |____ | |  | || |__| |  \  /\  /   
 |_|  |_| \____/  \_____||_|  |_| \____/    \/  \/    
                                                      )";
}

// Mem size is in kilobytes.
void mem_stat(MemInfo* mem_info) {
    uint64_t mem_total = 0;
    uint64_t mem_free = 0;
    uint64_t mem_buffers = 0;
    uint64_t mem_cached = 0;
    std::string tmp_str1;
    std::string tmp_str2;

    std::ifstream infile("/proc/meminfo");
    std::string line;
    while (std::getline(infile, line)) {
        std::istringstream iss(line);
        if (line.find("MemTotal") != std::string::npos) {
            if (!(iss >> tmp_str1 >> mem_total >> tmp_str2)) {
                break;
            }
        }
        else if (line.find("MemFree") != std::string::npos) {
            if (!(iss >> tmp_str1 >> mem_free >> tmp_str2)) {
                break;
            }
        }
        else if (line.find("Cached") != std::string::npos &&
            line.find("SwapCached") == std::string::npos) {
            if (!(iss >> tmp_str1 >> mem_cached >> tmp_str2)) {
                break;
            }
        }
        else if (line.find("Buffers") != std::string::npos) {
            if (!(iss >> tmp_str1 >> mem_buffers >> tmp_str2)) {
                break;
            }
        }
    }
    infile.close();
    mem_info->mem_total = mem_total;
    mem_info->mem_free = mem_free;
    mem_info->mem_cached = mem_cached;
    mem_info->mem_buffers = mem_buffers;
}

float mem_usage() {
    // calculate mem used percent
    int mem_usage = 0;
    MemInfo mem_info;
    mem_stat(&mem_info);
    if (mem_info.mem_total != 0) {
        uint64_t mem_free = mem_info.mem_free + mem_info.mem_cached + mem_info.mem_buffers;
        uint64_t mem_used = mem_info.mem_total - mem_free;
        float mem_usage_percent = static_cast<double>(mem_used) / static_cast<double>(mem_info.mem_total);
        return mem_usage_percent;
    }
    return 0;
}

size_t get_total_mem_size_in_mb() {
    if (FLAGS_datanode_memory_quota_in_mb == 0) {
        MemInfo mem_info;
        mem_stat(&mem_info);
        return mem_info.mem_total >> 10;
    }
    return FLAGS_datanode_memory_quota_in_mb;
}

size_t get_available_mem_size_in_bytes() {
    MemInfo mem_info;
    mem_stat(&mem_info);

    ProcMemory m;
    bool b = g_proc_memory_reader->read(m);
    if (!b) {
        uint64_t mem_available = mem_info.mem_free + mem_info.mem_cached + mem_info.mem_buffers;
        return mem_available << 10;
    } 
    uint64_t current_system_mem_in_bytes = (static_cast<uint64_t>(m.resident * 4)) << 10;
    uint64_t total_mem_size_in_bytes = (FLAGS_datanode_memory_quota_in_mb == 0) ? mem_info.mem_total << 10 : FLAGS_datanode_memory_quota_in_mb << 20;
    uint64_t ret = (total_mem_size_in_bytes < current_system_mem_in_bytes) ? 0 : total_mem_size_in_bytes - current_system_mem_in_bytes;
    return ret;
}

std::string uint64_to_byteunit(uint64_t n) {
    std::ostringstream oss;
    int power = 0;
    std::string unit;
    if (n >= 1024UL * 1024 * 1024 * 1024 * 1024) {
        power = 5;
        unit = "PB";
    } else if (n >= 1024UL * 1024 * 1024 * 1024) {
        power = 4;
        unit = "TB";
    } else if (n >= 1024UL * 1024 * 1024) {
        power = 3;
        unit = "GB";
    } else if (n >= 1024 * 1024) {
        power = 2;
        unit = "MB";
    } else if (n >= 1024) {
        power = 1;
        unit = "KB";
    }
    if (power > 0) {
        oss << std::setiosflags(std::ios::fixed) << std::setprecision(2)
            << ((double) n / pow(1024, power)) << unit;
    } else {
        oss << n << unit;
    }
    return oss.str();
}

std::string int64_to_byteunit(int64_t n) {
    if (n >= 0) {
        return uint64_to_byteunit(n);
    }

    std::ostringstream oss;
    oss << "-" << uint64_to_byteunit(-n);
    return oss.str();
}

bool byteunit_to_uint64(const std::string &n, uint64_t *result) {
    size_t pos = 0;
    for (; pos < n.size(); ++pos) {
        if (n[pos] != '.' && (n[pos] < '0' || n[pos] > '9')) {
            break;
        }
    }
    char *end = NULL;
    *result = strtoull(n.c_str(), &end, 10);
    if (&n[pos] != end) {
        return false;
    }
    std::string unit;
    while (pos < n.size()) {
        unit.append(1, toupper(n[pos++]));
    }
    if (unit.empty() || unit == "B") {
        return true;
    } else if (unit == "K" || unit == "KB") {
        *result *= 1024;
        return true;
    } else if (unit == "M" || unit == "MB") {
        *result *= 1024 * 1024;
        return true;
    } else if (unit == "G" || unit == "GB") {
        *result *= 1024UL * 1024 * 1024;
        return true;
    } else if (unit == "T" || unit == "TB") {
        *result *= 1024UL * 1024 * 1024 * 1024;
        return true;
    }
    return false;
}

std::string generate_uuid() {
    uuid_t id;
    uuid_generate(id);

    char str[37];
    uuid_unparse(id, str);
    return str;
}

std::string us2string(uint64_t time_us) {
    char buf[256];
    time_t rawtime = time_us / 1000000;
    struct tm timeinfo;
    std::ignore = localtime_r(&rawtime, &timeinfo);
    strftime(buf, 256, "%Y-%m-%d %H:%M:%S", &timeinfo);
    return std::string(buf);
}

bool starts_with(const std::string& str, const std::string& prefix) {
    if (str.empty() || prefix.empty()) { return false; }
    if (str.size() < prefix.size()) { return false; }
    return std::strncmp(str.data(), prefix.data(), prefix.size()) == 0;
}

bool find_device_from_path(const std::string& disk_path,
                           std::string* dev_name, dev_t* dev_no) {
    // LStat to get device no
    struct stat s;
    if (lstat(disk_path.c_str(), &s) != 0) {
        LOG(WARNING) << "Fail to find device due to lstat disk path failed,"
                     << " disk_path:" << disk_path
                     << " errno:" << errno << " errmsg:" << strerror(errno);
        return false;
    }
    *dev_no = s.st_dev;

    // Find /proc/diskstats to get dev name
    base::ScopedFILE fp(fopen("/proc/diskstats", "r"));
    if (fp == NULL) {
        LOG(WARNING) << "Fail to find device due to fopen /proc/diskstats failed,"
                     << " disk_path:" << disk_path
                     << " errno:" << errno << " errmsg:" << strerror(errno);
        return false;
    }
    char line[1024];
    char dev_name_buf[256];
    uint64_t dev_major, dev_minor, ios_pgr, total_ticks, rq_ticks, wr_ticks,
             rd_ios, rd_merges, rd_ticks, wr_ios, wr_merges, rd_sec, wr_sec;
    while (fgets(line, sizeof(line) - 1, fp) != NULL) {
        /* major minor name rio rmerge rsect ruse wio wmerge wsect wuse running use aveq */
        int n = sscanf(line, "%20lu %20lu %255s %20lu %20lu %20lu %20lu %20lu %20lu %20lu %20lu %20lu %20lu %20lu",
            &dev_major, &dev_minor, dev_name_buf,
            &rd_ios, &rd_merges, &rd_sec, &rd_ticks,
            &wr_ios, &wr_merges, &wr_sec, &wr_ticks, &ios_pgr, &total_ticks, &rq_ticks);
        if (n != 14) {
            continue;
        }
        if (dev_major == major(s.st_dev) && dev_minor == minor(s.st_dev)) {
            *dev_name = dev_name_buf;
            break;
        }
    }

    if (dev_name->empty()) {
        LOG(WARNING) << "Fail to find device from /proc/diskstats for disk path " << disk_path;
        return false;
    }

    return true;
}

}
