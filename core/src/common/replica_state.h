/***************************************************************************
 *
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

#pragma once

#include <string>

namespace mochow::common {

class ReplicaState final {
public:
    static constexpr const char* JOINING = "JOINING";
    static constexpr const char* CATCHING = "CATCHING";
    static constexpr const char* NORMAL = "NORMAL";

    static inline bool is_valid(const std::string& state) {
        return state == JOINING || state == NORMAL || state == CATCHING;
    }

private:
    ReplicaState() = default;
    ~ReplicaState() = default;
};

}
