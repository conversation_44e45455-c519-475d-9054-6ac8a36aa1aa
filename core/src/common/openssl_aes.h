#pragma once

#include <cassert>
#include <cmath>
#include <string>
#include <memory>

#include "openssl/aes.h"
#include "openssl/evp.h"
#include "openssl/pem.h"

namespace mochow::common {

enum EncryptMode {
    CBC = 0,
    ECB = 1,
    INVALID = 2
};

class OpenSSLAES {
public:
    // echo DEFAULT_AES_KEY | md5sum -> 9fa35951cb60e594603041befaa48164
    // echo DEFAULT_AES_IV  | md5sum -> 0552dcbffc730401a590965541de504f
    OpenSSLAES(const uint32_t key_len) : _default_key("9fa35951cb60e594603041befaa48164"),
                                         _default_iv("0552dcbffc730401") {    
        _key_len = (key_len != 16 && key_len != 24 && key_len != 32) ? 16 : key_len;
    }

    ~OpenSSLAES() = default; 
    
    int aes_encrypt(const std::string& plain_text, const std::string& key, std::string* cipher_text, 
                    const EncryptMode enc_mode = EncryptMode::CBC, const std::string& iv = "0552dcbffc730401");

    int aes_decrypt(const std::string& cipher_text, const std::string& key, std::string* plain_text, 
                    const EncryptMode enc_mode = EncryptMode::CBC, const std::string& iv = "0552dcbffc730401");

private:
    // pad_type: 1: pad or truncate key; 2: pad or truncate iv
    // enc: 1: AES_ENCRYPT; 0: AES_DECRYPT
    void check_and_pad(const int pad_type, const int enc, std::string* str);

    int set_key_and_iv(const int enc, std::string* key_str, std::string* iv_str, 
                       AES_KEY* aes_key, const EncryptMode enc_mode = EncryptMode::CBC);
    int encrypt_or_decrypt(const std::string& in_data, const int enc, const EncryptMode enc_mode, 
                       const AES_KEY aes_key, const std::string& iv, std::string* out_data);

#ifdef _UNIT_TEST
    uint32_t key_len() { 
        return _key_len; 
    }
#endif

private:
    std::string _default_key; // default secret key
    std::string _default_iv;  // default initialization vector(for cbc)
    uint32_t    _key_len;     // 16,24,32
};

int base64_encode(const std::string& in_str, std::string* out_str);

int base64_decode(const std::string& in_str, std::string* out_str);

inline std::unique_ptr<OpenSSLAES> g_password_aes_encryptor = std::unique_ptr<OpenSSLAES>(new OpenSSLAES(16));

}
