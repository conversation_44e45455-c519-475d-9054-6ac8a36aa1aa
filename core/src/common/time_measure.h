/***************************************************************************
 *
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

#pragma once

#include <base/time.h>
#include <bvar/bvar.h>

namespace mochow::common {

class TimeMeasure final {
public:
    TimeMeasure(bvar::LatencyRecorder* recorder) :
        _latency_recorder(recorder) {
        _timer.start();
    }

    ~TimeMeasure() {
        _timer.stop();

        if (_latency_recorder != nullptr) {
            (*_latency_recorder) << _timer.u_elapsed();
        }
    }

    TimeMeasure(const TimeMeasure&) = delete;
    TimeMeasure& operator=(const TimeMeasure&) = delete;

private:
    bvar::LatencyRecorder* _latency_recorder = nullptr;
    base::Timer _timer;
};

class TimeStamp {
public:
    TimeStamp() = delete;

    static TimeStamp now() {
        return TimeStamp(base::gettimeofday_us());
    }

    static TimeStamp invalid_time() {
        return TimeStamp(INT64_MAX);
    }

    TimeStamp operator+(int64_t duration_us) const {
        return TimeStamp(_time_us + duration_us);
    }

    int64_t operator-(TimeStamp rhs) const {
        return _time_us - rhs._time_us;
    }

    bool operator<(const TimeStamp& other) const {
        return _time_us < other._time_us;
    }

    bool operator>(const TimeStamp& other) const {
        return _time_us > other._time_us;
    }

    bool operator==(const TimeStamp& other) const {
        return _time_us == other._time_us;
    }

    bool operator>=(const TimeStamp& other) const {
        return _time_us >= other._time_us;
    }

    bool operator<=(const TimeStamp& other) const {
        return _time_us <= other._time_us;
    }

private:
    explicit TimeStamp(int64_t time_us) : _time_us(time_us) {}
    int64_t _time_us;
};

}
