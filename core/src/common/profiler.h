/**
 * MOCHOW - A Vector Database System.
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON> (<EMAIL>)
 * Date: 2023/11/06
 */
#pragma once

#include <chrono>

namespace mochow::common {

//! The profiler can be used to measure elapsed time
class Profiler {
public:
    //! Starts the timer
    void start() {
        _finished = false;
        _start = base::gettimeofday_us();;
    }
    //! Finishes timing
    void end() {
        _end = base::gettimeofday_us();;
        _finished = true;
    }

    //! Returns the elapsed time in microseconds. If End() has been called, returns
    //! the total elapsed time. Otherwise returns how far along the timer is
    //! right now.
    uint64_t elapsed() const {
        uint64_t end = _finished ? _end : base::gettimeofday_us();
        return end - _start;
}

private:
    uint64_t _start;
    uint64_t _end;
    bool _finished = false;
};

} // mochow::common
