/**
 * MOCHOW - A Vector Database System.
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON> (<EMAIL>)
 * Date: 2023/11/17
 * Desciption: Implementations for Config Reloader Service
 *
 */

#include <base/endpoint.h>
#include <baidu/rpc/server.h>
#include "core/src/common/common.h"
#include "core/src/common/config_reloader.h"
#include "core/src/common/config_reloader_service.h"

namespace mochow::common {

void ConfigReloaderServiceImpl::reload_conf(::google::protobuf::RpcController* controller,
        const pb::UpdateConfRequest* request,
        pb::AckResponse* response,
        ::google::protobuf::Closure* done) {
    baidu::rpc::Controller* cntl = (baidu::rpc::Controller*)controller;
    baidu::rpc::ClosureGuard done_guard(done);
    uint64_t log_id = cntl->log_id();
    base::EndPoint remote_addr = cntl->remote_side();
    auto status = response->mutable_status();

    do {
        if (!g_config_reloader->verify_token(request->token())) {
            status->set_code(ERR_INVALID_TOKEN);
            status->set_msg("Invalid token");
            break;
        }
        bool ok = g_config_reloader->reload();
        if (!ok) {
            status->set_code(ERR_FAIL);
            status->set_msg("Reload config failed");
            break;
        }
        status->set_code(OK);
        status->set_msg("Success");
    } while (0);

    RPC_LOG(NOTICE) << "Reload config, remote_addr:"
        << common::endpoint2str(remote_addr)
        << " request:" << common::pb2json(*request)
        << " response:" << common::pb2json(*response);
}

void ConfigReloaderServiceImpl::update_conf(::google::protobuf::RpcController* controller,
        const pb::UpdateConfRequest* request,
        pb::AckResponse* response,
        ::google::protobuf::Closure* done) {
    baidu::rpc::Controller* cntl = (baidu::rpc::Controller*)controller;
    baidu::rpc::ClosureGuard done_guard(done);
    uint64_t log_id = cntl->log_id();
    base::EndPoint remote_addr = cntl->remote_side();
    auto status = response->mutable_status();

    do {
        auto conf_key = request->conf_key();
        auto conf_value = request->conf_value();
        if (!g_config_reloader->verify_token(request->token())) {
            status->set_code(ERR_INVALID_TOKEN);
            status->set_msg("Invalid token");
            break;
        }
        bool ok = g_config_reloader->update_config_file(conf_key, conf_value);
        if (!ok) {
            status->set_code(ERR_FAIL);
            status->set_msg("Update config failed");
            break;
        }
        status->set_code(OK);
        status->set_msg("Success");
    } while (0);

    RPC_LOG(NOTICE) << "Update config, remote_addr:"
        << common::endpoint2str(remote_addr)
        << " request:" << common::pb2json(*request)
        << " response:" << common::pb2json(*response);
}

}
