/**
 * MOCHOW - A Vector Database System.
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON> (<EMAIL>)
 * Date: 2023/09/18
 * Desciption: Implementations for bthread lock
 *
 */

#pragma once

#include <base/time.h>
#include <bthread.h>

namespace mochow::common {

/////////////////////////////////////// bthread //////////////////////////////////////////
/////////////////////////////////////// bthread //////////////////////////////////////////
/////////////////////////////////////// bthread //////////////////////////////////////////

class MutexLock {
public:
    MutexLock() {
        bthread_mutex_init(&_mutex, NULL);
    }

    ~MutexLock() {
        bthread_mutex_destroy(&_mutex);
    }

    void lock() {
        bthread_mutex_lock(&_mutex);
    }

    void unlock() {
        bthread_mutex_unlock(&_mutex);
    }

    bthread_mutex_t* get_mutex() { // non-const
        return &_mutex;
    }

private:
    bthread_mutex_t _mutex;
private:
    DISALLOW_COPY_AND_ASSIGN(MutexLock);
};

class ConditionLock {
public:
    ConditionLock() {
        bthread_mutex_init(&_mutex, NULL);
        bthread_cond_init(&_condition, NULL);
    }

    ~ConditionLock() {
        bthread_mutex_destroy(&_mutex);
        bthread_cond_destroy(&_condition);
    }

    bthread_mutex_t* get_mutex() { // non-const
        return &_mutex;
    }

    void wait() {
        bthread_cond_wait(&_condition, get_mutex());
    }

    void signal() {
        bthread_cond_signal(&_condition);
    }

    int wait_until(const struct timespec* ts) {
        return bthread_cond_timedwait(&_condition, get_mutex(), ts);
    }

    int wait_ms(const uint64_t ms) {
        auto ts = base::milliseconds_from_now(ms);
        return bthread_cond_timedwait(&_condition, get_mutex(), &ts);
    }

    void signal_all() {
        bthread_cond_broadcast(&_condition);
    }

private:
    bthread_mutex_t         _mutex;
    bthread_cond_t          _condition;
private:
    DISALLOW_COPY_AND_ASSIGN(ConditionLock);
};

class ScopedMutexLock {
public:
    explicit ScopedMutexLock(MutexLock& mutex)
        : _mutex(mutex.get_mutex()) {
        bthread_mutex_lock(_mutex);
    }

    explicit ScopedMutexLock(ConditionLock& cond)
        : _mutex(cond.get_mutex()) {
        bthread_mutex_lock(_mutex);
    }

    ~ScopedMutexLock() {
        bthread_mutex_unlock(_mutex);
    }

private:
    bthread_mutex_t*    _mutex;
private:
    DISALLOW_COPY_AND_ASSIGN(ScopedMutexLock);
};

using Butex = MutexLock;
using ScopedButex = ScopedMutexLock;

#define SCOPED_LOCK_BUTEX(_BUTEX_) common::ScopedButex _lock_(_BUTEX_);

}
