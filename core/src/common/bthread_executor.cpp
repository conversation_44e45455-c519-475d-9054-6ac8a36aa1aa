/***************************************************************************
 *
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

#include "core/src/common/bthread_executor.h"

namespace mochow::common {

int BthreadExecutor::add(Func func) {
    auto* args = new Func(::std::move(func));
    ::bthread_t th;
    if (0 != ::bthread_start_background(&th, &BTHREAD_ATTR_NORMAL, bthread_run, args)) {
        LOG(ERROR) << "Fail to start bthread";
        delete args;
        return -1;
    }
    return 0;
}

int BthreadExecutor::add_urgent(Func func) {
    auto* args = new Func(::std::move(func));
    ::bthread_t th;
    if (0 != ::bthread_start_urgent(&th, &BTHREAD_ATTR_NORMAL, bthread_run, args)) {
        LOG(ERROR) << "Fail to start bthread";
        delete args;
        return -1;
    }
    return 0;
}

int BthreadCountdownExecutor::add(Func func) {
    CountdownArg* args = new CountdownArg();
    args->func = new Func(std::move(func));
    args->cond = _cond;

    ::bthread_t th;
    if (0 != ::bthread_start_background(&th, &BTHREAD_ATTR_NORMAL, bthread_run, args)) {
        LOG(ERROR) << "Fail to start bthread";
        delete args;
        return -1;
    }
    _tids.emplace_back(th);
    return 0;
}

int BthreadCountdownExecutor::add_urgent(Func func) {
    CountdownArg* args = new CountdownArg();
    args->func = new Func(std::move(func));
    args->cond = _cond;

    ::bthread_t th;
    if (0 != ::bthread_start_urgent(&th, &BTHREAD_ATTR_NORMAL, bthread_run, args)) {
        LOG(ERROR) << "Fail to start bthread";
        delete args;
        return -1;
    }
    _tids.emplace_back(th);
    return 0;
}

}
