#pragma once
#include <memory>
#include <mutex>
#include <shared_mutex>
#include <unordered_set>
#include <algorithm>
#include <sstream>

#include <boost/dynamic_bitset.hpp>
#include <base/logging.h>

#include "baidu/vdb/mochow/core/src/proto/data_rpc.pb.h"
#include "roaring64map.hh"

#include "sfl/attributes.h"
#include "core/src/common/using.h"
#include "sfl/logging.h"


namespace mochow::common {

class IBitMap;
class IBitMapIterator;
using IBitMapRef = std::shared_ptr<IBitMap>;
using IBitMapPtr = std::unique_ptr<IBitMap>;
using IConstBitMapRef = std::shared_ptr<const IBitMap>;
using IConstBitMapPtr = std::unique_ptr<const IBitMap>;
using IBitMapIteratorRef = std::shared_ptr<IBitMapIterator>;
using IBitMapIteratorPtr = std::unique_ptr<IBitMapIterator>;

class IBitMapIterator {
public:
    IBitMapIterator() = default;

    virtual ~IBitMapIterator() = default;

    virtual bool valid() const = 0;

    virtual void seek_to_first() = 0;

    virtual void seek(const uint64_t target) = 0;

    virtual void next() = 0;

    virtual uint64_t value() = 0;
};

enum class BitMapType {
    ROARING_BITMAP = 0,
    VECTOR_BITMAP = 1,
};

class IBitMap {
public:
    virtual ~IBitMap() = default;

    virtual BitMapType type() const = 0;

    virtual void insert(const uint64_t id) = 0;

    virtual void batch_insert(const std::vector<uint64_t>& ids) = 0;

    virtual void erase(const uint64_t id) = 0;

    virtual void clear() = 0;

    virtual bool merge(const IBitMap& other) = 0;

    virtual bool intersect(const IBitMap& other) = 0;

    // Removes the bits that are in `other`.
    virtual bool difference(const IBitMap& other) = 0;

    virtual void flip() = 0;

    virtual bool contains(const uint64_t id) const = 0;

    virtual size_t count() const = 0;

    virtual size_t size() const = 0;

    virtual std::string to_string() const = 0;

    virtual void list_ids(std::vector<uint64_t>* ids) const = 0;

    virtual IBitMapIteratorRef create_iterator() const = 0;

    virtual IBitMapPtr copy() const = 0; // make a deep-copy
};

/*
    Roaring BitMap is more memory efficient when compared to std/boost bitmap and also provides excellent performance with SIMD optimization,
    it has been widely used in some open source projects, such as Apache Doris, ClickHouse and StarRocks.
    more details infos can be found from here: https://roaringbitmap.org/
*/

class RoaringBitMapIterator;

class RoaringBitMap : public IBitMap {
public:
    BitMapType type() const override {
        return BitMapType::ROARING_BITMAP;
    }

    virtual void insert(const uint64_t id) {
        _bitmap.add(id);
    }

    virtual void batch_insert(const std::vector<uint64_t>& ids) {
        _bitmap.addMany(ids.size(), ids.data());
    }

    virtual void erase(const uint64_t id) {
        _bitmap.remove(id);
    }

    virtual void clear() {
        _bitmap.clear();
    }

    bool merge(const IBitMap& other) override {
        if (other.type() != BitMapType::ROARING_BITMAP) {
            return false;
        }

        _bitmap |= static_cast<const RoaringBitMap&>(other)._bitmap;
        return true;	
    }

    bool intersect(const IBitMap& other) {
        if (other.type() != BitMapType::ROARING_BITMAP) {
            return false;
        }

        _bitmap &= static_cast<const RoaringBitMap&>(other)._bitmap;
        return true;
    }

    bool difference(const IBitMap& other) override {
        if (other.type() != BitMapType::ROARING_BITMAP) {
            return false;
        }

        _bitmap -= static_cast<const RoaringBitMap&>(other)._bitmap;
        return true;
    }

    virtual void flip() {
        _bitmap.flip(0, _bitmap.maximum() + 1);
    }

    virtual bool contains(const uint64_t id) const {
        return _bitmap.contains(id);
    }

    virtual size_t count() const {
        return _bitmap.cardinality();
    }

    // this one is different from boost one
    virtual size_t size() const {
        return _bitmap.getSizeInBytes();
    }

    virtual std::string to_string() const {
        return _bitmap.toString();
    }

    const roaring::Roaring64Map& impl() const {
        return _bitmap;
    }

    virtual IBitMapIteratorRef create_iterator() const {
        auto iter = std::make_shared<RoaringBitMapIterator>(*this);
        return std::static_pointer_cast<IBitMapIterator>(iter);
    }

    void list_ids(std::vector<uint64_t>* ids) const override {
        ids->reserve(_bitmap.cardinality());
        for (auto iter = _bitmap.begin(); iter != _bitmap.end(); ++iter) {
            ids->push_back(*iter);
        }
    }

    IBitMapPtr copy() const override {
        auto res = std::make_unique<RoaringBitMap>();
        res->_bitmap = _bitmap; // copy on write
        return res;
    }

private:
    roaring::Roaring64Map _bitmap;
};

class RoaringBitMapIterator : public IBitMapIterator {
public:
    RoaringBitMapIterator(const RoaringBitMap& roaring_bitmap) :
            _roaring_bitmap(roaring_bitmap),
            _iterator(_roaring_bitmap.impl().begin()),
            _end_iterator(_roaring_bitmap.impl().end()) {
    }

    virtual ~RoaringBitMapIterator() = default;

    virtual bool valid() const {
        return _iterator != _end_iterator;
    }

    virtual void seek_to_first() {
        _iterator = _roaring_bitmap.impl().begin();
    }

    virtual void seek(const uint64_t target) {
        _iterator.move(target);
    }

    virtual void next() {
        ++_iterator;
    }

    virtual uint64_t value() {
        assert(valid());
        return *_iterator;
    }

private:
    const RoaringBitMap& _roaring_bitmap;
    roaring::Roaring64Map::const_iterator _iterator;
    roaring::Roaring64Map::const_iterator _end_iterator;
};

class VectorBitmapIterator : public IBitMapIterator {
public:
    explicit VectorBitmapIterator(const std::vector<uint64_t>& vector) :
            _vector(vector),
            _iterator(_vector.begin()),
            _end_iterator(_vector.end()) {
    }

    bool valid() const override {
        return _iterator != _end_iterator;
    }

    void seek_to_first() override {
        _iterator = _vector.begin();
    }

    void seek(const uint64_t target) override {
        _iterator = std::lower_bound(_vector.begin(), _vector.end(), target);
    }

    void next() override {
        if (_iterator != _end_iterator) {
            ++_iterator;
        }
    }

    uint64_t value() override {
        assert(valid());
        return *_iterator;
    }

    const std::vector<uint64_t>& impl() const {
        return _vector;
    }

private:
    const std::vector<uint64_t>& _vector;
    std::vector<uint64_t>::const_iterator _iterator;
    std::vector<uint64_t>::const_iterator _end_iterator;
};

// Using a `std::vector` to store the INCIDs. In the vector, the INCIDs are sorted.
//
// When the number of elements is small, `VectorBitMap` will be more memory-efficent than `RoaringBitMap`.
class VectorBitMap : public IBitMap {
public:
    VectorBitMap() = default;

    explicit VectorBitMap(std::vector<uint64_t>&& ids) {
        batch_insert(ids);
    }

    BitMapType type() const override {
        return BitMapType::VECTOR_BITMAP;
    }

    void insert(const uint64_t id) override {
        // Check if the ID already exists
        auto it = std::lower_bound(_bitmap.begin(), _bitmap.end(), id);
        if (it == _bitmap.end() || *it != id) {
            // Insert the ID in sorted order
            _bitmap.insert(it, id);
        }
        _bitmap.shrink_to_fit();
    }

    void batch_insert(const std::vector<uint64_t>& ids) override {
        if (ids.empty()) {
            return;
        }

        // Reserve space
        size_t new_size = _bitmap.size() + ids.size();
        _bitmap.reserve(new_size);

        // Copy current bitmap
        std::vector<uint64_t> temp = _bitmap;

        // Create a sorted copy of the ids to insert
        std::vector<uint64_t> sorted_ids = ids;
        std::sort(sorted_ids.begin(), sorted_ids.end());

        // Remove duplicates from sorted_ids
        auto last = std::unique(sorted_ids.begin(), sorted_ids.end());
        sorted_ids.erase(last, sorted_ids.end());

        // Merge the two sorted vectors
        _bitmap.resize(temp.size() + sorted_ids.size());
        std::merge(temp.begin(), temp.end(),
                  sorted_ids.begin(), sorted_ids.end(),
                  _bitmap.begin());

        // Remove duplicates from the final result
        last = std::unique(_bitmap.begin(), _bitmap.end());
        _bitmap.erase(last, _bitmap.end());
        _bitmap.shrink_to_fit();
    }

    void erase(const uint64_t id) override {
        auto it = std::lower_bound(_bitmap.begin(), _bitmap.end(), id);
        if (it != _bitmap.end() && *it == id) {
            _bitmap.erase(it);
        }
        _bitmap.shrink_to_fit();
    }

    void clear() override {
        _bitmap.clear();
        _bitmap.shrink_to_fit();
    }

    bool contains(const uint64_t id) const override {
        auto it = std::lower_bound(_bitmap.begin(), _bitmap.end(), id);
        return it != _bitmap.end() && *it == id;
    }

    size_t count() const override {
        return _bitmap.size();
    }

    size_t size() const override {
        return _bitmap.capacity() * sizeof(uint64_t);
    }

    std::string to_string() const override {
        std::stringstream ss;
        ss << "{";
        for (size_t i = 0; i < _bitmap.size(); i++) {
            if (i > 0) {
                ss << ",";
            }
            ss << _bitmap[i];
        }
        ss << "}";
        return ss.str();
    }

    IBitMapIteratorRef create_iterator() const override {
        return std::make_shared<VectorBitmapIterator>(_bitmap);
    }

    const std::vector<uint64_t>& impl() const {
        return _bitmap;
    }

    void list_ids(std::vector<uint64_t>* ids) const override {
        *ids = _bitmap;
    }

    IBitMapPtr copy() const override {
        std::vector<uint64_t> bitmap = _bitmap;  // copy
        return std::make_unique<VectorBitMap>(std::move(bitmap));
    }

    // Not supported interfaces:
    bool merge(const IBitMap& other) override {
        LOG_AND_ASSERT(false);
        return false;
    }
    bool intersect(const IBitMap& other) override {
        LOG_AND_ASSERT(false);
        return false;
    }

    bool difference(const IBitMap& other) override {
        LOG_AND_ASSERT(false);
        return false;
    }

    void flip() override {
        LOG_AND_ASSERT(false);
    }

private:
    std::vector<uint64_t> _bitmap;
};

class VidBitmap : public RoaringBitMap {
public:
    static VidBitmap deserialize_from_pb(const pb::VidBitmap& pb) {
        VidBitmap res;
        for (int idx = 0; idx < pb.vids_size(); ++idx) {
            res.insert(pb.vids(idx));
        }

        return res;
    }

    void serialize_to_pb(pb::VidBitmap* pb) const {
        if (count() == 0) {
            return;
        }

        for (RoaringBitMapIterator iter(*this); iter.valid(); iter.next()) {
            pb->add_vids(iter.value());
        }
    }

    VidBitmap() = default;
};

}  // namespace mochow::common
