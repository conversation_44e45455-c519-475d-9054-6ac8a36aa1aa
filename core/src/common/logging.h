/***************************************************************************
 *
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

#pragma once

#include <base/logging.h>
#include <gflags/gflags.h>
#include "core/src/common/trace_log.h"

namespace mochow::common {

DECLARE_string(comlog_path);

#define RPC_LOG(level) LOG(level) << "[log_id:" << log_id << "] "

#define RPC_RECV_LOG(level) \
        RPC_LOG(level) << "[cmd:" << __FUNCTION__ << "]" \
                       << " recv " << request->GetTypeName() \
                       << " from " << cntl->remote_side()

#define RPC_RECV_TRACE_LOG(level) \
        RPC_TRACE_LOG(level) << "[cmd:" << __FUNCTION__ << "]" \
                             << " recv " << request->GetTypeName() \
                             << " from " << cntl->remote_side()

#define RPC_REQUEST_LOG(level) \
        RPC_RECV_LOG(level) << " request:" << common::pb2json(*request)

#define RPC_REQUEST_TRACE_LOG(level) \
        RPC_RECV_TRACE_LOG(level) << " request:" << common::pb2json(*request)

#define RPC_ACK_LOG(level) \
        RPC_LOG(level) << "[cmd:" << __FUNCTION__ << "]" \
                << " ack " << response->GetTypeName() \
                << " to " << cntl->remote_side()       \
                << " status:" << common::pb2json(response->status())

#define RPC_ACK_TRACE_LOG(level) \
        RPC_TRACE_LOG(level) << "[cmd:" << __FUNCTION__ << "]" \
                             << " ack " << response->GetTypeName() \
                             << " to " << cntl->remote_side()       \
                             << " status:" << common::pb2json(response->status())

#define HANDLER_RPC_LOG(level) LOG(level) << "[log_id:" << log_id << "] [http_request_id:" << request.id << "] "

#define HANDLER_REQ_LOG(level) LOG(level) << "[http_request_id:" << request.id << "] "

int init_comlog();

}
