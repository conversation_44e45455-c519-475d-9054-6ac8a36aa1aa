/**
 * MOCHOW - A Vector Database System.
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON> (guo<PERSON>@baidu.com)
 * Date: 2023/10/01
 * Desciption: Implementations for basic configs and comlog
 *
 */

#include "core/src/common/config.h"
#include "core/src/common/logging.h"
#include "core/src/common/endpoint.h"
#include "core/src/common/host_address.h"

namespace logging {
    DECLARE_int32(min_log_level);
}

namespace mochow {
    DECLARE_bool(standalone_mode);
}

namespace mochow::common {

DEFINE_int32(port, 8287, "Server local listen port");
DEFINE_string(interfaces, "bond0,xgbe0,xgbe1,eth1,eth0", "Interface list of this server");


base::EndPoint s_local_addr;
base::EndPoint s_master_addr;

int init_local_addr() {
    base::EndPoint addr;
    if (FLAGS_interfaces.empty()) {
        addr.ip = base::my_ip();
    } else {
        addr.ip = base::int2ip(common::get_host_ip_by_interfaces(FLAGS_interfaces.c_str()));
        if (base::ip2int(addr.ip) == 0) {
            addr.ip = base::my_ip();
        }
    }
    if (base::ip2int(addr.ip) == 0 || base::ip2int(addr.ip) == UINT32_MAX) {
        LOG(ERROR) << "Unable to get a valid ip from baidu-rpc or interfaces,"
                   << " invalid_ip: " << common::ip2str(addr.ip);
        return -1;
    }

    if (FLAGS_port < 1000 || FLAGS_port > 65535) {
        LOG(ERROR) << "Fail to init local addr due to invalid local port " << FLAGS_port
                   << " (should be in [1000, 65535])";
        return -1;
    }

    if (mochow::FLAGS_standalone_mode) {
        base::EndPoint addr2;
        common::str2endpoint("127.0.0.1", FLAGS_port, &addr2);
        addr.ip = addr2.ip;
    }

    addr.port = FLAGS_port;
    s_local_addr = addr;
    return 0;
}

base::EndPoint get_local_addr() {
    return s_local_addr;
}

base::EndPoint get_master_addr() {
    return s_master_addr;
}

}
