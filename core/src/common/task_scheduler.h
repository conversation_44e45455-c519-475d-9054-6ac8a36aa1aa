/**
 * MOCHOW - A Vector Database System.
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON> (f<PERSON><PERSON><PERSON><PERSON>@baidu.com)
 * Date: 2023/10/20
 */

#pragma once

#include "core/src/common/bucketed_thread_pool.h"

namespace mochow::common {

class TaskScheduler {
public:
    TaskScheduler(const std::string& name, uint64_t worker_count, uint64_t bucket_count)
            : _name(name + ".threadpool"), _worker_count(worker_count)
            , _bucket_count(bucket_count) {}

    TaskScheduler(const std::string& name) : _name(name + ".threadpool")
            , _worker_count(std::thread::hardware_concurrency())
            , _bucket_count(std::thread::hardware_concurrency()) {}

    ~TaskScheduler() = default;
    
    NODISCARD int submit_task(baidu::sfl::IThreadpool::Task task,
                              common::BucketedThreadpool::TaskOption* options);

private:
    void initialize_threadpool();

private:
    static constexpr uint64_t DEFAULT_WORKER_COUNT = 1;
    static constexpr uint64_t DEFAULT_BUCKET_COUNT = 10;

    const std::string _name;
    uint64_t _worker_count = DEFAULT_WORKER_COUNT;
    uint64_t _bucket_count = DEFAULT_BUCKET_COUNT;
    std::unique_ptr<common::BucketedThreadpool> _threadpool;
    std::once_flag _once_flag;
};

using TaskSchedulerRef = std::shared_ptr<TaskScheduler>;

}
