#include "core/src/common/logging.h"
#include "core/src/common/openssl_aes.h"

namespace mochow::common {

int OpenSSLAES::aes_encrypt(const std::string& plain_text,
                            const std::string& key,
                            std::string* cipher_text, 
                            const EncryptMode enc_mode,
                            const std::string& iv) {
    if (cipher_text == nullptr) {
        return -1;
    }
    if (plain_text.empty()) {
        *cipher_text = "";
        return 0;
    }
    
    // check and set encrypt or decrypt key and iv
    std::string temp_key = key.c_str();
    std::string temp_iv = iv.c_str();
    AES_KEY aes_key;

    if (set_key_and_iv(AES_ENCRYPT, &temp_key, &temp_iv, &aes_key, enc_mode) < 0) {
        LOG(WARNING) << "Fail to aes encrypt due to set key and iv failed";
        return -1;
    }
    
    std::string data_bak = plain_text;
    int pad_num = AES_BLOCK_SIZE - data_bak.size() % AES_BLOCK_SIZE;
    for (int i = 0; i < pad_num; ++i) {
        data_bak += pad_num;
    }

    return encrypt_or_decrypt(data_bak, AES_ENCRYPT, enc_mode, aes_key, iv, cipher_text);
}

int OpenSSLAES::aes_decrypt(const std::string& cipher_text,
                            const std::string& key,
                            std::string* plain_text, 
                            const EncryptMode enc_mode,
                            const std::string& iv) {
    if (plain_text == nullptr) {
        return -1;
    }
    if (cipher_text.empty()) {
        *plain_text = "";
        return 0;
    }
    
    // check and set encrypt or decrypt key and iv
    std::string temp_key = key;
    std::string temp_iv = iv;
    AES_KEY aes_key;

    if (set_key_and_iv(AES_DECRYPT, &temp_key, &temp_iv, &aes_key, enc_mode) < 0) {
        LOG(WARNING) << "Fail to aes decrypt due to set key and iv failed";
        return -1;
    }

    return encrypt_or_decrypt(cipher_text, AES_DECRYPT, enc_mode, aes_key, iv, plain_text);
}

void OpenSSLAES::check_and_pad(const int pad_type, const int enc, std::string* str) {
    int len = (pad_type == 1 ? _key_len : 16);
    std::string default_str = (pad_type == 1 ? _default_key : _default_iv);

    if (str->empty()) {
        *str = default_str.substr(0, len);
    }
    // truncate or pad
    if (str->size() != len) {
        int pad_num = len - str->size();
        if (pad_num < 0) {
            *str = str->substr(0, len);
        } else {
            str->append(default_str.substr(0, pad_num)); 
        } 
        LOG(TRACE) << "Length of the key or iv for aes is not aligned, requiring pad or truncate,"
                     << " pad_or_truncate_num:" << pad_num
                     << " pad_type(1:key 2:iv):" << pad_type
                     << " enc(1:encrypt 0:decrypt):" << enc;
    }
}

int OpenSSLAES::set_key_and_iv(const int enc, std::string* key_str, std::string* iv_str, 
                               AES_KEY* aes_key, const EncryptMode enc_mode) {
    // check and pad key
    check_and_pad(1, enc, key_str);

    // check and pad iv
    if (enc_mode == EncryptMode::CBC) {
        check_and_pad(2, enc, iv_str);
    }
    
    // set key
    assert(enc == AES_ENCRYPT || enc == AES_DECRYPT);
    if (enc == AES_ENCRYPT) {
        return AES_set_encrypt_key((unsigned char*)key_str->c_str(), _key_len * 8, aes_key);
    } /* else if (enc == AES_DECRYPT) */ {
        return AES_set_decrypt_key((unsigned char*)key_str->c_str(), _key_len * 8, aes_key);
    }
}

int OpenSSLAES::encrypt_or_decrypt(const std::string& in_data, const int enc, const EncryptMode enc_mode, 
                                   const AES_KEY aes_key, const std::string& iv, std::string* out_data) {
    out_data->clear();
    int en_block_num = ceil((double)in_data.size() / AES_BLOCK_SIZE);

    for (int i = 0; i < en_block_num; ++i) {
        std::string tt_iv = iv.c_str();
        std::string str16 = in_data.substr(i * AES_BLOCK_SIZE, AES_BLOCK_SIZE);
        unsigned char out[AES_BLOCK_SIZE];
        ::memset(out, 0, AES_BLOCK_SIZE);

        switch(enc_mode) {
            case EncryptMode::CBC:
                AES_cbc_encrypt((unsigned char*)str16.c_str(), out, AES_BLOCK_SIZE, &aes_key, 
                                (unsigned char*)tt_iv.c_str(), enc);
                break;
            case EncryptMode::ECB:
                AES_ecb_encrypt((unsigned char*)str16.c_str(), out, &aes_key, enc);
                break;      
            default:
                LOG(WARNING) << "Fail to aes encrypt or decrypt due to unknown encrypt mode " << enc_mode;
                return -1;
        }
        
        *out_data += std::string((const char*)out, AES_BLOCK_SIZE);
    }
    if (enc == AES_DECRYPT) {
        int raw_data_end_pos = out_data->size() - (int)out_data->back();
        if (raw_data_end_pos <= 0) {
            LOG(WARNING) << "Fail to aes decrypt due to decrypt failed or pad character error,"
                         << " out_data_size:" << out_data->size()
                         << " pad_char:" << (int)out_data->back();
            return -1;
        }

        out_data->erase(raw_data_end_pos);
    }
    return 0;
}

int base64_encode(const std::string& in_str, std::string* out_str) {
    if (out_str == nullptr) {
        return -1;
    }
    out_str->clear();
    if (in_str.empty()) {
        return 0;
    }

    size_t out_len = (int)(ceil((double)in_str.size() / 3) * 4);
    out_str->resize(out_len);
    int res = EVP_EncodeBlock((unsigned char*)out_str->c_str(), 
                          (const unsigned char*)in_str.c_str(), in_str.size());
    if (res < 0) {
        LOG(WARNING) << "Fail to encode base64 by EVP_EncodeBlock";
        return -1;
    }
    return res;
}

int base64_decode(const std::string& in_str, std::string* out_str) {
    if (out_str == nullptr) {
        return -1;
    }
    out_str->clear();
    if (in_str.empty()) {
        return 0;
    }

    if (in_str.size() % 4 != 0) {
        LOG(WARNING) << "Fail to decode base64, input string maybe not a base64 string " << in_str;
        return -1;
    }

    out_str->resize(in_str.size());
    int res = EVP_DecodeBlock((unsigned char*)out_str->c_str(),
                          (const unsigned char*)in_str.c_str(), in_str.size());
    if (res < 0) {
        LOG(WARNING) << "Fail to decode base64 by EVP_DecodeBlock";
        return -1;
    }

    size_t out_len = in_str.size() * 3 / 4;
    // remove padding '='
    size_t pos = in_str.find_first_of("=");
    size_t truncate_num = (pos != std::string::npos) ? in_str.size() - pos : 0;
    out_str->erase(out_len - truncate_num);
    
    return out_str->size();
}

}
