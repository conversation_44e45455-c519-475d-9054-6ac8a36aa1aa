/**
 * MOCHOW - A Vector Database System.
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON> (<EMAIL>)
 * Date: 2023/09/19
 * Desciption: Declarations for Config Reloader
 *
 */

#pragma once

#include <string>
#include <unordered_map>
#include <functional>
#include <base/endpoint.h>
#include <gflags/gflags.h>
#include "core/src/common/butex.h"

namespace mochow::common {

class ConfigReloader {
public:
    using Handle = int32_t;
    using Callback = std::function<void(void)>;
    using Filter = std::function<bool(const std::string&, const std::string&)>;

    ConfigReloader(const std::string& file_path) : _file_path(file_path) { }
    ConfigReloader(const std::string& file_path, const std::string& token) : 
        _file_path(file_path), _token(token){ }
    ~ConfigReloader() = default;

    bool verify_token(const std::string& token) {
        return _token == token;
    }

    bool reload();
    bool update_config_file(const std::string& conf_key, const std::string& conf_value);

    Handle add_callback(Callback&& callback);
    bool remove_callback(Handle id);

    Handle add_filter(Filter&& filter);
    bool remove_filter(Handle id);

private:
    bool gen_new_line(const std::string& line, const std::string& conf_key,
            const std::string& conf_value, std::string* new_line);
    std::string gen_new_conf(const std::string& conf_data, const std::string& conf_key,
            const std::string& conf_value);
    bool reload_config_file();
    void reload_line(const std::string& key, const std::string& value);
    void notify_all(); // execute all callbacks
    bool is_valid_conf(const std::string& conf_key, const std::string& conf_value, Handle* filter_id);

private:
    std::string                         _file_path;
    std::string                         _token;
    common::MutexLock                   _mutex;
    std::unordered_map<Handle, Callback> _callbacks;
    std::unordered_map<Handle, Filter>  _valid_filters; // filter invalid k-v
};

extern ConfigReloader* g_config_reloader;

}
