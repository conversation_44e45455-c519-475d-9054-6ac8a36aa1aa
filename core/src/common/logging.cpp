/***************************************************************************
 *
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

#include <base/comlog_sink.h>

#include "core/src/common/logging.h"
#include "baidu/rpc/reloadable_flags.h"

namespace mochow::common {

DEFINE_bool(print_debug_log, true, "switch for print debug log");
BAIDU_RPC_VALIDATE_GFLAG(print_debug_log, baidu::rpc::PassValidate);

// FLAGS_verbose, VLOG level < vlog_level will print
// FLAGS_min_log_level, LOG level > min_log_level will print
//                      (0=TRACE 1=NOTICE 2=WARNING 3=ERROR 4=FATAL)
//                      Take care: DEBUG is just TRACE (designed by baidu-rpc's log system)
DEFINE_string(comlog_path, "log", "comlog path");
DEFINE_string(comlog_process, "", "comlog process name, use argv[0] if empty");
DEFINE_int32(comlog_split_type, 1, "comlog split type: "\
             "0(TRUNCT), 1(SIZECUT, split per 2048mb), 2(DATECUT, split per 60min)");
DEFINE_int32(comlog_max_log_length, 4096, "comlog max log length, "\
             "logs longer than this value are truncated.");
DEFINE_int32(comlog_cut_block_size, 1024, "Move existing logs into a separate file suffixed with datetime until log file size beyond cut_block_size(MB)."\
            "Default: 2048");
DEFINE_int32(comlog_quota_size, 2048, "Remove oldest cutoff log files when they exceed so many megabytes(roughly) MB. "\
            "If value is 0, never remove log files.");
DEFINE_int32(comlog_quota_day, 0, "comlog quota day in min, "\
             "use when split_type is DATECUT, > 0 valid. split per 60min defaultly");
DEFINE_int32(comlog_quota_hour, 0, "comlog quota hour in min, "\
             "use when split_type is DATECUT, > 0 valid. split per 60min defaultly");
DEFINE_bool(comlog_enable_async, true, "comlog enable AFILE");
DEFINE_bool(comlog_enable_wf, true, "comlog enable wf");

int init_comlog() {
    logging::ComlogSinkOptions options;
    options.async = FLAGS_comlog_enable_async;
    options.shorter_log_level = false;
    options.log_dir = FLAGS_comlog_path;
    options.process_name = FLAGS_comlog_process;
    options.print_vlog_as_warning = false;
    options.split_type = (logging::ComlogSplitType)FLAGS_comlog_split_type;
    options.max_log_length = FLAGS_comlog_max_log_length;
    options.cut_size_megabytes = FLAGS_comlog_cut_block_size;
    options.quota_size = FLAGS_comlog_quota_size;
    options.quota_day = FLAGS_comlog_quota_day;
    options.quota_hour = FLAGS_comlog_quota_hour;
    options.enable_wf_device = FLAGS_comlog_enable_wf;

    if (logging::ComlogSink::GetInstance()->Setup(&options) != 0) {
        LOG(ERROR) << "Fail to init comlog";
        return -1;
    }
    logging::SetLogSink(logging::ComlogSink::GetInstance());

    return 0;
}
}
