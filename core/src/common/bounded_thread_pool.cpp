/**
 * MOCHOW - A Vector Database System.
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON> (<PERSON><PERSON><PERSON><PERSON>@baidu.com)
 * Description: bounded thread pool implemented by bthread condition variable.
 */

#include <sys/resource.h>
#include "core/src/common/trace_log.h"
#include "core/src/common/bounded_thread_pool.h"
#include "baidu/vdb/mochow/core/src/proto/error_code.pb.h"

namespace mochow::common {

BoundedThreadpool::~BoundedThreadpool() {
    _task_queue->close();
    stop();
}

int BoundedThreadpool::start(const IStartOption* const option) {
    const StartOption* const start_option = dynamic_cast<const StartOption*>(option);
    if (start_option == nullptr) {
        return ERR_INVALID_ARGUMENT;
    }

    {
        const common::ScopedButex guard(_threadpool_lock);
        if (_state == State::RUNNING) {
            return ERR_UNEXPECTED;
        }

        if (_state == State::STOPPED) {
            return ERR_UNEXPECTED;
        }

        LOG_AND_ASSERT(_state == State::UNINITIALIZED);

        size_t max_queue_size = start_option->get_max_queue_size();
        _task_queue = std::make_unique<ConcurrentBoundedQueue<TaskQueueItem>>(max_queue_size);

        size_t worker_count = start_option->get_worker_count();

        // After we create the following worker threads, they will start execution
        // immediately. But they have to acquire _threadpool_lock before they can
        // do anything. Because we still hold the mutex, these worker threads will
        // be blocked until we finish the initialization work.
        for (uint64_t i = 0; i < worker_count; ++i) {
            LOG(NOTICE) << "Threadpool:" << _name << " start normal worker:" << i;
            _workers.emplace_back(&BoundedThreadpool::execute_task, this);
        }

        _state = State::RUNNING;
    }

    return OK;
}

void BoundedThreadpool::stop() {
    const common::ScopedButex guard1(_threadpool_stop_lock);

    {
        const common::ScopedButex guard2(_threadpool_lock);

        if (_state == State::UNINITIALIZED) {
            _state = State::STOPPED;
            return;
        }

        if (_state == State::STOPPED) {
            return;
        }

        LOG_AND_ASSERT(_state == State::RUNNING);

        _state = State::STOPPED;

        _task_queue->close();
    }

    for (auto& worker : _workers) {
        worker.join();
    }
}

int BoundedThreadpool::submit(Task task, const ITaskOption* const option) {
    if (task == nullptr) {
        return ERR_INVALID_ARGUMENT;
    }

    TaskOption task_option("");
    const TaskOption* actual_task_option = &task_option;

    if (option != nullptr) {
        actual_task_option = dynamic_cast<const TaskOption*>(option);
        if (actual_task_option == nullptr) {
            return ERR_INVALID_ARGUMENT;
        }
    }

    if (_state == State::UNINITIALIZED) {
        return ERR_UNINITIALIZED;
    }

    if (_state == State::STOPPED) {
        return ERR_UNEXPECTED;
    }

    LOG_AND_ASSERT(_state == State::RUNNING);

    const auto [log_id, trace_id] = GET_CURRENT_TRACE_ID();
    _task_queue->push(TaskQueueItem{
            std::move(task), actual_task_option->name(), common::TimeStamp::now(), log_id, trace_id});

    return OK;
}

void BoundedThreadpool::execute_task() {
    // get lock first to waiting for ctor end.
    {
        const common::ScopedButex guard(_threadpool_lock);
        if (setpriority(PRIO_PROCESS, gettid(), 19) != 0) {
            LOG(ERROR) << "Fail to set thread priority, error: " << std::strerror(errno);
        }
    }

    while (_state == State::RUNNING) {
        // this task must be defined inside this loop, since this task may
        // hold shared pointers and may increase the ref count, which should
        // not happen after the task is popped.
        // if we don't set it to nullptr here and the task queue is empty,
        // this thread will be blocked in pop() and this thread will hold
        // task forever. this may affect tablet garbage collection, since
        // tablet gc status check relies on ref count.
        TaskQueueItem queue_item{Task{}, nullptr, TimeStamp::invalid_time(), 0, 0};
        bool b = _task_queue->pop(queue_item);
        if (b) {
            const common::TimeStamp start_execute_time(common::TimeStamp::now());

            int64_t enqueue_latency_us = start_execute_time - queue_item.enqueue_time;
            _bvar_recorder.add_enqueue_latency(enqueue_latency_us);

            SET_SCOPED_TASK_TRACE_ID(queue_item.log_id, queue_item.trace_id);

            queue_item.task(_state == State::STOPPED);  // Run the task.

            int64_t execute_latency_us = common::TimeStamp::now() - start_execute_time;
            _bvar_recorder.add_execute_latency(execute_latency_us);

            if (queue_item.name != nullptr) {
                MOCHOW_TRACE_LOG(NOTICE) << _name << " finished task,"
                                     << " task_name:" << queue_item.name
                                     << " enqueue_us:" << enqueue_latency_us
                                     << " execute_us:" << execute_latency_us;
            }
        } else {
            break;
        }
    }
}

}   // namespace mochow::common
