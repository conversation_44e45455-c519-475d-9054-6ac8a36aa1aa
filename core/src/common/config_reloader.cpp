/**
 * MOCHOW - A Vector Database System.
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON> (<EMAIL>)
 * Date: 2023/09/19
 * Desciption: Implementations for Config Reloader
 *
 */

#include <sstream>
#include <time.h>
#include <stdio.h>
#include <stdlib.h>
#include <base/logging.h>
#include <base/fd_guard.h>
#include <base/file_util.h>
#include <baidu/rpc/server.h>
#include "core/src/common/common.h"
#include "core/src/common/config_reloader.h"

namespace mochow::common {

ConfigReloader* g_config_reloader;

bool ConfigReloader::reload() {
    if (!reload_config_file()) {
        LOG(ERROR) << "Fail to reload config file, file_path:" << _file_path;
        return false;
    }
    notify_all();
    return true;
}

void ConfigReloader::reload_line(const std::string& key, const std::string& value) {
    google::CommandLineFlagInfo info;
    auto ok = GetCommandLineFlagInfo(key.data(), &info);
    if (!ok) {
        return;
    }
    if (info.type == "string" && info.description.find("reloadable") == std::string::npos) {
        LOG(TRACE) << "will not reload string flag:" << key;
        return;
    }
    google::SetCommandLineOption(key.data(), value.data());
}

bool ConfigReloader::reload_config_file() {
    FILE *fp = fopen(_file_path.c_str(), "r");
    if (fp == NULL) {
        LOG(ERROR) << "Fail to open config file, file_path:" << _file_path << " errno:" << errno;
        return false;
    }

    while (true) {
        char *line = NULL;
        size_t len = 0;
        int ret = getline(&line, &len, fp);
        // check eof or error
        if (ret == -1) {
            free(line);
            if (feof(fp)) {
                fclose(fp);
                return true;
            } else {
                LOG(ERROR) << "Fail to read config file, file_path:" << _file_path << " errno:" << errno;
                fclose(fp);
                return false;
            }
        }
        // check empty
        len = strlen(line);
        if (len == 0) {
            free(line);
            continue;
        }
        char * end = strchr(line, '\n');
        if (end != NULL) {
            *end = '\0';
            len = end - line;
        }
        // trim head
        char *start = line;
        for (; *start == ' '; ++start) {}
        std::string str(start, (line + len) - start);
        if (str.empty()) {
            free(line);
            continue;
        }
        // filter the comment line
        size_t pos = str.find_first_of('#');
        str = str.substr(0, pos);
        if (str.empty()) {
            free(line);
            continue;
        }
        // trim tail
        pos = str.find_first_of(' ');
        str = str.substr(0, pos);
        if (str.empty()) {
            LOG(WARNING) << "Reload config file: skip invalid line from " << _file_path
                         << " line:" << line;
            free(line);
            continue;
        }
        // parse
        pos = str.find_first_of("--");
        if (pos != 0) {
            LOG(WARNING) << "Reload config file: skip invalid line from " << _file_path
                         << " line:" << line;
            free(line);
            continue;
        }
        pos += 2;
        size_t eq = str.find_first_of('=');
        if (eq == std::string::npos) {
            LOG(WARNING) << "Reload config file: skip invalid line from " << _file_path
                         << " line:" << line;
            free(line);
            continue;
        }
        // ectract key
        std::string key = str.substr(pos, eq - pos);
        if (key.empty()) {
            LOG(WARNING) << "Reload config file: skip invalid line from " << _file_path
                         << " line:" << line;
            free(line);
            continue;
        }
        // extract value
        eq += 1;
        std::string value = str.substr(eq);
        if (value.empty()) {
            LOG(WARNING) << "Reload config file: skip invalid line from " << _file_path
                         << " line:" << line;
            free(line);
            continue;
        }
        // filter
        Handle filter_id = -1;
        if (!is_valid_conf(key, value, &filter_id)) {
            LOG(ERROR) << "Fail to reload config file due to invalid conf, key:" << key
                       << " value:" << value << " filter_id:" << filter_id;
            return false;
        }
        reload_line(key, value);
        free(line);
    }
    return true;
}

bool ConfigReloader::gen_new_line(const std::string& _line, const std::string& conf_key,
            const std::string& conf_value, std::string* new_line) {
    auto pos = _line.find_first_not_of(' ');
    std::string line = (pos != std::string::npos ? _line.substr(pos) : std::string(""));
    std::string prefixes[2];
    prefixes[0] = "--" + conf_key + "=";
    prefixes[1] = "-"  + conf_key + "=";

    for (auto & prefix : prefixes) {
        if (line.size() >= prefix.size()
                && ::strncmp(line.c_str(), prefix.c_str(), prefix.size()) == 0) {
            line.replace(prefix.size(), line.size() - prefix.size(), conf_value);
            *new_line = line;
            LOG(WARNING) << "Reload config file: match conf line, old_line:" << _line
                         << " new_line:" << *new_line;
            return true;
        }
    }

    *new_line = _line;
    return false;
}

std::string ConfigReloader::gen_new_conf(const std::string& conf_data,
                        const std::string& conf_key, const std::string& conf_value) {
    std::string line;
    std::string new_conf;
    bool find_out = false;
    for (unsigned int i = 0; ; ++i) {
        if (i == conf_data.size()) {
            std::string new_line;
            find_out |= gen_new_line(line, conf_key, conf_value, &new_line);
            new_conf += new_line;
            break;
        }
        const auto& c = conf_data[i];
        if (c == '\n') {
            std::string new_line;
            find_out |= gen_new_line(line, conf_key, conf_value, &new_line);
            new_conf += new_line + c;
            line.clear();
        } else {
            line += c;
        }
    }
    if (!find_out) {
        new_conf += "\n--" + conf_key + "=" + conf_value;
        LOG(NOTICE) << "Reload config file: conf key not found, add conf key into the end of config file,"
                    << " conf_key:" << conf_key << " conf_value:" << conf_value;
    }
    return new_conf;
}

bool ConfigReloader::update_config_file(const std::string& conf_key,
                                        const std::string& conf_value) {
    static common::MutexLock mutex;
    common::ScopedMutexLock lock(mutex);

    Handle filter_id = -1;
    if (!is_valid_conf(conf_key, conf_value, &filter_id)) {
        LOG(ERROR) << "Fail to update config file due to invalid conf,"
                   << " key:" << conf_key << " value:" << conf_value
                   << " filter_id:" << filter_id;
        return false;
    }

    std::string conf_data;
    if (!base::ReadFileToString(base::FilePath(_file_path), &conf_data)) {
        LOG(ERROR) << "Fail to update config file due to read config file failed,"
                   << " file_path:" << _file_path;
        return false;
    }

    std::string new_conf = std::move(gen_new_conf(conf_data, conf_key, conf_value));

    // dump new conf
    std::ostringstream oss;
    oss << _file_path << ".temp." << base::gettimeofday_us();
    std::string temp_file = oss.str();
    // generate temp file
    {
        base::fd_guard fd(::open(temp_file.c_str(), O_WRONLY | O_CREAT | O_TRUNC, 0644));
        if (fd < 0) {
            LOG(ERROR) << "Fail to update config file due to open temp config file failed,"
                       << " temp_file_path:" << temp_file << " errno:" << errno;
            return false;
        }

        int64_t size = new_conf.size();
        if (::write(fd, new_conf.data(), size) != size) {
            LOG(ERROR) << "Fail to update config file due to write temp config file failed,"
                       << " temp_file_path:" << temp_file << " errno:" << errno;
            return false;
        }
    }

    // backup old conf file
    time_t now = time(nullptr);
    struct tm tp;
    localtime_r(&now, &tp);
    char buf[15];
    memset(buf, 0, 15);
    snprintf(buf, 15, "%4d%02d%02d%02d%02d%02d",
            tp.tm_year+1900, tp.tm_mon+1, tp.tm_mday, tp.tm_hour, tp.tm_min, tp.tm_sec);
    std::string backup_file_path = _file_path + '.' + std::string(buf);
    if (!base::CopyFile(base::FilePath(_file_path), base::FilePath(backup_file_path))) {
        LOG(ERROR) << "Fail to update config file due to backup config file failed,"
                   << " file_path:" << _file_path << " backup_file_path:" << backup_file_path;
    }

    // rename temp file to normal conf
    if (::rename(temp_file.c_str(), _file_path.c_str())) {
        LOG(ERROR) << "Fail to update config file due to rename temp file to normal file error,"
                   << " old_file_path:" << temp_file << " new_file_path:" << _file_path
                   << " errno:" << errno;
        return false;
    }

    LOG(WARNING) << "Succeed to update config file";
    return reload();
}

void ConfigReloader::notify_all() {
    for (const auto& pair : _callbacks) {
        pair.second();
    }
}

ConfigReloader::Handle ConfigReloader::add_callback(Callback&& callback) {
    static Handle id = 1;
    common::ScopedMutexLock lock(_mutex);
    _callbacks.emplace(id, std::move(callback));
    return id++;
}

bool ConfigReloader::remove_callback(Handle id) {
    common::ScopedMutexLock lock(_mutex);

    auto it = _callbacks.find(id);
    if (it == _callbacks.end()) {
        return false;
    }

    _callbacks.erase(it);
    return true;
}

ConfigReloader::Handle ConfigReloader::add_filter(Filter&& filter) {
    static Handle id = 1;
    common::ScopedMutexLock lock(_mutex);
    _valid_filters.emplace(id, std::move(filter));
    return id++;
}

bool ConfigReloader::remove_filter(Handle id) {
    common::ScopedMutexLock lock(_mutex);
    auto it = _valid_filters.find(id);
    if (it == _valid_filters.end()) {
        return false;
    }

    _valid_filters.erase(it);
    return true;
}

bool ConfigReloader::is_valid_conf(const std::string& conf_key,
                const std::string& conf_value, Handle* filter_id) {
    for (auto& [id, is_valid] : _valid_filters) {
        if (!is_valid(conf_key, conf_value)) {
            if (filter_id != nullptr) {
                *filter_id = id;
            }
            return false;
        }
    }
    return true;
}

}
