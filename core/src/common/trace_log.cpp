#include <memory>
#include <mutex>
#include <bthread.h>
#include "base/bthread/bthread_types.h"
#include "base/bthread/bthread_unstable.h"
#include "core/src/common/likely.h"
#include "sfl/logging.h"
#include "trace_log.h"

namespace mochow {

namespace {
std::once_flag create_key_once;
bthread_key_t bthread_key = INVALID_BTHREAD_KEY;
std::unique_ptr<bthread_keytable_pool_t> keytable_pool;

struct TaskInternal {
    trace_internal::TraceContext* ctx;
};

void init_trace_util() {
    int err = bthread_key_create(&bthread_key, [](void* p) { delete static_cast<TaskInternal*>(p); });
    unlikely_if(err != 0) {
        LOG(FATAL) << "Fail to execute `bthread_key_create`";
        return;
    }

    keytable_pool = std::make_unique<bthread_keytable_pool_t>();
    unlikely_if(bthread_keytable_pool_init(keytable_pool.get()) != 0) {
        LOG(FATAL) << "Fail to init keytable_pool";
        keytable_pool.reset();
    }

    LOG(NOTICE) << "Created bthread key for trace log, bthread_key:" << bthread_key
                << " keytable_pool:" << static_cast<void*>(keytable_pool.get());
}
}  // namespace

namespace trace_internal {

TraceContext::~TraceContext() {
    TraceContext* ctx = CurrTask::get();
    unlikely_if(ctx != this) {
        // should never happen
        LOG(FATAL) << "Found unexpected two TraceContext exist at the same time,"
                   << " curr:" << (void*)this
                   << " curr_log_id:" << this->log_id << " curr_trace_id:" << this->trace_id
                   << " prev:" << (void*)ctx << noflush;
        if (ctx != nullptr) {
            LOG(FATAL) << " prev_log_id:" << ctx->log_id << " prev_trace_id:" << ctx->trace_id << noflush;
        }
        LOG(FATAL) << "";
    }

    CurrTask::reset(nullptr);
}

TraceContext* CurrTask::get() {
    std::call_once(create_key_once, [] { init_trace_util(); });

    auto* task = static_cast<TaskInternal*>(bthread_getspecific(bthread_key));
    if (task == nullptr) {
        return nullptr;
    }
    return task->ctx;
}

void CurrTask::reset(TraceContext* ctx) {
    std::call_once(create_key_once, [] { init_trace_util(); });

    TaskInternal* existing_task = static_cast<TaskInternal*>(bthread_getspecific(bthread_key));

    // Reuse `existing_task` to avoid memory leak. https://github.com/apache/brpc/issues/1449
    if (existing_task != nullptr) {
        existing_task->ctx = ctx;
        return;
    }

    if (ctx == nullptr) {
        return;
    }

    TaskInternal* new_task = new TaskInternal();
    new_task->ctx = ctx;
    int err = bthread_setspecific(bthread_key, new_task);
    LOG_AND_ASSERT(err == 0);
}

}  // namespace trace_internal

void init_bthread_attr_for_trace(bthread_attr_t* attr) {
    attr->keytable_pool = keytable_pool.get();
}
}  // namespace mochow
