/**
 * MOCHOW - A Vector Database System.
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON><PERSON><PERSON><PERSON> (f<PERSON><PERSON><PERSON><PERSON>@baidu.com)
 * Date: 2023/10/20
 */

#pragma once

#include <array>
#include <atomic>
#include <cstdint>
#include <map>
#include <memory>
#include <mutex>
#include <queue>
#include <string>
#include <thread>
#include <utility>
#include <vector>

#include "bthread/countdown_event.h"

#include "core/src/common/using.h"
#include "sfl/attributes.h"
#include "sfl/bresult.h"
#include "sfl/ithreadpool.h"
#include "sfl/return.h"
#include "sfl/ticktock.h"
#include "sfl/event.h"

#include "core/src/common/ithreadpool.h"
#include "core/src/common/butex.h"
#include "core/src/common/trace_log.h"

namespace mochow::common {

class BucketedThreadpool : public IThreadpool {
public:
    class StartOption;

    class TaskOption;

    explicit BucketedThreadpool(const std::string& name);

    ~BucketedThreadpool() override;

    DISABLE_COPY_AND_MOVE(BucketedThreadpool)

    NODISCARD int start(const IStartOption* const option) override;

    void stop() override;

    NODISCARD int submit(Task task, const ITaskOption* const option) override;

    NODISCARD uint64_t get_pending_task_count() const override {
        return _pending_task_count;
    }

    NODISCARD uint64_t get_max_task_execution_time_us() const override;

private:
    class InternalTask;

    using InternalTaskPtr = std::shared_ptr<InternalTask>;

    InternalTaskPtr get_task_from_one_queue(uint64_t bucket_index, const Priority priority);

    InternalTaskPtr get_task_from_all_queues(uint64_t bucket_index);

    InternalTaskPtr get_task(const bool realtime_task);

    void execute_task(const bool realtime_task);

private:
    enum class State : uint8_t {
        UNINITIALIZED = 0,
        RUNNING       = 1,
        STOPPED       = 2
    };

    const std::string _name;

    std::atomic<State> _state;

    std::array<double, 2> _probability_thresholds;

    std::atomic<uint64_t> _pending_task_count;

    uint64_t _bucket_count;
    
    std::vector<uint64_t> _bucket_pending_task;

    std::vector<std::array<std::queue<InternalTaskPtr>, 5>> _task_queues;

    std::map<std::thread::id, std::atomic<uint64_t>> _start_times;

    std::vector<std::thread> _normal_workers;

    std::vector<std::thread> _realtime_workers;

    mutable baidu::sfl::Event _all_task_event;

    mutable baidu::sfl::Event _realtime_task_event;

    // This mutex is to protect _state and _task_queues, and it is used in
    // the start function to block worker threads until start finishes.
    mutable common::Butex _threadpool_lock;

    // This mutex is to enforce sequential execution of the stop function.
    mutable common::Butex _threadpool_stop_lock;
};

class BucketedThreadpool::StartOption final : public IStartOption {
public:
    StartOption() = default;

    StartOption(const uint64_t normal_thread_count,
                const uint64_t realtime_thread_count,
                const uint64_t bucket_count) :
        _normal_thread_count(normal_thread_count),
        _realtime_thread_count(realtime_thread_count),
        _bucket_count(bucket_count) {}
    
    StartOption(const uint64_t normal_thread_count,
                const uint64_t realtime_thread_count,
                const double high_priority_weight,
                const double medium_priority_weight,
                const uint64_t bucket_count) :
        _normal_thread_count(normal_thread_count),
        _realtime_thread_count(realtime_thread_count),
        _high_priority_weight(high_priority_weight),
        _medium_priority_weight(medium_priority_weight),
        _bucket_count(bucket_count) {}

    StartOption(const StartOption& other) :
        IStartOption(),
        _normal_thread_count(other._normal_thread_count),
        _realtime_thread_count(other._realtime_thread_count),
        _high_priority_weight(other._high_priority_weight),
        _medium_priority_weight(other._medium_priority_weight),
        _bucket_count(other._bucket_count) {}

    StartOption(StartOption&& other) noexcept :
        IStartOption(),
        _normal_thread_count(std::move(other._normal_thread_count)),
        _realtime_thread_count(std::move(other._realtime_thread_count)),
        _high_priority_weight(std::move(other._high_priority_weight)),
        _medium_priority_weight(std::move(other._medium_priority_weight)),
        _bucket_count(other._bucket_count) {}

    StartOption& operator=(const StartOption& other) {
        if (this == &other) {
            return *this;
        }

        _normal_thread_count = other._normal_thread_count;
        _realtime_thread_count = other._realtime_thread_count;
        _high_priority_weight = other._high_priority_weight;
        _medium_priority_weight = other._medium_priority_weight;
        _bucket_count = other._bucket_count;

        return *this;
    }

    StartOption& operator=(StartOption&& other) noexcept {
        if (this == &other) {
            return *this;
        }

        _normal_thread_count = std::move(other._normal_thread_count);
        _realtime_thread_count = std::move(other._realtime_thread_count);
        _high_priority_weight = std::move(other._high_priority_weight);
        _medium_priority_weight = std::move(other._medium_priority_weight);
        _bucket_count = std::move(other._bucket_count);

        return *this;
    }

    NODISCARD uint64_t get_normal_thread_count() const {
        return _normal_thread_count;
    }

    void set_normal_thread_count(const uint64_t normal_thread_count) {
        _normal_thread_count = normal_thread_count;
    }

    NODISCARD uint64_t get_realtime_thread_count() const {
        return _realtime_thread_count;
    }

    void set_realtime_thread_count(const uint64_t realtime_thread_count) {
        _realtime_thread_count = realtime_thread_count;
    }

    NODISCARD uint64_t get_bucket_count() const {
        return _bucket_count;
    }

    void set_bucket_count(const uint64_t bucket_count) {
        _bucket_count = bucket_count;
    }

    void get_priority_weight(double& high_priority_weight,
                             double& medium_priority_weight) const {
        high_priority_weight = _high_priority_weight;
        medium_priority_weight = _medium_priority_weight;
    }

    void set_priority_weight(const double high_priority_weight,
                             const double medium_priority_weight) {
        _high_priority_weight = high_priority_weight;
        _medium_priority_weight = medium_priority_weight;
    }

private:
    static constexpr uint64_t DEFAULT_NORMAL_THREAD_COUNT = 1;

    static constexpr uint64_t DEFAULT_REALTIME_THREAD_COUNT = 0;
    
    static constexpr uint64_t DEFAULT_BUCKET_COUNT = 1;

    static constexpr double DEFAULT_HIGH_PRIORITY_WEIGHT = 0.5;

    static constexpr double DEFAULT_MEDIUM_PRIORITY_WEIGHT = 0.3;

    uint64_t _normal_thread_count = DEFAULT_NORMAL_THREAD_COUNT;

    uint64_t _realtime_thread_count = DEFAULT_REALTIME_THREAD_COUNT;

    double _high_priority_weight = DEFAULT_HIGH_PRIORITY_WEIGHT;

    double _medium_priority_weight = DEFAULT_MEDIUM_PRIORITY_WEIGHT;

    uint64_t _bucket_count = DEFAULT_BUCKET_COUNT;
};

VERIFY_NOTHROW_MOVABLE(BucketedThreadpool::StartOption)

class BucketedThreadpool::TaskOption final : public ITaskOption {
public:
    TaskOption() = default;

    TaskOption(uint64_t bucket_id) :
        _bucket_id(bucket_id) {}
    
    TaskOption(uint64_t bucket_id, const Priority priority) :
        _bucket_id(bucket_id),
        _priority(priority) {}
    
    TaskOption(uint64_t bucket_id, const Priority priority, 
            bthread::CountdownEvent* event) :
        _bucket_id(bucket_id),
        _priority(priority),
        _event(event) {}

    TaskOption(const TaskOption& other) :
        ITaskOption(),
        _bucket_id(other._bucket_id),
        _priority(other._priority),
        _event(other._event) {}

    TaskOption(TaskOption&& other) noexcept :
        ITaskOption(),
        _bucket_id(std::move(other._bucket_id)),
        _priority(std::move(other._priority)),
        _event(std::move(other._event)) {}

    TaskOption& operator=(const TaskOption& other) {
        if (this == &other) {
            return *this;
        }
        
        _bucket_id = other._bucket_id;
        _priority = other._priority;
        _event = other._event;

        return *this;
    }

    TaskOption& operator=(TaskOption&& other) noexcept {
        if (this == &other) {
            return *this;
        }

        _bucket_id = std::move(other._bucket_id);
        _priority = std::move(other._priority);
        _event = std::move(other._event);

        return *this;
    }

    NODISCARD Priority get_priority() const {
        return _priority;
    }

    void set_priority(const Priority priority) {
        _priority = priority;
    }
    
    NODISCARD uint64_t get_bucket_id() const {
        return _bucket_id;
    }

    void set_bucket_id(uint64_t bucket_id) {
        _bucket_id = bucket_id;
    }
    
    NODISCARD bthread::CountdownEvent* get_event() const {
        return _event;
    }

    void set_event(bthread::CountdownEvent* event) {
        _event = event;
    }

private:
    uint64_t _bucket_id = 0;
    Priority _priority = Priority::MEDIUM;
    bthread::CountdownEvent* _event = nullptr;
};

VERIFY_NOTHROW_MOVABLE(BucketedThreadpool::TaskOption)

class BucketedThreadpool::InternalTask final {
public:
    InternalTask(Task task) :
        _task(std::move(task)),
        _creation_time_us(baidu::sfl::get_current_wall_time_us()) {
        init_trace_id();
    }

    InternalTask(Task task, bthread::CountdownEvent* event) :
        _task(std::move(task)),
        _event(std::move(event)),
        _creation_time_us(baidu::sfl::get_current_wall_time_us()) {
        init_trace_id();
    }

    ~InternalTask() = default;

    DISABLE_COPY_AND_MOVE(InternalTask)

    NODISCARD uint64_t get_creation_time() const {
        return _creation_time_us;
    }

    void execute(const bool stopping) {
        SET_SCOPED_TASK_TRACE_ID(_log_id, _trace_id);
        return _task(stopping);
    }

    NODISCARD bthread::CountdownEvent* get_event() {
        return _event;
    }

private:
    void init_trace_id() {
        std::tie(_log_id, _trace_id) = GET_CURRENT_TRACE_ID();
    }

    Task _task;

    bthread::CountdownEvent* _event = nullptr;

    const uint64_t _creation_time_us;

    uint64_t _log_id;
    TRACEID _trace_id;
};

} // mochow::engine
