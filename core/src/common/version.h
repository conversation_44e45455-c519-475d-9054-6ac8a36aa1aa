/**
 * MOCHOW - A Vector Database System.
 * Copyright (c) 2024 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON> (<EMAIL>)
 * Date: 2024/03/25
 * Description: Implementations for module version
 *
 */

#pragma once

#include <atomic>
#include <memory>
#include <cctype>
#include <string>
#include <cstring>
#include <cstdlib>
#include "core/src/common/logging.h"

namespace mochow::common {

// Version in uint64:
// |--major--|--minor--|------build------|--------------revision--------------|
//     8b        8b            16b                         32b

#pragma pack(push, r1, 1)

struct Version {
    uint8_t major = 0;
    uint8_t minor = 0;
    uint16_t build = 0;
    uint32_t revision = 0;

    Version(const uint8_t _major = 0, const uint8_t _minor = 0,
            const uint16_t _build = 0, const uint32_t _revision = 0) {
        major = _major;
        minor = _minor;
        build = _build;
        revision = _revision;
    }

    Version(const uint64_t version) {
        from_uint64(version);
    }

    void from_uint64(const uint64_t version) {
        revision = static_cast<uint32_t>((version << 32) >> 32);
        build = static_cast<uint16_t>((version << 16) >> 48);
        minor = static_cast<uint8_t>((version << 8) >> 56);
        major = static_cast<uint8_t>(version >> 56);
    }

    uint64_t to_uint64() const {
        return (static_cast<uint64_t>(major) << 56)
             + (static_cast<uint64_t>(minor) << 48)
             + (static_cast<uint64_t>(build) << 32)
             + revision;
    }

    bool from_string(const std::string& version_str) {
        std::string str = version_str;
        int non_digit_chars = 0;
        for (size_t i = 0; i < str.size(); ++i) {
            if (!std::isdigit(str[i])) {
                if (str[i] != '.') {
                    return false;
                }
                str[i] = ' ';
                ++non_digit_chars;
            }
        }
        if (non_digit_chars != 3) {
            return false;
        }
        int64_t values[4];
        if (sscanf(str.c_str(), "%ld %ld %ld %ld", &values[0],
                                &values[1], &values[2], &values[3]) != 4) {
            return false;
        }
        if (values[0] < 0 || values[0] > static_cast<int64_t>(UINT8_MAX)) { return false; }
        if (values[1] < 0 || values[1] > static_cast<int64_t>(UINT8_MAX)) { return false; }
        if (values[2] < 0 || values[2] > static_cast<int64_t>(UINT16_MAX)) { return false; }
        if (values[3] < 0 || values[3] > static_cast<int64_t>(UINT32_MAX)) { return false; }
        major = static_cast<uint8_t>(values[0]);
        minor = static_cast<uint8_t>(values[1]);
        build = static_cast<uint16_t>(values[2]);
        revision = static_cast<uint32_t>(values[3]);
        return true;
    }

    std::string to_string(const char sep = '.') const {
        return std::to_string(major) + sep
                + std::to_string(minor) + sep
                + std::to_string(build) + sep
                + std::to_string(revision);
    }

    friend bool operator<(const Version& lhs, const Version& rhs) {
        return lhs.to_uint64() < rhs.to_uint64();
    }

    friend bool operator>(const Version& lhs, const Version& rhs) {
        return lhs.to_uint64() > rhs.to_uint64();
    }

    friend bool operator<=(const Version& lhs, const Version& rhs) {
        return lhs.to_uint64() <= rhs.to_uint64();
    }

    friend bool operator>=(const Version& lhs, const Version& rhs) {
        return lhs.to_uint64() >= rhs.to_uint64();
    }

    friend bool operator==(const Version& lhs, const Version& rhs) {
        return lhs.to_uint64() == rhs.to_uint64();
    }

    friend bool operator!=(const Version& lhs, const Version& rhs) {
        return lhs.to_uint64() != rhs.to_uint64();
    }
};

#pragma pack(pop, r1)

class VersionManager {
public:
    void set_master_version(const Version& version) {
        _master_version.store(version.to_uint64());
    }

    Version get_master_version() const {
        uint64_t u64v = _master_version.load();
        return Version(u64v);
    }

private:
    std::atomic<uint64_t> _master_version;
};

inline std::unique_ptr<VersionManager> g_version_manager
                                = std::make_unique<VersionManager>();

}
