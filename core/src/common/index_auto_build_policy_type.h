/**
 * MOCHOW - A Vector Database System.
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON> (<EMAIL>)
 * Date: 2024/03/21
 * Description: Definitions for Index Auto Build Policy Type
 *
 */

#pragma once

#include <string>

namespace mochow::common {

class IndexAutoBuildPolicyType final {
public:
    static constexpr const char* TIMING = "timing";
    static constexpr const char* PERIODICAL = "periodical";
    static constexpr const char* ROW_COUNT_INCREMENT = "row_count_increment";

    static inline bool is_valid_auto_build_policy_type(const std::string& type) {
        return type == TIMING || type == PERIODICAL || type == ROW_COUNT_INCREMENT;
    }

private:
    IndexAutoBuildPolicyType() = default;
    ~IndexAutoBuildPolicyType() = default;
};

}
