#pragma once

#if defined(__GNUC__) && __GNUC__ >= 4
#define LIKELY(x)       __builtin_expect((x), 1)
#define UNLIKELY(x)     __builtin_expect((x), 0)
#ifndef likely
#define likely(x)       __builtin_expect(!!(x), 1)
#endif
#ifndef unlikely
#define unlikely(x)     __builtin_expect(!!(x), 0)
#endif
#else
#define LIKELY(x)       (x)
#define UNLIKELY(x)     (x)
#endif

#ifndef likely_if
#define likely_if(x)    if (likely(x))
#endif
#ifndef unlikely_if
#define unlikely_if(x)  if (unlikely(x))
#endif
