/**
 * MOCHOW - A Vector Database System.
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved.
 *
 */

#pragma once

#include <string>
#include <vector>
#include <bthread.h>
#include <base/endpoint.h>
#include <bthread_unstable.h>
#include <base/scoped_lock.h>

#include "core/src/common/butex.h"
#include "core/src/common/thread_pool.h"
#include "core/src/common/host_address.h"
#include "core/src/common/endpoint.h"

namespace mochow::common {

class LeaderTracker {
public:
    LeaderTracker();
    virtual ~LeaderTracker();
    int start(const base::EndPoint& local_addr, std::string token,
            std::string module_addr, uint32_t refresh_interval_second);
    void stop();

    base::EndPoint get_leader();
    std::vector<base::EndPoint> get_list();
    void get_followers(const base::EndPoint& addr, std::vector<base::EndPoint>* follower_addrs);
    void set_leader_and_followers(const base::EndPoint& leader, std::vector<base::EndPoint>* followers = nullptr);

    void refresh();
    void do_refresh();
    void update_refresh_interval_second(uint32_t refresh_interval_second);

    bool is_started() {
        return _is_started;
    }

    uint32_t refresh_interval_second() {
        return _refresh_interval_second;
    }

protected:
    static void on_refresh_timer(void* arg);
    virtual void check_leader(const std::vector<base::EndPoint>& addrs, std::map<base::EndPoint, int>* candidates) = 0;
protected:
    uint16_t _port;
    MutexLock _mutex;
    std::string _hostname;
    base::EndPoint _local_addr;
    std::atomic<uint32_t> _refresh_interval_second;

    base::EndPoint _leader_addr;
    std::vector<base::EndPoint> _module_addrs;

    bthread_timer_t _refresh_timer;
    ThreadPool* _refresh_thread;
    uint64_t _round_index;
    std::string _token;
    bool _is_started = false;
    NameServiceProtocol _ns_protocol;
};

}
