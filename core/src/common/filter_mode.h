/**
 * MOCHOW - A Vector Database System.
 * Copyright (c) 2024 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON> (<PERSON><PERSON><PERSON><PERSON>@baidu.com)
 * 
 * Date: 2025/02/13
 * Desciption: Definitions for Filter Mode
 *
 */

#pragma once

#include <string>

namespace mochow::common {

class FilterMode {
public:
    static constexpr const char* AUTO = "AUTO";
    static constexpr const char* POST = "POST";

    static inline bool is_valid(const std::string& mode) {
        return mode == AUTO || mode == POST;
    }

private:
    FilterMode() = delete;
    ~FilterMode() = delete;
};

}   // namespace mochow::common
