/**
 * MOCHOW - A Vector Database System.
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON> (<PERSON><PERSON><PERSON><PERSON>@baidu.com)
 * Date: 2023/09/19
 */

#pragma once
#include <memory>
#include <mutex>
#include <shared_mutex>
#include <unordered_set>
#include <algorithm>
#include <sstream>

#include <boost/dynamic_bitset.hpp>
#include <base/logging.h>

#include "sfl/attributes.h"

#include "core/src/common/using.h"
#include "core/src/common/butex.h"
#include "core/src/common/bitmap_impl.h"

#include "core/src/zhuque/vid_index.h"
#include "core/src/zhuque/row.h"
#include "core/src/zhuque/filter.h"
#include "core/src/schema/schema_cache.h"

namespace mochow::common {

enum class BitSetFilterMode {
    BLACKLIST = 0,
    WHITELIST = 1,
};

class BitSetFilterType final {
public:
    static constexpr const char* BASIC = "BASIC";
    static constexpr const char* UNION = "UNION";
    static constexpr const char* INTRA_FILTER = "INTRA_FILTER";

private:
    BitSetFilterType() = default;
    ~BitSetFilterType() = default;
};

class BitSetFilter {
public:
    virtual ~BitSetFilter() = default;

    virtual bool is_available(const INCID incid) const = 0;    

    virtual const std::string type() const = 0;
};

using BitSetFilterPtr = std::unique_ptr<BitSetFilter>;
using BitSetFilterRef = std::shared_ptr<BitSetFilter>;

class BasicBitSetFilter : public BitSetFilter {
public:
    BasicBitSetFilter(BitSetFilterMode mode = BitSetFilterMode::BLACKLIST) : _mode(mode) {
        _bitmap = std::make_shared<RoaringBitMap>();
    }

    BasicBitSetFilter(BitSetFilterMode mode, std::unique_ptr<common::IBitMap> bitmap, INCID max_available_incid)
            : _mode(mode), _bitmap(std::move(bitmap)), _max_available_incid(max_available_incid) {}

    virtual ~BasicBitSetFilter() = default;

    bool is_blacklist() const {
        common::ScopedButex lock(_lock);
        return mode() == BitSetFilterMode::BLACKLIST;
    }

    bool is_whitelist() const {
        common::ScopedButex lock(_lock);
        return mode() == BitSetFilterMode::WHITELIST;
    }

    BitSetFilterMode mode() const {
        return _mode;
    }

    void insert(const INCID incid) {
        common::ScopedButex lock(_lock);
        _bitmap->insert(incid);
    }

    void batch_insert(const std::vector<INCID>& incids) {
        common::ScopedButex lock(_lock);
        _bitmap->batch_insert(incids);
    }

    bool merge(std::shared_ptr<BasicBitSetFilter> other) {
        common::ScopedButex lock(_lock);
        if (other == nullptr || mode() != other->mode()) {
            return false;
        }
        return _bitmap->merge(*(other->_bitmap));
    }
    
    void flip() {
        common::ScopedButex lock(_lock);
        if (_mode == BitSetFilterMode::BLACKLIST) {
            _mode = BitSetFilterMode::WHITELIST;
        } else if (_mode == BitSetFilterMode::WHITELIST) {
            _mode = BitSetFilterMode::BLACKLIST;
        }

        _bitmap->flip();
    }

    bool intersect(std::shared_ptr<BasicBitSetFilter> const other) {
        common::ScopedButex lock(_lock);
        if (other == nullptr || mode() != other->mode()) {
            return false;
        }
        return _bitmap->intersect(*(other->_bitmap));
    }

    NODISCARD bool contains(const INCID incid) const {
        common::ScopedButex lock(_lock);
        return _bitmap->contains(incid);
    }

    void reset(IBitMapRef bitmap) {
        common::ScopedButex lock(_lock);      
        _bitmap = std::move(bitmap);
    }

    bool is_available(const INCID incid) const override {
        if (_max_available_incid.has_value() && incid > _max_available_incid.value()) {
            return false;
        }

        bool available = false;
        if (is_whitelist() && contains(incid)) {
            available = true;
        } else if (is_blacklist() && !contains(incid)) {
            available = true;
        }
        return available;
    }

    size_t size() const {
        return _bitmap->size();
    }

    size_t count() const {
        return _bitmap->count();
    }

    IBitMapRef bitmap_impl() const {
        return _bitmap;        
    }

    NODISCARD std::string to_string() const {
        common::ScopedButex lock(_lock);
        return _bitmap->to_string();
    }

    const std::string type() const override {
        return common::BitSetFilterType::BASIC;
    }

private:
    BitSetFilterMode _mode;

    mutable common::Butex _lock;

    IBitMapRef _bitmap;

    // Since bitset_filter can be a blacklist, we need to provide it with a "snapshot" of the current state.
    // This ensures that bitset_filter does not mistakenly identify newly inserted records as valid.
    std::optional<INCID> _max_available_incid;
};

using BasicBitSetFilterRef = std::shared_ptr<BasicBitSetFilter>;
using BasicBitSetFilterPtr = std::unique_ptr<BasicBitSetFilter>;

class UnionBitSetFilter : public BitSetFilter {
public:
    UnionBitSetFilter() = default;

    virtual ~UnionBitSetFilter() = default;

    void add_filter(BitSetFilterRef filter) {
        _filters.emplace_back(std::move(filter));
    }

    bool is_available(const INCID incid) const override {
        for (const auto& filter : _filters) {
            if (!filter->is_available(incid)) {
                return false;
            }
        }

        return true;
    }   

    const std::string type() const override {
        return common::BitSetFilterType::UNION;
    }

private:
    std::vector<BitSetFilterRef>  _filters;
};

using UnionBitSetFilterRef = std::shared_ptr<UnionBitSetFilter>;
using UnionBitSetFilterPtr = std::unique_ptr<UnionBitSetFilter>;

constexpr const char* BLACKLIST_BITSET_FILTER_CLASS = "BlacklistBitsetFilter";
constexpr const char* WHITELIST_BITSET_FILTER_CLASS = "WhitelistBitsetFilter";

class BitSetFilterFactory {
public:
    static BitSetFilterFactory& get_instance() {
        static BitSetFilterFactory factory;
        return factory;
    }

    BitSetFilterPtr create(const std::string& bitset_filter_class_name) {
        if (bitset_filter_class_name == BLACKLIST_BITSET_FILTER_CLASS) {
            return std::make_unique<BasicBitSetFilter>(BitSetFilterMode::BLACKLIST);
        } else if (bitset_filter_class_name == WHITELIST_BITSET_FILTER_CLASS) {
            return std::make_unique<BasicBitSetFilter>(BitSetFilterMode::WHITELIST);
        } else {
            LOG(TRACE) << "unrecognized bitset filter class name: "
                       << bitset_filter_class_name;
            return nullptr;
        }
    }

private:
    BitSetFilterFactory() = default;
    BitSetFilterFactory(const BitSetFilterFactory&) = delete;
    BitSetFilterFactory& operator=(const BitSetFilterFactory&) = delete;
};

}
