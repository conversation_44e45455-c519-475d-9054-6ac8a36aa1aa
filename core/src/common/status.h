/**
 * MOCHOW - A Vector Database System.
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON> (<EMAIL>)
 * Date: 2023/10/27
 * Desciption: Definitions for Status
 *
 */

#pragma once

#include <string>
#include <string_view>
#include <sstream>
#include <stdio.h>
#include <stdarg.h>
#include <rocksdb/status.h>
#include <base/status.h>
#include "baidu/vdb/mochow/core/src/proto/error_code.pb.h"
#include "core/src/common/likely.h"

namespace mochow {

#define RETURN_IF_ERROR(_status_)   \
    if (UNLIKELY(!_status_.ok())) { \
        return _status_;            \
    }

#define RETURN_NULL_IF_ERROR(_status_)  \
    if (UNLIKELY(!_status_.ok())) {     \
        return nullptr;                 \
    }

class Status final {
public:
    Status() : _code(OK) {}

    Status(int code) {
        _code = code;
        _msg = MochowErrno_Name((MochowErrno) code);
    }

    Status(int code, const char *msg) {
        _code = code;
        _msg = msg;
    }

    Status(int code, const std::string& msg) {
        _code = code;
        _self = msg;
        _msg = _self;
    }

    Status(const rocksdb::Status& s) {
        switch (s.code()) {
        case rocksdb::Status::kOk:
            _code = OK;
            break;
        case rocksdb::Status::kIOError:
            if (s.IsNoSpace()) {
                _code = ERR_STORAGE_SHORTAGE;
            } else {
                _code = ERR_IO_ERROR;
            }
            break;
        case rocksdb::Status::kNotFound:
            _code = ERR_NOT_EXIST;
            break;
        case rocksdb::Status::kIncomplete:
            _code = ERR_DB_INCOMPLETE;
            break;
        default:
            _code = ERR_DB_INTERNAL_ERROR;
        }
        if (_code != OK) {
            _self = s.ToString();
            if (!_self.empty()) {
                _msg = _self;
            }
        }
    }

    Status(const Status &s) {
        _code = s._code;
        if (s.self_host()) {
            _self = s._self;
            _msg = _self;
        } else {
            _msg = s._msg;
        }
    }

    Status &operator= (const Status &s) {
        _code = s._code;
        if (s.self_host()) {
            _self = s._self;
            _msg = _self;
        } else {
            _msg = s._msg;
        }
        return *this;
    }

    inline int code() const {
        return _code;
    }

    inline const std::string_view msg() const {
        return _msg;
    }

    inline const char* msg_str() const {
        return _msg.data();
    }

    inline bool self_host() const {
        return !_self.empty();
    }

    inline bool ok() const {
        return _code == OK;
    }

    inline bool failed() const {
        return _code != OK;
    }

    std::string to_string() const {
        std::ostringstream oss;
        oss << "(" << _code << ", " << _msg << ")";
        return oss.str();
    }

    static Status printf(int code, const char *fmt, ...) {
        char buffer[4096];
        va_list ap;
        va_start(ap, fmt);
        vsnprintf(buffer, sizeof(buffer), fmt, ap);
        va_end(ap);
        return Status(code, buffer);
    }

    static Status by_errno(int err, const char *msg = NULL) {
        Status s;
        if (err == EIO || err == EROFS || err == EAGAIN) {
            s._code = ERR_IO_ERROR;
        } else if (err == ENOSPC) {
            s._code = ERR_STORAGE_SHORTAGE;
        } else {
            s._code = ERR_DB_INTERNAL_ERROR;
        }
        if (msg == NULL || strlen(msg) == 0) {
            s._msg = strerror(err);
        } else {
            s._self = msg;
            s._msg = s._self;
        }
        return s;
    }

    static Status by_errno(const char *msg) {
        return by_errno(errno, msg);
    }

    bool is_not_exist() const {
        return _code == ERR_NOT_EXIST;
    }

    // for iterator end.
    bool is_iterator_end() const {
        return _code == ERR_ITERATOR_END;
    }

private:
    int _code;
    std::string_view _msg;
    std::string _self;
};

static inline std::ostream& operator<<(std::ostream& os, const Status& status) {
    return os << "(code:" << status.code() << ", msg:" << status.msg() << ")";
}

}
