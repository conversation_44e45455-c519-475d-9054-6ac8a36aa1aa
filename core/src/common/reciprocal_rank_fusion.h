/***************************************************************************
 *
 * Copyright (c) 2024 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

#pragma once

namespace mochow {

// k is a constant used to adjust the influence of documents with lower rankings.
// The greater the value of k, the greater the influence of the documents with
// lower rankings. It is usually set as 60 by default.
constexpr const size_t K = 60;

constexpr inline double get_score(const size_t rank, const size_t k = K) {
    return 1.0 / (k + rank);
}

}
