#include "encrypted_file_system.h"

#include "core/src/common/sm4.h"
#include "sfl/random.h"
#include "sfl/bresult.h"
#include "sfl/logging.h"
#include "sfl/return.h"

namespace mochow::common {

AESCipher::AESCipher(const std::string& key) : _block_size(AES_BLOCK_SIZE) {
    int rc = AES_set_encrypt_key((const unsigned char*)key.data(), key.size() * 8, &_aes_key);
    LOG_AND_ASSERT_EXPR(rc == 0, "AES_set_encrypt_key failed");
};

size_t AESCipher::block_size() {
    return _block_size;
};

BRESULT AESCipher::encrypt(char* data) {
    AES_encrypt((const unsigned char*)data, (unsigned char*)data, &_aes_key);
    RETURN_SUCCESS;
};

BRESULT AESCipher::decrypt(char* data) {
    AES_decrypt((const unsigned char*)data, (unsigned char*)data, &_aes_key);
    RETURN_SUCCESS;
};

SM4Cipher::SM4Cipher(const std::string& key) : _block_size(SM4_BLOCK_SIZE) {
    LOG_AND_ASSERT_EXPR(key.size() * 8 == 128, "sm4 key size should be 128bits");
    int rc = ossl_sm4_set_key((const unsigned char*)key.data(), &_sm4_key);
    LOG_AND_ASSERT_EXPR(rc == 0, "SM4_set_encrypt_key failed");
};

size_t SM4Cipher::block_size() {
    return _block_size;
};

BRESULT SM4Cipher::encrypt(char* data) {
    ossl_sm4_encrypt((const unsigned char*)data, (unsigned char*)data, &_sm4_key);
    RETURN_SUCCESS;
};

BRESULT SM4Cipher::decrypt(char* data) {
    ossl_sm4_decrypt((const unsigned char*)data, (unsigned char*)data, &_sm4_key);
    RETURN_SUCCESS;
};

BRESULT BlockAccessCipherStream::encrypt(uint64_t file_offset, char* data, size_t data_size) {
    auto blockSize = block_size();
    uint64_t block_index = file_offset / blockSize;
    size_t blockOffset = file_offset % blockSize;
    std::unique_ptr<char[]> blockBuffer;

    std::string scratch;
    alloc_scratch(scratch);

    while (true) {
        char* block = data;
        size_t n = std::min(data_size, blockSize - blockOffset);
        if (n != blockSize) {
            if (!blockBuffer) {
                blockBuffer = std::unique_ptr<char[]>(new char[blockSize]);
            }
            block = blockBuffer.get();
            memmove(block + blockOffset, data, n);
        }
        auto status = encrypt_block(block_index, block, (char*)scratch.data());
        if (baidu::sfl::is_failed(status)) {
            return status;
        }
        if (block != data) {
            memmove(data, block + blockOffset, n);
        }
        data_size -= n;
        if (data_size == 0) {
            RETURN_SUCCESS;
        }
        data += n;
        blockOffset = 0;
        block_index++;
    }
}

BRESULT BlockAccessCipherStream::decrypt(uint64_t file_offset, char* data, size_t data_size) {
    auto blockSize = block_size();
    uint64_t block_index = file_offset / blockSize;
    size_t blockOffset = file_offset % blockSize;
    std::unique_ptr<char[]> blockBuffer;

    std::string scratch;
    alloc_scratch(scratch);

    while (true) {
        char* block = data;
        size_t n = std::min(data_size, blockSize - blockOffset);
        if (n != blockSize) {
            if (!blockBuffer) {
                blockBuffer = std::unique_ptr<char[]>(new char[blockSize]);
            }
            block = blockBuffer.get();
            memmove(block + blockOffset, data, n);
        }
        auto status = decrypt_block(block_index, block, (char*)scratch.data());
        if (baidu::sfl::is_failed(status)) {
            return status;
        }
        if (block != data) {
            memmove(data, block + blockOffset, n);
        }

        assert(data_size >= n);
        if (data_size < n) {
            return baidu::sfl::BRESULT_OUT_OF_RANGE;
        }

        data_size -= n;
        if (data_size == 0) {
            RETURN_SUCCESS;
        }
        data += n;
        blockOffset = 0;
        block_index++;
    }
}

void CTRCipherStream::alloc_scratch(std::string& scratch) {
    auto blockSize = _cipher->block_size();
    scratch.reserve(blockSize);
}

BRESULT CTRCipherStream::encrypt_block(uint64_t block_index, char* data, char* scratch) {
    auto blockSize = _cipher->block_size();
    memmove(scratch, _iv.data(), blockSize);
    uint64_t value = block_index + _initial_counter;
    memcpy(scratch, &value, sizeof(value));

    auto status = _cipher->encrypt(scratch);
    if (baidu::sfl::is_failed(status)) {
        return status;
    }

    for (size_t i = 0; i < blockSize; i++) {
        data[i] = data[i] ^ scratch[i];
    }
    RETURN_SUCCESS;
}

BRESULT CTRCipherStream::decrypt_block(uint64_t block_index, char* data, char* scratch) {
    return encrypt_block(block_index, data, scratch);
}

CTREncryptionProvider::CTREncryptionProvider(const std::shared_ptr<BlockCipher>& c) : _cipher(c) {}

size_t CTREncryptionProvider::get_prefix_length() const {
    return default_prefix_length;
}

BRESULT CTREncryptionProvider::create_new_prefix(const std::string& /*fname*/, char* prefix, size_t prefix_length)
        const {
    if (!_cipher) {
        return baidu::sfl::BRESULT_INVALID_ARGUMENT;
    }
    for (size_t i = 0; i < prefix_length; i++) {
        prefix[i] = baidu::sfl::get_tls_random32_in_range(0, 255);
    }
    auto blockSize = _cipher->block_size();
    uint64_t initial_counter;
    char* prefixIV = prefix + blockSize;
    memcpy(&initial_counter, prefix, sizeof(initial_counter));

    CTRCipherStream cipherStream(_cipher, prefixIV, initial_counter);
    BRESULT status;
    status = cipherStream.encrypt(0, prefix + (2 * blockSize), prefix_length - (2 * blockSize));

    return status;
}

BRESULT CTREncryptionProvider::create_cipher_stream(
        const std::string& fname,
        char* prefix,
        size_t prefix_length,
        std::unique_ptr<BlockAccessCipherStream>* result) {
    if (!_cipher) {
        return baidu::sfl::BRESULT_INVALID_ARGUMENT;
    }
    auto blockSize = _cipher->block_size();
    uint64_t initial_counter;
    char* iv = prefix + blockSize;
    memcpy(&initial_counter, prefix, sizeof(initial_counter));

    LOG_AND_ASSERT(prefix_length >= 2 * blockSize);
    if (prefix_length < 2 * blockSize) {
        return baidu::sfl::BRESULT_INVALID_ARGUMENT;
    }

    CTRCipherStream cipherStream(_cipher, iv, initial_counter);
    BRESULT status = cipherStream.decrypt(0, prefix + (2 * blockSize), prefix_length - (2 * blockSize));
    if (baidu::sfl::is_failed(status)) {
        return status;
    }

    return create_cipher_stream_from_prefix(fname, initial_counter, iv, prefix, result);
}

BRESULT CTREncryptionProvider::create_cipher_stream_from_prefix(
        const std::string& fname,
        uint64_t initial_counter,
        const char* iv,
        const char* prefix,
        std::unique_ptr<BlockAccessCipherStream>* result) {
    (*result) = std::unique_ptr<BlockAccessCipherStream>(new CTRCipherStream(_cipher, iv, initial_counter));
    RETURN_SUCCESS;
}

BRESULT EncryptedFileSystem::initialize(const EncryptedFileSystemOption& option) {
    if (option.get_name().empty() || option.get_thread_count() == 0) {
        return baidu::sfl::BRESULT_INVALID_ARGUMENT;
    }

    bool expected = false;
    const bool ok = _initialized.compare_exchange_strong(expected, true);
    if (!ok) {
        return baidu::sfl::BRESULT_INITIALIZE_MORE_THAN_ONCE;
    }

    _file_system_name = option.get_name();
    _thread_count = option.get_thread_count();
    _provider = option.get_provider();

    RETURN_SUCCESS;
}

BRESULT EncryptedFileSystem::new_writable_file(
        const char* const path_name,
        const uint64_t open_file_flags,
        baidu::sfl::FileHandle& file_handle) {
    int internal_flag = O_WRONLY | O_APPEND;

    switch (open_file_flags & baidu::sfl::OPEN_FILE_ACTION_MASK) {
    case 0:
        /* do nothing */
        break;
    case baidu::sfl::CREATE_IF_NOT_EXIST:
        internal_flag |= O_CREAT;
        break;
    case baidu::sfl::FAIL_IF_EXIST:
        internal_flag |= O_CREAT | O_EXCL;
        break;
    case baidu::sfl::OVERWRITE_IF_EXIST:
        internal_flag |= O_CREAT | O_TRUNC;
        break;
    default:
        return baidu::sfl::BRESULT_INVALID_ARGUMENT;
    }

    int fd = ::open(path_name, internal_flag, S_IRUSR | S_IWUSR | S_IRGRP | S_IROTH);
    if (fd == -1) {
        return baidu::sfl::get_last_error();
    }
    LOG_AND_ASSERT(fd >= 0);

    uint64_t prefix_length = _provider->get_prefix_length();
    char prefix[prefix_length];
    RETURN_IF_FAILED(_provider->create_new_prefix(path_name, prefix, prefix_length));

    const ssize_t length = ::write(fd, prefix, prefix_length);
    if (length == -1) {
        return baidu::sfl::get_last_error();
    }

    std::unique_ptr<BlockAccessCipherStream> stream;
    RETURN_IF_FAILED(_provider->create_cipher_stream(path_name, prefix, prefix_length, &stream));

    file_handle = std::make_shared<EncryptedFile>(path_name, fd, open_file_flags, prefix_length, std::move(stream));
    RETURN_SUCCESS;
}

BRESULT EncryptedFileSystem::new_readable_file(
        const char* const path_name,
        const uint64_t open_file_flags,
        baidu::sfl::FileHandle& file_handle) {
    int fd = ::open(path_name, O_RDONLY);
    if (fd == -1) {
        return baidu::sfl::get_last_error();
    }
    LOG_AND_ASSERT(fd >= 0);

    const auto encrypted_file = dynamic_cast<EncryptedFile*>(file_handle.get());

    uint64_t prefix_length = _provider->get_prefix_length();
    char prefix[prefix_length];
    uint64_t offset = 0;

    while (offset < prefix_length) {
        const ssize_t length = ::pread(fd, prefix, prefix_length - offset, offset);
        if (length == -1) {
            return baidu::sfl::get_last_error();
        }

        LOG_AND_ASSERT(length >= 0);
        LOG_AND_ASSERT(static_cast<uint64_t>(length) <= prefix_length - offset);
        offset += length;

        if (length == 0) {
            return baidu::sfl::BRESULT_READ_FILE_END;
        }
    }

    std::unique_ptr<BlockAccessCipherStream> stream;
    RETURN_IF_FAILED(_provider->create_cipher_stream(path_name, prefix, prefix_length, &stream));

    file_handle = std::make_shared<EncryptedFile>(path_name, fd, open_file_flags, prefix_length, std::move(stream));
    RETURN_SUCCESS;
}

BRESULT EncryptedFileSystem::open(
        const char* const path_name,
        const uint64_t open_file_flags,
        const uint64_t timeout_us,
        const baidu::sfl::IUserOption* const user_option,
        baidu::sfl::FileHandle& file_handle) {
    std::ignore = timeout_us;
    std::ignore = user_option;

    file_handle = nullptr;

    if (!_initialized) {
        return baidu::sfl::BRESULT_FILE_SYSTEM_NOT_INITIALIZED;
    }

    if (path_name == nullptr) {
        return baidu::sfl::BRESULT_INVALID_ARGUMENT;
    }

    if ((open_file_flags & baidu::sfl::OPEN_FILE_READ_WRITE_MASK) != 0 &&
        (open_file_flags & baidu::sfl::OPEN_FILE_READ_WRITE_MASK) != baidu::sfl::OPEN_FILE_READ_ONLY &&
        (open_file_flags & baidu::sfl::OPEN_FILE_READ_WRITE_MASK) != baidu::sfl::OPEN_FILE_APPEND_ONLY) {
        return baidu::sfl::BRESULT_INVALID_ARGUMENT;
    }

    if ((open_file_flags & baidu::sfl::OPEN_FILE_ACTION_MASK) != 0 &&
        (open_file_flags & baidu::sfl::OPEN_FILE_ACTION_MASK) != baidu::sfl::CREATE_IF_NOT_EXIST &&
        (open_file_flags & baidu::sfl::OPEN_FILE_ACTION_MASK) != baidu::sfl::FAIL_IF_EXIST &&
        (open_file_flags & baidu::sfl::OPEN_FILE_ACTION_MASK) != baidu::sfl::OVERWRITE_IF_EXIST) {
        return baidu::sfl::BRESULT_INVALID_ARGUMENT;
    }

    if ((open_file_flags & baidu::sfl::OPEN_FILE_MEDIA_MASK) != 0 &&
        (open_file_flags & baidu::sfl::OPEN_FILE_MEDIA_MASK) != baidu::sfl::FILE_MEDIA_TYPE_HDD &&
        (open_file_flags & baidu::sfl::OPEN_FILE_MEDIA_MASK) != baidu::sfl::FILE_MEDIA_TYPE_SSD) {
        return baidu::sfl::BRESULT_INVALID_ARGUMENT;
    }

    if ((open_file_flags & ~baidu::sfl::OPEN_FILE_FLAG_MASK) != 0) {
        return baidu::sfl::BRESULT_INVALID_ARGUMENT;
    }

    std::shared_ptr<EncryptedFile> new_file_handle;
    // read only file
    if ((open_file_flags & baidu::sfl::OPEN_FILE_READ_WRITE_MASK) == 0 ||
        (open_file_flags & baidu::sfl::OPEN_FILE_READ_WRITE_MASK) == baidu::sfl::OPEN_FILE_READ_ONLY) {
        if ((open_file_flags & baidu::sfl::OPEN_FILE_ACTION_MASK) != 0 ||
            (open_file_flags & baidu::sfl::OPEN_FILE_MEDIA_MASK) != 0) {
            return baidu::sfl::BRESULT_INVALID_ARGUMENT;
        }

        return new_readable_file(path_name, open_file_flags, file_handle);
    } else {
        return new_writable_file(path_name, open_file_flags, file_handle);
    }
}

BRESULT EncryptedFileSystem::close(
        baidu::sfl::FileHandle& file_handle,
        const uint64_t close_file_flags,
        const uint64_t timeout_us,
        const baidu::sfl::IUserOption* const user_option) {
    std::ignore = timeout_us;
    std::ignore = user_option;

    if (!_initialized) {
        return baidu::sfl::BRESULT_FILE_SYSTEM_NOT_INITIALIZED;
    }

    if (file_handle == nullptr) {
        RETURN_SUCCESS;
    }

    if ((close_file_flags & ~baidu::sfl::CLOSE_FILE_FLAG_MASK) != 0) {
        return baidu::sfl::BRESULT_INVALID_ARGUMENT;
    }

    const auto posix_file_handle = std::dynamic_pointer_cast<EncryptedFile>(file_handle);

    if (posix_file_handle == nullptr) {
        return baidu::sfl::BRESULT_INVALID_FILE_HANDLE;
    }

    {
        const baidu::sfl::SharedMutexExclusiveGuard guard(&posix_file_handle->_shared_mutex);

        if (posix_file_handle->_fd == -1) {
            file_handle = nullptr;
            RETURN_SUCCESS;
        }

        if ((close_file_flags & baidu::sfl::CLOSE_FILE_WITHOUT_SYNC) == 0) {
            const int result = ::fsync(posix_file_handle->_fd);
            if (result != 0) {
                return baidu::sfl::get_last_error();
            }
        }

        const int result = ::close(posix_file_handle->_fd);
        const auto errnum = errno;

        if (result != 0) {
            WARNING_LOG << "close failed. _fd=" << posix_file_handle->_fd
                        << ", _path_name=" << posix_file_handle->_path_name
                        << ", _open_file_flag=" << posix_file_handle->_open_file_flags << ", errno=" << errnum
                        << ", errmsg=" << std::strerror(errnum) << ENDL;
        }

        posix_file_handle->_fd = -1;

        file_handle = nullptr;
    }

    RETURN_SUCCESS;
}

BRESULT EncryptedFileSystem::pread(
        const baidu::sfl::FileHandle& file_handle,
        const uint64_t read_file_flags,
        void* const buffer,
        const uint64_t buffer_length,
        const uint64_t file_offset,
        const uint64_t timeout_us,
        const baidu::sfl::IUserOption* const user_option,
        uint64_t* const read_length,
        baidu::sfl::AsyncCallback const callback,
        baidu::sfl::AsyncContext* const context) {
    std::ignore = timeout_us;
    std::ignore = user_option;

    if (baidu::sfl::AsyncContext::is_async(callback, context)) {
        return read_async(
                file_handle,
                read_file_flags,
                buffer,
                buffer_length,
                file_offset + _provider->get_prefix_length(),
                read_length,
                callback,
                context);
    } else {
        return read_sync(
                file_handle,
                read_file_flags,
                buffer,
                buffer_length,
                file_offset + _provider->get_prefix_length(),
                read_length);
    }
}

BRESULT EncryptedFileSystem::append(
        const baidu::sfl::FileHandle& file_handle,
        const uint64_t append_file_flags,
        const void* const buffer,
        const uint64_t buffer_length,
        const uint64_t timeout_us,
        const baidu::sfl::IUserOption* const user_option) {
    std::ignore = timeout_us;
    std::ignore = user_option;

    if (!_initialized) {
        return baidu::sfl::BRESULT_FILE_SYSTEM_NOT_INITIALIZED;
    }

    if (file_handle == nullptr) {
        return baidu::sfl::BRESULT_INVALID_FILE_HANDLE;
    }

    if ((append_file_flags & ~baidu::sfl::APPEND_FILE_FLAG_MASK) != 0) {
        return baidu::sfl::BRESULT_INVALID_ARGUMENT;
    }

    if (buffer == nullptr && buffer_length != 0) {
        return baidu::sfl::BRESULT_INVALID_ARGUMENT;
    }

    const auto encrypted_file = dynamic_cast<EncryptedFile*>(file_handle.get());

    if (encrypted_file == nullptr) {
        return baidu::sfl::BRESULT_INVALID_FILE_HANDLE;
    }

    {
        const baidu::sfl::SharedMutexExclusiveGuard guard(&encrypted_file->_shared_mutex);

        if (encrypted_file->_fd == -1) {
            return baidu::sfl::BRESULT_INVALID_FILE_HANDLE;
        }

        const auto open_file_flags = encrypted_file->_open_file_flags;
        if ((open_file_flags & baidu::sfl::OPEN_FILE_READ_WRITE_MASK) == 0 ||
            (open_file_flags & baidu::sfl::OPEN_FILE_READ_WRITE_MASK) == baidu::sfl::OPEN_FILE_READ_ONLY) {
            return baidu::sfl::BRESULT_WRITE_READ_ONLY_FILE;
        }

        struct stat status;
        const int result = ::stat(encrypted_file->get_path().c_str(), &status);
        if (result != 0) {
            return baidu::sfl::get_last_error();
        }

        uint64_t file_size = status.st_size;

        char* data = new char[buffer_length];
        if (buffer != nullptr) {
            memcpy(data, buffer, buffer_length);
        }
        RETURN_IF_FAILED(encrypted_file->encrypt(file_size, data, buffer_length));

        uint64_t offset = 0;
        while (offset < buffer_length) {
            const ssize_t length = ::write(encrypted_file->_fd, data + offset, buffer_length - offset);
            if (length == -1) {
                delete[] data;
                return baidu::sfl::get_last_error();
            }

            LOG_AND_ASSERT(length >= 0);
            LOG_AND_ASSERT(static_cast<uint64_t>(length) <= buffer_length - offset);
            offset += length;
        }
        delete[] data;
    }

    RETURN_SUCCESS;
}

BRESULT EncryptedFileSystem::fsync(
        const baidu::sfl::FileHandle& file_handle,
        const uint64_t fsync_file_flags,
        const uint64_t timeout_us,
        const baidu::sfl::IUserOption* const user_option) {
    std::ignore = timeout_us;
    std::ignore = user_option;

    if (!_initialized) {
        return baidu::sfl::BRESULT_FILE_SYSTEM_NOT_INITIALIZED;
    }

    if (file_handle == nullptr) {
        return baidu::sfl::BRESULT_INVALID_FILE_HANDLE;
    }

    if ((fsync_file_flags & ~baidu::sfl::FSYNC_FILE_FLAG_MASK) != 0) {
        return baidu::sfl::BRESULT_INVALID_ARGUMENT;
    }

    const auto encrypted_file = dynamic_cast<EncryptedFile*>(file_handle.get());

    if (encrypted_file == nullptr) {
        return baidu::sfl::BRESULT_INVALID_FILE_HANDLE;
    }

    {
        const baidu::sfl::SharedMutexExclusiveGuard guard(&encrypted_file->_shared_mutex);

        if (encrypted_file->_fd == -1) {
            return baidu::sfl::BRESULT_INVALID_FILE_HANDLE;
        }

        const int result = ::fsync(encrypted_file->_fd);
        if (result != 0) {
            return baidu::sfl::get_last_error();
        }
    }

    RETURN_SUCCESS;
}

BRESULT EncryptedFileSystem::size(
        const char* const path_name,
        const uint64_t size_file_flags,
        const uint64_t timeout_us,
        const baidu::sfl::IUserOption* const user_option,
        uint64_t& file_size) {
    std::ignore = timeout_us;
    std::ignore = user_option;

    file_size = 0;

    if (!_initialized) {
        return baidu::sfl::BRESULT_FILE_SYSTEM_NOT_INITIALIZED;
    }

    if (path_name == nullptr) {
        return baidu::sfl::BRESULT_INVALID_ARGUMENT;
    }

    if ((size_file_flags & ~baidu::sfl::SIZE_FILE_FLAG_MASK) != 0) {
        return baidu::sfl::BRESULT_INVALID_ARGUMENT;
    }

    struct stat buffer;
    const int result = ::stat(path_name, &buffer);
    if (result != 0) {
        return baidu::sfl::get_last_error();
    }

    file_size = buffer.st_size;

    RETURN_SUCCESS;
}

BRESULT EncryptedFileSystem::size(
        const baidu::sfl::FileHandle& file_handle,
        const uint64_t size_file_flags,
        const uint64_t timeout_us,
        const baidu::sfl::IUserOption* const user_option,
        uint64_t& file_size) {
    std::ignore = timeout_us;
    std::ignore = user_option;

    file_size = 0;

    if (!_initialized) {
        return baidu::sfl::BRESULT_FILE_SYSTEM_NOT_INITIALIZED;
    }

    if (file_handle == nullptr) {
        return baidu::sfl::BRESULT_INVALID_FILE_HANDLE;
    }

    if ((size_file_flags & ~baidu::sfl::SIZE_FILE_FLAG_MASK) != 0) {
        return baidu::sfl::BRESULT_INVALID_ARGUMENT;
    }

    const auto encrypted_file = dynamic_cast<EncryptedFile*>(file_handle.get());

    if (encrypted_file == nullptr) {
        return baidu::sfl::BRESULT_INVALID_FILE_HANDLE;
    }

    {
        const baidu::sfl::SharedMutexSharedGuard guard(&encrypted_file->_shared_mutex);

        if (encrypted_file->_fd == -1) {
            return baidu::sfl::BRESULT_INVALID_FILE_HANDLE;
        }

        struct stat buffer;
        const int result = ::fstat(encrypted_file->_fd, &buffer);
        if (result != 0) {
            return baidu::sfl::get_last_error();
        }

        file_size = buffer.st_size - _provider->get_prefix_length();
    }

    RETURN_SUCCESS;
}

BRESULT EncryptedFileSystem::mtime(
        const char* const path_name,
        const uint64_t mtime_file_flags,
        const uint64_t timeout_us,
        const baidu::sfl::IUserOption* const user_option,
        std::time_t& file_mtime) {
    std::ignore = timeout_us;
    std::ignore = user_option;

    file_mtime = 0;

    if (!_initialized) {
        return baidu::sfl::BRESULT_FILE_SYSTEM_NOT_INITIALIZED;
    }

    if (path_name == nullptr) {
        return baidu::sfl::BRESULT_INVALID_ARGUMENT;
    }

    if ((mtime_file_flags & ~baidu::sfl::MTIME_FILE_FLAG_MASK) != 0) {
        return baidu::sfl::BRESULT_INVALID_ARGUMENT;
    }

    struct stat buffer;
    const int result = ::stat(path_name, &buffer);
    if (result != 0) {
        return baidu::sfl::get_last_error();
    }

    file_mtime = buffer.st_mtime;

    RETURN_SUCCESS;
}

BRESULT EncryptedFileSystem::mtime(
        const baidu::sfl::FileHandle& file_handle,
        const uint64_t mtime_file_flags,
        const uint64_t timeout_us,
        const baidu::sfl::IUserOption* const user_option,
        std::time_t& file_mtime) {
    std::ignore = timeout_us;
    std::ignore = user_option;

    file_mtime = 0;

    if (!_initialized) {
        return baidu::sfl::BRESULT_FILE_SYSTEM_NOT_INITIALIZED;
    }

    if (file_handle == nullptr) {
        return baidu::sfl::BRESULT_INVALID_FILE_HANDLE;
    }

    if ((mtime_file_flags & ~baidu::sfl::MTIME_FILE_FLAG_MASK) != 0) {
        return baidu::sfl::BRESULT_INVALID_ARGUMENT;
    }

    const auto encrypted_file = dynamic_cast<EncryptedFile*>(file_handle.get());

    if (encrypted_file == nullptr) {
        return baidu::sfl::BRESULT_INVALID_FILE_HANDLE;
    }

    {
        const baidu::sfl::SharedMutexSharedGuard guard(&encrypted_file->_shared_mutex);

        if (encrypted_file->_fd == -1) {
            return baidu::sfl::BRESULT_INVALID_FILE_HANDLE;
        }

        struct stat buffer;
        const int result = ::fstat(encrypted_file->_fd, &buffer);
        if (result != 0) {
            return baidu::sfl::get_last_error();
        }

        file_mtime = buffer.st_mtime;
    }

    RETURN_SUCCESS;
}

BRESULT EncryptedFileSystem::exist(
        const char* const path_name,
        const uint64_t timeout_us,
        const baidu::sfl::IUserOption* const user_option,
        bool& found) {
    BRESULT br;

    std::ignore = timeout_us;
    std::ignore = user_option;

    found = false;

    if (!_initialized) {
        return baidu::sfl::BRESULT_FILE_SYSTEM_NOT_INITIALIZED;
    }

    if (path_name == nullptr) {
        return baidu::sfl::BRESULT_INVALID_ARGUMENT;
    }

    const int result = ::access(path_name, F_OK);
    const auto errnum = errno;

    if (result != 0) {
        if (errnum == ENOENT) {
            RETURN_SUCCESS;
        }

        br = baidu::sfl::linux_system_errno_to_bresult(errnum);
        RETURN_FAILURE(br);
    }

    found = true;

    RETURN_SUCCESS;
}

BRESULT EncryptedFileSystem::rename(
        const char* const old_path_name,
        const char* const new_path_name,
        const uint64_t rename_flags,
        const uint64_t timeout_us,
        const baidu::sfl::IUserOption* const user_option) {
    BRESULT br;

    std::ignore = timeout_us;
    std::ignore = user_option;

    if (!_initialized) {
        return baidu::sfl::BRESULT_FILE_SYSTEM_NOT_INITIALIZED;
    }

    if (old_path_name == nullptr || new_path_name == nullptr) {
        return baidu::sfl::BRESULT_INVALID_ARGUMENT;
    }

    if ((rename_flags & ~baidu::sfl::RENAME_FLAG_MASK) != 0) {
        return baidu::sfl::BRESULT_INVALID_ARGUMENT;
    }

    bool new_path_name_exist = false;

    if ((rename_flags & baidu::sfl::RENAME_WITH_REPLACE) == 0) {
        br = exist(new_path_name, UINT64_MAX, nullptr, new_path_name_exist);
        RETURN_IF_FAILED(br);
    }

    // There is a loophole. If another thread creates the new path name after
    // this function checks the existence of the new path name and before the
    // function calls ::rename, the new path name will be replaced even when
    // user does not use RENAME_WITH_REPLACE flag.

    if ((rename_flags & baidu::sfl::RENAME_WITH_REPLACE) != 0 || !new_path_name_exist) {
        const int result = std::rename(old_path_name, new_path_name);
        if (result != 0) {
            return baidu::sfl::get_last_error();
        }
    } else {
        // The new_path_name exists and can not be replaced.
        br = baidu::sfl::linux_system_errno_to_bresult(EEXIST);
        RETURN_FAILURE(br);
    }

    RETURN_SUCCESS;
}

BRESULT EncryptedFileSystem::link(
        const char* const old_path_name,
        const char* const new_path_name,
        const uint64_t link_flags,
        const uint64_t timeout_us,
        const baidu::sfl::IUserOption* const user_option) {
    std::ignore = timeout_us;
    std::ignore = user_option;

    if (!_initialized) {
        return baidu::sfl::BRESULT_FILE_SYSTEM_NOT_INITIALIZED;
    }

    if (old_path_name == nullptr || new_path_name == nullptr) {
        return baidu::sfl::BRESULT_INVALID_ARGUMENT;
    }

    if ((link_flags & ~baidu::sfl::LINK_FLAG_MASK) != 0) {
        return baidu::sfl::BRESULT_INVALID_ARGUMENT;
    }

    const int result = ::link(old_path_name, new_path_name);
    if (result != 0) {
        return baidu::sfl::get_last_error();
    }

    RETURN_SUCCESS;
}

BRESULT EncryptedFileSystem::symlink(
        const char* const old_path_name,
        const char* const new_path_name,
        const uint64_t symlink_flags,
        const uint64_t timeout_us,
        const baidu::sfl::IUserOption* const user_option) {
    std::ignore = timeout_us;
    std::ignore = user_option;

    if (!_initialized) {
        return baidu::sfl::BRESULT_FILE_SYSTEM_NOT_INITIALIZED;
    }

    if (old_path_name == nullptr || new_path_name == nullptr) {
        return baidu::sfl::BRESULT_INVALID_ARGUMENT;
    }

    if ((symlink_flags & ~baidu::sfl::SYMLINK_FLAG_MASK) != 0) {
        return baidu::sfl::BRESULT_INVALID_ARGUMENT;
    }

    const int result = ::symlink(old_path_name, new_path_name);
    if (result != 0) {
        return baidu::sfl::get_last_error();
    }

    RETURN_SUCCESS;
}

class AutoCloseDir final : public baidu::sfl::INoHeapAllocation {
public:
    AutoCloseDir(DIR* const dir) : _dir(dir) {}

    ~AutoCloseDir() {
        if (_dir != nullptr) {
            const int result = ::closedir(_dir);
            // According to linux man page on the closedir function, it fails only
            // when dir is not a valid directory stream descriptor, which will not
            // happen in this case. So we can assert here.
            LOG_AND_ASSERT(result == 0);
        }
    }

    DISABLE_COPY_AND_MOVE(AutoCloseDir)

private:
    DIR* const _dir = nullptr;
};

BRESULT EncryptedFileSystem::lsdir(
        const char* const path_name,
        const uint64_t timeout_us,
        const baidu::sfl::IUserOption* const user_option,
        std::vector<baidu::sfl::DirectoryEntryDescriptor>& entries) {
    std::ignore = timeout_us;
    std::ignore = user_option;

    entries.clear();

    if (!_initialized) {
        return baidu::sfl::BRESULT_FILE_SYSTEM_NOT_INITIALIZED;
    }

    if (path_name == nullptr) {
        return baidu::sfl::BRESULT_INVALID_ARGUMENT;
    }

    DIR* const dir = ::opendir(path_name);
    if (dir == nullptr) {
        return baidu::sfl::get_last_error();
    }

    AutoCloseDir auto_close_dir(dir);

    while (true) {
        errno = 0;
        const struct dirent* const entry = ::readdir(dir);
        assert(entry != nullptr || errno == 0);

        if (entry == nullptr) {
            break;
        }

        baidu::sfl::DirectoryEntryDescriptor descriptor;
        descriptor.name = entry->d_name;

        if (entry->d_type == DT_REG) {
            descriptor.type = baidu::sfl::DirectoryEntryType::REGULAR_FILE;
        } else if (entry->d_type == DT_LNK) {
            descriptor.type = baidu::sfl::DirectoryEntryType::SYMBOLIC_LINK;
        } else if (entry->d_type == DT_DIR) {
            descriptor.type = baidu::sfl::DirectoryEntryType::DIRECTORY;
        } else {
            descriptor.type = baidu::sfl::DirectoryEntryType::UNKNOWN;
        }

        if ((descriptor.name == "." || descriptor.name == "..") &&
            descriptor.type == baidu::sfl::DirectoryEntryType::DIRECTORY) {
            continue;
        }

        entries.push_back(std::move(descriptor));
    }

    RETURN_SUCCESS;
}

BRESULT EncryptedFileSystem::mkdir_internal(const std::string& directory) {
    BRESULT br;

    // We are not sure if the ::mkdir function can make any side effect on the
    // existing file or directory with the same name. So we test first. If the
    // name does not exist, we call the ::mkdir function.

    struct stat buffer;
    int result = ::stat(directory.c_str(), &buffer);
    if (result == 0) {
        if (S_ISDIR(buffer.st_mode)) {
            RETURN_SUCCESS;
        } else {
            br = baidu::sfl::linux_system_errno_to_bresult(ENOTDIR);
            RETURN_FAILURE(br);
        }
    }

    if (errno != ENOENT) {
        return baidu::sfl::get_last_error();
    }

    result = ::mkdir(directory.c_str(), S_IRWXU | S_IRWXG | S_IRWXO);
    if (result != 0) {
        return baidu::sfl::get_last_error();
    }

    RETURN_SUCCESS;
}

BRESULT EncryptedFileSystem::mkdir(
        const char* const path_name,
        const uint64_t mkdir_flags,
        const uint64_t timeout_us,
        const baidu::sfl::IUserOption* const user_option) {
    BRESULT br;

    std::ignore = timeout_us;
    std::ignore = user_option;

    if (!_initialized) {
        return baidu::sfl::BRESULT_FILE_SYSTEM_NOT_INITIALIZED;
    }

    if (path_name == nullptr) {
        return baidu::sfl::BRESULT_INVALID_ARGUMENT;
    }

    if ((mkdir_flags & ~baidu::sfl::MKDIR_FLAG_MASK) != 0) {
        return baidu::sfl::BRESULT_INVALID_ARGUMENT;
    }

    const std::string directory = path_name;

    for (std::string::size_type position = 0; position < directory.size(); ++position) {
        position = directory.find('/', position);
        if (position == std::string::npos) {
            return mkdir_internal(directory);
        } else {
            br = mkdir_internal(directory.substr(0, position + 1));
            RETURN_IF_FAILED(br);
        }
    }

    return BRESULT_OK;
}

BRESULT EncryptedFileSystem::remove_directory(std::string directory) {
    BRESULT br;

    LOG_AND_ASSERT(!directory.empty());

    if (directory.back() != '/') {
        directory += "/";
    }

    std::vector<baidu::sfl::DirectoryEntryDescriptor> entries;
    br = lsdir(directory.c_str(), UINT64_MAX, nullptr, entries);
    RETURN_IF_FAILED(br);

    for (const auto& element : entries) {
        if (element.type == baidu::sfl::DirectoryEntryType::DIRECTORY) {
            br = remove_directory(directory + element.name);
            RETURN_IF_FAILED(br);
        } else {
            const int result = std::remove((directory + element.name).c_str());
            if (result != 0) {
                return baidu::sfl::get_last_error();
            }
        }
    }

    const int result = std::remove(directory.c_str());
    if (result != 0) {
        return baidu::sfl::get_last_error();
    }

    RETURN_SUCCESS;
}

BRESULT EncryptedFileSystem::remove(
        const char* const path_name,
        const uint64_t remove_flags,
        const uint64_t timeout_us,
        const baidu::sfl::IUserOption* const user_option) {
    BRESULT br;

    std::ignore = timeout_us;
    std::ignore = user_option;

    if (!_initialized) {
        return baidu::sfl::BRESULT_FILE_SYSTEM_NOT_INITIALIZED;
    }

    if (path_name == nullptr) {
        return baidu::sfl::BRESULT_INVALID_ARGUMENT;
    }

    if ((remove_flags & ~baidu::sfl::REMOVE_FLAG_MASK) != 0) {
        return baidu::sfl::BRESULT_INVALID_ARGUMENT;
    }

    struct stat buffer;
    int result = ::stat(path_name, &buffer);
    if (result != 0) {
        return baidu::sfl::get_last_error();
    }

    if (S_ISDIR(buffer.st_mode)) {
        br = remove_directory(path_name);
        RETURN_IF_FAILED(br);
    } else {
        result = std::remove(path_name);
        if (result != 0) {
            return baidu::sfl::get_last_error();
        }
    }

    RETURN_SUCCESS;
}

BRESULT EncryptedFileSystem::truncate_lease(const char* const path_name) {
    // Posix does not support truncate_lease.
    std::ignore = path_name;
    return BRESULT_OK;
}

void EncryptedFileSystem::initialize_threadpool() {
    BRESULT br;

    std::string threadpool_name = _file_system_name + ".threadpool";
    _threadpool = baidu::sfl::make_unique<baidu::sfl::PrioritizedThreadpool>(std::move(threadpool_name));

    LOG_AND_ASSERT(_thread_count > 0);
    baidu::sfl::PrioritizedThreadpool::StartOption start_option;
    start_option.set_normal_thread_count(_thread_count);

    br = _threadpool->start(&start_option);
    LOG_AND_ASSERT(baidu::sfl::is_succeeded(br));
}

BRESULT EncryptedFileSystem::submit_task(baidu::sfl::IThreadpool::Task task) {
    BRESULT br;

    LOG_AND_ASSERT(_initialized);

    std::call_once(_once_flag, &EncryptedFileSystem::initialize_threadpool, this);

    br = _threadpool->submit(std::move(task), nullptr);
    RETURN_IF_FAILED(br);

    RETURN_SUCCESS;
}

BRESULT EncryptedFileSystem::read_sync(
        const baidu::sfl::FileHandle& file_handle,
        const uint64_t read_file_flags,
        void* const buffer,
        const uint64_t buffer_length,
        const uint64_t file_offset,
        uint64_t* const read_length) {
    if (read_length != nullptr) {
        *read_length = 0;
    }

    if (!_initialized) {
        return baidu::sfl::BRESULT_FILE_SYSTEM_NOT_INITIALIZED;
    }

    if (file_handle == nullptr) {
        return baidu::sfl::BRESULT_INVALID_FILE_HANDLE;
    }

    if ((read_file_flags & ~baidu::sfl::READ_FILE_FLAG_MASK) != 0) {
        return baidu::sfl::BRESULT_INVALID_ARGUMENT;
    }

    if (buffer == nullptr && buffer_length != 0) {
        return baidu::sfl::BRESULT_INVALID_ARGUMENT;
    }

    const auto encrypted_file = dynamic_cast<EncryptedFile*>(file_handle.get());

    if (encrypted_file == nullptr) {
        return baidu::sfl::BRESULT_INVALID_FILE_HANDLE;
    }

    {
        const baidu::sfl::SharedMutexSharedGuard guard(&encrypted_file->_shared_mutex);

        if (encrypted_file->_fd == -1) {
            return baidu::sfl::BRESULT_INVALID_FILE_HANDLE;
        }

        const auto open_file_flags = encrypted_file->_open_file_flags;
        if ((open_file_flags & baidu::sfl::OPEN_FILE_READ_WRITE_MASK) == baidu::sfl::OPEN_FILE_APPEND_ONLY) {
            return baidu::sfl::BRESULT_READ_APPEND_ONLY_FILE;
        }

        auto data = static_cast<char*>(buffer);
        uint64_t offset = 0;

        while (offset < buffer_length) {
            const ssize_t length =
                    ::pread(encrypted_file->_fd, data + offset, buffer_length - offset, file_offset + offset);
            if (length == -1) {
                return baidu::sfl::get_last_error();
            }

            LOG_AND_ASSERT(length >= 0);
            LOG_AND_ASSERT(static_cast<uint64_t>(length) <= buffer_length - offset);
            offset += length;

            if (read_length != nullptr) {
                *read_length = offset;
            }

            if (length == 0) {
                if (read_length != nullptr) {
                    RETURN_IF_FAILED(encrypted_file->decrypt(file_offset, data, *read_length));
                }
                return baidu::sfl::BRESULT_READ_FILE_END;
            }
        }
        if (read_length != nullptr) {
            RETURN_IF_FAILED(encrypted_file->decrypt(file_offset, data, *read_length));
        }
    }

    RETURN_SUCCESS;
}

BRESULT EncryptedFileSystem::read_async(
        const baidu::sfl::FileHandle& file_handle,
        const uint64_t read_file_flags,
        void* const buffer,
        const uint64_t buffer_length,
        const uint64_t file_offset,
        uint64_t* const read_length,
        baidu::sfl::AsyncCallback const callback,
        baidu::sfl::AsyncContext* const context) {
    // The file_handle (shared_ptr) is copied into lambda.
    auto task = [=](const bool stopped) {
        baidu::sfl::AsyncContext async_context(callback, context);
        LOG_AND_ASSERT(async_context.is_async());

        BRESULT& result = async_context.get_bresult_reference();

        if (stopped) {
            result = baidu::sfl::BRESULT_THREADPOOL_IS_STOPPED;
        } else {
            result = read_sync(file_handle, read_file_flags, buffer, buffer_length, file_offset, read_length);
        }
    };

    const BRESULT br = submit_task(std::move(task));

    if (baidu::sfl::is_failed(br)) {
        baidu::sfl::AsyncContext async_context(callback, context);
        LOG_AND_ASSERT(async_context.is_async());
        async_context.set_bresult(br);
    }

    return BRESULT_IO_PENDING;
}

}  // namespace mochow::common
