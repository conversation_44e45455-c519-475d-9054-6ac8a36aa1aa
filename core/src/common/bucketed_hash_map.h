/**
 * MOCHOW - A Vector Database System.
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON> (<PERSON><PERSON><PERSON><PERSON>@baidu.com)
 * Description: concurrent bucketed hashmap
 * Date: 2024/07/31
 */

#pragma once

#include <string>
#include <memory>
#include <vector>
#include <unordered_map>

#include "core/src/common/butex.h"
#include "core/src/common/flags.h"

namespace mochow::common {

template<typename KeyType,
         typename ValueType,
         typename KeyHash = std::hash<KeyType>,
         typename KeyEqual = std::equal_to<KeyType>>
class Bucket final {
public:
    Bucket() = default;
    ~Bucket() = default;

    void insert(const KeyType& key, const ValueType& value) {
        common::ScopedButex lock(_lock);
        _map[key] = value;
    }

    void erase(const KeyType& key) {
        common::ScopedButex lock(_lock);
        auto iter = _map.find(key);
        if (iter != _map.end()) {
            _map.erase(iter);
        }
    }

    bool get(const KeyType& key, ValueType& value) const {
        common::ScopedButex lock(_lock);
        auto iter = _map.find(key);
        if (iter != _map.end()) {
            value = iter->second;
            return true;
        }
        return false;
    }

    void clear() {
        common::ScopedButex lock(_lock);
        _map.clear();
    }

    size_t size() const {
        common::ScopedButex lock(_lock);
        return _map.size();
    }

public:
    using ForeachFunction = std::function<void(const KeyType&, const ValueType&)>;

    void foreach(ForeachFunction func) {
        common::ScopedButex lock(_lock);
        for (auto& pair : _map) {
            func(pair.first, pair.second);
        }
    }

private:
    mutable Butex _lock;
    std::unordered_map<KeyType, ValueType, KeyHash, KeyEqual> _map;
};

template<typename KeyType,
         typename ValueType,
         typename KeyHash = std::hash<KeyType>,
         typename KeyEqual = std::equal_to<KeyType>>
class BucketedHashMap final {
public:
    BucketedHashMap(const uint64_t bucket_count)
        : _bucket_count(bucket_count) {
        _buckets.resize(bucket_count);
        for (size_t i = 0; i < bucket_count; ++i) {
            _buckets[i] = std::make_shared<Bucket<KeyType, ValueType, KeyHash, KeyEqual>>();
        }
    }
    ~BucketedHashMap() = default;

    void insert(const KeyType& key, const ValueType& value) {
        size_t hash = _key_hash(key);
        _buckets[hash % _bucket_count]->insert(key, value);
    }

    void erase(const KeyType& key) {
        size_t hash = _key_hash(key);
        _buckets[hash % _bucket_count]->erase(key);
    }

    bool get(const KeyType& key, ValueType& value) const {
        size_t hash = _key_hash(key);
        return _buckets[hash % _bucket_count]->get(key, value);
    }

    size_t bucket_count() const {
        return _bucket_count;
    }

    size_t size() const {
        size_t size = 0;
        for (auto& bucket : _buckets) {
            size += bucket->size();
        }
        return size + sizeof(*this) + _bucket_count * sizeof(Bucket<KeyType, ValueType, KeyHash, KeyEqual>);
    }

    void clear() {
        for (auto& bucket : _buckets) {
            bucket->clear();
        }
    }

public:
    using ForeachFunction = std::function<void(const KeyType&, const ValueType&)>;

    void foreach(ForeachFunction func) {
        for (auto& bucket : _buckets) {
            bucket->foreach(func);
        }
    }

private:
    uint64_t _bucket_count;
    std::vector<std::shared_ptr<Bucket<KeyType, ValueType, KeyHash, KeyEqual>>> _buckets;
    KeyHash _key_hash;
};

template<typename KeyType, typename ValueType, typename KeyHash>
using BucketedHashMapPtr = std::unique_ptr<BucketedHashMap<KeyType, ValueType, KeyHash>>;
template<typename KeyType, typename ValueType, typename KeyHash>
using BucketedHashMapRef = std::shared_ptr<BucketedHashMap<KeyType, ValueType, KeyHash>>;

} // namespace mochow::common
