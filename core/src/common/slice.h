#pragma once

#include <string.h>
#include <string>
#include <rocksdb/slice.h>
#include <base/logging.h>

namespace mochow {
struct Slice {
    const char* _data;
    size_t _size;

    // Create an empty slice.
    Slice() : _data(""), _size(0) { }

    // Create a slice that refers to d[0,n-1].
    Slice(const char* d, size_t n) : _data(d), _size(n) { }

    // Create a slice that refers to the contents of "s"
    /* implicit */
    Slice(const std::string& s) : _data(s.data()), _size(s.size()) { }

    Slice(const rocksdb::Slice& s) : _data(s.data()), _size(s.size()) { }

    // Create a slice that refers to s[0,strlen(s)-1]
    /* implicit */
    Slice(const char* s) : _data(s), _size(strlen(s)) { }

    Slice(const Slice& other) {
        _data = other._data;
        _size = other._size;
    }

    Slice& operator= (const Slice& other) {
        if (this == &other) {
            return *this;
        }

        _data = other._data;
        _size = other._size;
        return *this;
    }

    ~Slice() { }

    // Return a pointer to the beginning of the referenced data
    const char* data() const { return _data; }

    // Return the length (in bytes) of the referenced data
    size_t size() const { return _size; }

    // Return true iff the length of the referenced data is zero
    bool empty() const { return _size == 0; }

    // Return the ith byte in the referenced data.
    // REQUIRES: n < size()
    char operator[](size_t n) const {
        assert(n < size());
        return _data[n];
    }

    // Change this slice to refer to an empty array
    void clear() {
        _data = "";
        _size = 0;
    }

    // Drop the first "n" bytes from this slice.
    void remove_prefix(size_t n) {
        assert(n <= size());
        _data += n;
        _size -= n;
    }

    void remove_suffix(size_t n) {
        assert(n <= size());
        _size -= n;
    }

    // Return a string that contains the copy of the referenced data.
    // when hex is true, returns a string of twice the length hex encoded (0-9A-F)
    std::string to_string(bool hex = false) const {
        std::string result;  // RVO/NRVO/move
        if (hex) {
            char buf[10];
            for (size_t i = 0; i < _size; i++) {
                snprintf(buf, 10, "%02X", (unsigned char)_data[i]);
                result += buf;
            }
            return result;
        } else {
            result.assign(_data, _size);
            return result;
        }
    }

    // Three-way comparison.  Returns value:
    //   <  0 iff "*this" <  "b",
    //   == 0 iff "*this" == "b",
    //   >  0 iff "*this" >  "b"
    int compare(const Slice& b) const {
        const size_t min_len = (_size < b._size) ? _size : b._size;
        int r = memcmp(_data, b._data, min_len);
        if (r == 0) {
            if (_size < b._size) {
                r = -1;
            } else if (_size > b._size) {
                r = +1;
            }
        }
        return r;
    }

    // Return true iff "x" is a prefix of "*this"
    bool starts_with(const Slice& x) const {
        return ((_size >= x._size) &&
                (memcmp(_data, x._data, x._size) == 0));
    }

    bool ends_with(const Slice& x) const {
        return ((_size >= x._size) &&
                (memcmp(_data + _size - x._size, x._data, x._size) == 0));
    }

    // Compare two slices and returns the first byte where they differ
    size_t difference_offset(const Slice& b) const {
        size_t off = 0;
        const size_t len = (_size < b._size) ? _size : b._size;
        for (; off < len; off++) {
            if (_data[off] != b._data[off]) {
                break;
            }
        }
        return off;
    }

    size_t find(char c, size_t pos = 0) const {
        if (_size <= 0 || pos >= _size) {
            return std::string::npos;
        }
        const char* result = static_cast<const char*>(
            memchr(_data + pos, c, _size - pos));
        return result != nullptr ? result - _data : std::string::npos;
    }

};

} //namespace mochow
