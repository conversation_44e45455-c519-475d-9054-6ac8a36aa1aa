/**
 * MOCHOW - A Vector Database System.
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON> (f<PERSON><PERSON><PERSON><PERSON>@baidu.com)
 * Date: 2023/10/20
 */

#include "core/src/common/common.h"
#include "core/src/common/task_scheduler.h"
#include "baidu/vdb/mochow/core/src/proto/error_code.pb.h"

namespace mochow::common {

void TaskScheduler::initialize_threadpool() {
    _threadpool = std::make_unique<common::BucketedThreadpool>(_name);

    common::BucketedThreadpool::StartOption start_option;
    start_option.set_normal_thread_count(_worker_count);
    start_option.set_bucket_count(_bucket_count);

    int code = _threadpool->start(&start_option);
    LOG_AND_ASSERT(code == OK);
    LOG(NOTICE) << "Success to initialize threadpool " << _name;
}

int TaskScheduler::submit_task(baidu::sfl::IThreadpool::Task task,
                               common::BucketedThreadpool::TaskOption* options) {

    std::call_once(_once_flag, &TaskScheduler::initialize_threadpool, this);

    int code = _threadpool->submit(std::move(task), std::move(options));
    if (code != OK) {
        LOG(WARNING) << "Fail to submit task to threadpool, error:" << code;
    }
    return code;
}

}
