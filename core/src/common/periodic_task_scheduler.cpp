#include "core/src/common/logging.h"
#include "core/src/common/bthread_executor.h"
#include "core/src/common/periodic_task_scheduler.h"

namespace mochow::common {

Status PeriodicTaskScheduler::register_task(
        const std::string& task_name, PeriodicTaskFunc task_func,
        const uint64_t repeat_period_seconds) {

    uint64_t repeat_period_us = repeat_period_seconds * MICROS_IN_SECOND;
    {
        common::ScopedMutexLock lock(_mutex);
        auto it = _task_map.find(task_name);
        if (it != _task_map.end()) {
            if (it->second->repeat_period_us == repeat_period_us) {
                return Status();
            }
            _task_map.erase(it);
        }
        uint64_t now = base::gettimeofday_us();
        uint64_t next_run_time_us = now + repeat_period_us;
        auto task = std::make_shared<TaskInfo>(task_func, task_name,
                next_run_time_us, repeat_period_us);
        _heap.push(task);
        _task_map.insert({task_name, task});
    }
    return Status();
}

Status PeriodicTaskScheduler::cancel_task(
        const std::string& task_name) {

    common::ScopedMutexLock lock(_mutex);
    auto it = _task_map.find(task_name); 
    if (it != _task_map.end()) {
        it->second->valid = false;
        _heap.remove(it->second);
        _task_map.erase(it);
    }
    return Status();
}

void PeriodicTaskScheduler::run() {
    while(_is_running) {
        TaskInfoRef current_task;
        bool empty = true;
        {
            common::ScopedMutexLock lock(_mutex);
            empty = _heap.empty();
        }
        if(empty) {
            bthread_usleep(MICROS_IN_SECOND);
            continue;
        }
        {
            common::ScopedMutexLock lock(_mutex);
            current_task = _heap.top();
            if (!current_task->is_valid()) {
                _heap.pop();
                _task_map.erase(current_task->name);
                continue;
            }
        }
        uint64_t now = base::gettimeofday_us();
        if (current_task->next_run_time_us <= now) {
            //execute periodic task
            LOG(TRACE) << "Start to run periodic task:" << current_task->name;
            auto executor = common::BthreadExecutor::instance();
            executor.add_urgent(current_task->func);
            {
                common::ScopedMutexLock lock(_mutex);
                if (current_task == _heap.top()) {
                    _heap.pop();
                }
                if (current_task->is_valid() && current_task->repeat_period_us > 0) {
                    current_task->next_run_time_us = now + current_task->repeat_period_us;
                    _heap.push(current_task);
                } else {
                    _task_map.erase(current_task->name);
                }
            }
        } else {
            bthread_usleep(MICROS_IN_SECOND);
        }
    }
}

}
