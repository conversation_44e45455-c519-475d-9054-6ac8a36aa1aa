/**
 * MOCHOW - A Vector Database System.
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON> (<EMAIL>)
 * Date: 2023/09/19
 * Desciption: Declarations for host address functions
 *
 */

#pragma once

#include <string>
#include <vector>
#include <ostream>
#include <base/endpoint.h>
#include "baidu/vdb/mochow/core/src/proto/common.pb.h"

namespace mochow::common {

enum class NameServiceProtocol : std::uint8_t {
    NONE = 0,
    BNS  = 1,
    LIST = 2,
    DNS  = 3,
};

static std::ostream& operator<<(std::ostream& os, NameServiceProtocol ns) {
    if (ns == NameServiceProtocol::NONE) { os << "NONE"; } else
    if (ns == NameServiceProtocol::BNS) { os << "BNS"; } else
    if (ns == NameServiceProtocol::LIST) { os << "LIST"; } else
    if (ns == NameServiceProtocol::DNS) { os << "DNS"; } else
    { os << "Unknown"; }
    return os;
}

// Get host ip list from DNS name
int get_host_ips_by_dns(const std::string& dns_name, std::vector<uint32_t>* ips);

// Get host ip list form BNS name
int get_host_ips_by_bns(const std::string& service_name, std::vector<base::EndPoint>* addrs);

// Get host ip list form List name
int get_host_ips_by_list(const std::string& service_name, std::vector<base::EndPoint>* addrs);

// Parse address like '[[http://][https://][bns://][list://]]$DNS_NAME_OR_BNS_NAME:$PORT', eg.:
//   BNS  - address should starts with 'bns://'
//   LIST - address should starts with 'list://'
//   DNS  - otherwise, also support address starts with 'http://' or 'https://'
int parse_address(const std::string& address, std::string* hostname,
                  NameServiceProtocol* ns_protocol = NULL);
int parse_address(const std::string& address, std::string* hostname, uint16_t *port,
                  NameServiceProtocol* ns_protocol = NULL);
int parse_address(const std::string& address, std::vector<base::EndPoint> *addrs,
                  NameServiceProtocol* ns_protocol = NULL);

// Get the ip from specificed interface
uint32_t get_host_ip_by_interface(const char* interface);

// Get first valid ip from interface list splitted by ','
uint32_t get_host_ip_by_interfaces(const char* _interfaces);

}
