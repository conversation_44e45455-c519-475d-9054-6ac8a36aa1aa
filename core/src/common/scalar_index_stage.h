/**
 * MOCHOW - A Vector Database System.
 * Copyright (c) 2024 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON> (caoshi<PERSON>@baidu.com)
 * Date: 2024/11/15
 * Desciption: Definitions for Scalar Index Stage
 *
 */

#pragma once

#include <string>

namespace mochow::common {

class ScalarIndexStage final {
public:
    static constexpr const char* INVALID    = "INVALID";
    static constexpr const char* BUILDING   = "BUILDING";
    static constexpr const char* AVAILABLE  = "AVAILABLE";
    static constexpr const char* FAILED     = "FAILED";

    static inline bool is_valid_index_stage(const std::string& stage) {
        return stage == BUILDING || stage == AVAILABLE;
    }

private:
    ScalarIndexStage() = default;
    ~ScalarIndexStage() = default;
};

}
