/***************************************************************************
 *
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

#pragma once

#include <string>

namespace mochow::common {

class DataNodeState final {
public:
    static constexpr const char* DISABLED = "DISABLED";
    static constexpr const char* NORMAL = "NORMAL";

    static inline bool is_valid(const std::string& state) {
        return state == DISABLED || state == NORMAL;
    }

private:
    DataNodeState() = default;
    ~DataNodeState() = default;
};

}
