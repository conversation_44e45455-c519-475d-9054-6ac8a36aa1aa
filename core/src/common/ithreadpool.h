/***************************************************************************
 *
 * Copyright (c) 2018 Baidu.com, Inc. All Rights Reserved
 *
 * All sfl threadpools can be used in the same way for the sake of simplicity.
 * Every threadpool has three states (UNINITIALIZED, RUNNING and STOPPED) and
 * three operations (start, submit task and stop). These three operations are
 * thread safe. For example, thread A can stop a threadpool while thread B is
 * submitting a task to this threadpool. The state transition is as follows.
 *
 *                                                   stop
 *                                                 -------
 *                                                 |     |
 *                                                 |     V
 *  construct   -----------------      stop      -----------  destruct
 * -----------> | UNINITIALIZED | -------------> | STOPPED | ---------->
 *              -----------------                -----------
 *                     |                             ^
 *                     |          -----------        |
 *                     ---------> | RUNNING | --------
 *                       start    -----------   stop
 *                                  |     ^
 *                                  |     |
 *                                  -------
 *                                submit task
 *
 **************************************************************************/

#pragma once

#include <cstdint>
#include <functional>
#include <memory>

#include "sfl/class_helper.h"

namespace mochow::common {

enum class Priority : uint8_t {
    IDLE      = 0,
    LOW       = 1,
    MEDIUM    = 2,
    HIGH      = 3,
    REAL_TIME = 4
};

class IThreadpool {
public:
    IThreadpool() = default;

    virtual ~IThreadpool() = default;

    DISABLE_COPY_AND_MOVE(IThreadpool)

    class IStartOption {
    public:
        IStartOption() = default;

        virtual ~IStartOption() = default;

        DISABLE_COPY_AND_MOVE(IStartOption)
    };

    class ITaskOption {
    public:
        ITaskOption() = default;

        virtual ~ITaskOption() = default;

        DISABLE_COPY_AND_MOVE(ITaskOption)
    };

    using Task = std::function<void(bool)>;
    using TaskRef = std::shared_ptr<Task>;

    virtual int start(const IStartOption* const option) = 0;

    virtual void stop() = 0;

    virtual int submit(Task task, const ITaskOption* const option) = 0;

    // This function returns the number of the tasks that have been submitted to
    // the threadpool, but not started execution by any thread of the threadpool.
    virtual uint64_t get_pending_task_count() const = 0;

    // This function computes the execution time of every task being executed by
    // a threadpool thread and it returns the maximum one of the execution times.
    // If there is no task being executed, this function returns zero.
    virtual uint64_t get_max_task_execution_time_us() const = 0;
};

VERIFY_INTERFACE(IThreadpool)

VERIFY_INTERFACE(IThreadpool::IStartOption)

VERIFY_INTERFACE(IThreadpool::ITaskOption)

}
