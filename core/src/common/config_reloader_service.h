/**
 * MOCHOW - A Vector Database System.
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON> (<EMAIL>)
 * Date: 2023/11/17
 * Desciption: Declarations for Config Reloader Service
 *
 */

#pragma once

#include "baidu/vdb/mochow/core/src/proto/common.pb.h"

namespace mochow::common {

class ConfigReloaderServiceImpl : public pb::ConfigReloaderService {
public:
    ConfigReloaderServiceImpl() = default;
    ~ConfigReloaderServiceImpl() override = default;

    void reload_conf(::google::protobuf::RpcController* controller,
        const pb::UpdateConfRequest* request,
        pb::AckResponse* response,
        ::google::protobuf::Closure* done);

    void update_conf(::google::protobuf::RpcController* controller,
        const pb::UpdateConfRequest* request,
        pb::AckResponse* response,
        ::google::protobuf::Closure* done);
};

}
