/**
 * MOCHOW - A Vector Database System.
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved.
 *
 */

#pragma once

#include <deque>
#include <set>
#include <string>
#include <sstream>
#include <vector>
#include <pthread.h>
#include <unistd.h>
#include <boost/function.hpp>
#include <base/time.h>
#include <base/scoped_lock.h>
#include <base/logging.h>
#include <bvar/latency_recorder.h>
#include <base/memory/ref_counted.h>
#include <base/threading/platform_thread.h>

namespace mochow::common {

class ThreadPool : public base::RefCountedThreadSafe<ThreadPool> {
public:
    ThreadPool(const std::string& name, int thread_num = 8)
        : _name(name),
        _thread_num(thread_num),
        _stop(true),
        _last_job_id(0) {
        pthread_mutex_init(&_mutex, NULL);
        pthread_cond_init(&_cond, NULL);
    }
    ~ThreadPool() {
        pthread_cond_destroy(&_cond);
        pthread_mutex_destroy(&_mutex);
    }

    typedef boost::function<void ()> Task;

    enum Priority {
        HIGH_PRIORITY = 0,
        MEDIUM_PRIORITY = 1,
        LOW_PRIORITY = 2,
    };

    int start() {
        std::lock_guard<pthread_mutex_t> guard(_mutex);
        if (_threads.size()) {
            LOG(WARNING) << "Threadpool " << _name << " already started";
            return 0;
        }
        _stop = false;
        for (int i = 0; i < _thread_num; i++) {
            pthread_t tid;
            if (0 != pthread_create(&tid, NULL, thread_func, this)) {
                LOG(FATAL) << "Threadpool " << _name << " create pthread failed,"
                           << " errno:" << errno;
                ::abort();
            }
            _threads.push_back(tid);
            AddRef();
        }
        LOG(NOTICE) << "Threadpool " << _name << " startup";
        _queue_latency.expose(_name, "queue");
        _proc_latency.expose(_name, "proc");
        return 0;
    }

    void stop() {
        // signal stop
        std::lock_guard<pthread_mutex_t> guard(_mutex);
        if (_stop) {
            return;
        }
        _stop = true;
        pthread_cond_broadcast(&_cond);
        LOG(NOTICE) << "Threadpool " << _name << " stopped";
    }

    void join() {
        for (size_t i = 0; i < _threads.size(); i++) {
            pthread_join(_threads[i], NULL);
        }
        _threads.clear();
        LOG(NOTICE) << "Threadpool " << _name << " joined";
    }

    int64_t submit(const Task& task, Priority priority) {
        std::lock_guard<pthread_mutex_t> guard(_mutex);
        if (_stop) {
            return -EINVAL;
        }

        int64_t job_id = ++_last_job_id; // start from 1
        Job item(job_id, task);
        switch (priority) {
        case LOW_PRIORITY:
            _low_queue.push_back(item);
            break;
        case MEDIUM_PRIORITY:
            _medium_queue.push_back(item);
            break;
        case HIGH_PRIORITY:
            _high_queue.push_back(item);
            break;
        default:
            LOG(ERROR) << "Threadpool " << _name << " fail to submit task due to unknown priority " << priority;
            return -EINVAL;
        }
        _pending_jobs.insert(job_id);

        pthread_cond_signal(&_cond);
        return job_id;
    }

    int cancel(int64_t job_id) {
        if (job_id <= 0) {
            return -EINVAL;
        }

        std::lock_guard<pthread_mutex_t> guard(_mutex);
        // retry cancel will ok
        if (_canceled_jobs.find(job_id) != _canceled_jobs.end()) {
            return -ECANCELED;
        }
        // in running job set, can't cancel
        if (_running_jobs.find(job_id) != _running_jobs.end()) {
            return -EEXIST;
        }
        // not in pending job set, can't cancel
        if (_pending_jobs.find(job_id) == _pending_jobs.end()) {
            return -ENOENT;
        }
        _canceled_jobs.insert(job_id);

        return 0;
    }

    size_t size() {
        std::lock_guard<pthread_mutex_t> guard(_mutex);
        return  _high_queue.size() + _medium_queue.size() + _low_queue.size();
    }

    void print() {
        std::stringstream ss;
        {
            std::lock_guard<pthread_mutex_t> guard(_mutex);
            ss << "ThreadPool(" << _name << ")"
                << " total: " << _high_queue.size() + _medium_queue.size() + _low_queue.size()
                << " high: " << _high_queue.size()
                << " medium: " << _medium_queue.size()
                << " low: " << _low_queue.size();
        }

        LOG(NOTICE) << ss.str();
    }
private:
    DISALLOW_COPY_AND_ASSIGN(ThreadPool);
    friend class base::RefCountedThreadSafe<ThreadPool>;

    struct Job {
        int64_t id;
        Task task;
        int64_t timestamp;

        Job() : id(0), timestamp(0) {}
        Job(int64_t id_, const Task& task_)
            : id(id_), task(task_) {
            timestamp = base::monotonic_time_us();
        }
    };

    static void* thread_func(void* arg) {
        reinterpret_cast<ThreadPool*>(arg)->thread_loop();
        return NULL;
    }

    void thread_loop() {
        int64_t running_id = 0;
        LOG(NOTICE) << "Thread " << base::PlatformThread::CurrentId()
                    << " in Threadpool(" << _name << ") start";
        while (true) {
            Job item;
            {
                std::lock_guard<pthread_mutex_t> guard(_mutex);
                // erase running
                if (running_id > 0) {
                    _running_jobs.erase(running_id);
                }

                // all queue is empty, and not stop, wait for jobs or stop
                while (!_stop &&
                       _high_queue.empty() && _medium_queue.empty() && _low_queue.empty()) {
                    // wait 500ms
                    struct timespec ts = base::milliseconds_from_now(500);
                    pthread_cond_timedwait(&_cond, &_mutex, &ts);
                }
                // finish when stop and all queue consumed
                if (_stop &&
                    _high_queue.empty() && _medium_queue.empty() && _low_queue.empty()) {
                    break;
                }

                // get job
                if (_high_queue.size() > 0) {
                    item = _high_queue.front();
                    _high_queue.pop_front();
                } else if (_medium_queue.size() > 0) {
                    item = _medium_queue.front();
                    _medium_queue.pop_front();
                } else if (_low_queue.size() > 0) {
                    item = _low_queue.front();
                    _low_queue.pop_front();
                }

                // remove from pending jobs
                _pending_jobs.erase(item.id);

                // check cancel, Task owned by caller
                std::set<int64_t>::iterator cancel_it = _canceled_jobs.find(item.id);
                if (cancel_it != _canceled_jobs.end()) {
                    _canceled_jobs.erase(cancel_it);
                    running_id = 0;
                    continue;
                }

                // add running
                running_id = item.id;
                _running_jobs.insert(running_id);
            }
            int64_t start_time = base::monotonic_time_us();
            item.task();
            int64_t end_time = base::monotonic_time_us();

            _queue_latency << (start_time - item.timestamp);
            _proc_latency << (end_time - start_time);
        }
        LOG(NOTICE) << "Thread " << base::PlatformThread::CurrentId()
                    << " in Threadpool(" << _name << ") stop";
        Release();
    }

    std::string _name;
    int _thread_num;
    bool _stop;
    pthread_cond_t _cond;
    pthread_mutex_t _mutex;
    std::vector<pthread_t> _threads;

    int64_t _last_job_id;
    std::deque<Job> _high_queue;
    std::deque<Job> _medium_queue;
    std::deque<Job> _low_queue;
    std::set<int64_t> _canceled_jobs;
    std::set<int64_t> _running_jobs;
    std::set<int64_t> _pending_jobs;

    bvar::LatencyRecorder _queue_latency;
    bvar::LatencyRecorder _proc_latency;
};

}
