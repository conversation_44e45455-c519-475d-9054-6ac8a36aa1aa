/***************************************************************************
 *
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

#pragma once

#include <string>
#include <sstream>
#include <iomanip>
#include <memory>
#include <thread>
#include <uconv.h>
#include <sys/statvfs.h>
#include <sys/sysmacros.h>
#include <base/crc32c.h>
#include <base/files/scoped_file.h>
#include <base/third_party/murmurhash3/murmurhash3.h>
#include "core/src/common/using.h"
#include "core/src/common/flags.h"
#include "baidu/vdb/mochow/core/src/proto/common.pb.h"

namespace mochow {

struct MemInfo {
    uint64_t mem_total;
    uint64_t mem_free;
    uint64_t mem_cached;
    uint64_t mem_buffers;
};

std::string mochow_logo();

void mem_stat(MemInfo* mem_info);

float mem_usage();

size_t get_total_mem_size_in_mb();

size_t get_available_mem_size_in_bytes();

std::string uint64_to_byteunit(uint64_t n);

std::string int64_to_byteunit(int64_t n);

bool byteunit_to_uint64(const std::string& n, uint64_t* result);

std::string generate_uuid();

std::string us2string(uint64_t time_us);

bool starts_with(const std::string& str, const std::string& prefix);

bool find_device_from_path(const std::string& disk_path,
                           std::string* dev_name, dev_t* dev_no);

inline uint32_t crc32(const void* key, size_t len) {
    return base::crc32c::Value((const char*)key, len);
}

inline uint32_t murmurhash32(const void *key, int len) {
    uint32_t hash = 0;
    base::MurmurHash3_x86_32(key, len, 0, &hash);
    return hash;
}

static inline uint32_t get_thread_count(const uint32_t total_thread_count, double percentage) {
    const uint32_t total_thread_num = (total_thread_count > 0) ? total_thread_count : std::thread::hardware_concurrency();
    const auto thread_num = static_cast<uint32_t>(std::ceil(total_thread_num * percentage));
    return std::max(1U, thread_num);
}

inline std::string binary_to_hex_string(const unsigned char* binary, size_t size) {
    std::ostringstream oss;
    for (size_t i = 0; i < size; ++i) {
        oss << std::setw(2) << std::setfill('0') << std::hex << static_cast<int>(binary[i]);
    }
    return oss.str();
}

inline std::string time2str(uint64_t time_s) {
    char buf[256];
    time_t rawtime = time_s;
    struct tm* timeinfo = localtime(&rawtime);
    strftime(buf, 256, "%Y-%m-%d %H:%M:%S", timeinfo);
    return std::string(buf);
}

static inline TabletID make_tablet_id(const mochow::pb::PTabletId& id) {
    return make_tablet_id(id.table_id(), id.tp_id());
}

static inline void tablet_id_2pb(const TabletID long_id, mochow::pb::PTabletId* id) {
    id->set_table_id(tblid(long_id));
    id->set_tp_id(tpid(long_id));
}

inline std::string time2utcstr(time_t time) {
    struct tm* utc_tm = gmtime(&time);
    char buffer[30];
    strftime(buffer, sizeof(buffer), "%Y-%m-%dT%H:%M:%SZ", utc_tm);
    return std::string(buffer);
}

class UconvUtils {
public:
    // ascii str is considered as gb18030 str
    static bool is_gb18030_str(const char* str, size_t len) {
        size_t i = 0;
        while (i < len) {
            if (IS_ASCII(str[i])) { // one byte case
                i += 1;
            } else if (LEGAL_GBK_FIRST_BYTE(str[i])) {
                // two bytes case
                if (i + 1 < len && LEGAL_GBK_2BYTE_SECOND_BYTE(str[i + 1])) {
                    i += 2;
                }
                // four bytes case
                else if (i + 3 < len
                        && LEGAL_GBK_4BYTE_SECOND_BYTE(str[i + 1])
                        && LEGAL_GBK_4BYTE_THIRD_BYTE(str[i + 2])
                        && LEGAL_GBK_4BYTE_FORTH_BYTE(str[i + 3])) {
                    i += 4;
                } else {
                    return false;
                }

            } else {
                return false;
            }
        }

        return true;
    }

    static bool utf2gbk(const std::string& src_str, std::string& gbk_str) {
        // check if src_str is utf8
        if (is_utf8(src_str.c_str(), src_str.length(), 0) != 0) {
            // convert from utf8 to gbk
            uint32_t gbk_len_max = src_str.length() + 1;
            auto buf = std::make_unique<char[]>(gbk_len_max);
            int gbk_len = utf8_to_gbk((const char*)src_str.c_str(), src_str.length(),
                        buf.get(), gbk_len_max, UCONV_INVCHAR_ERROR);
            if (gbk_len == -1) {
                return false;
            }
            gbk_str = std::string(reinterpret_cast<char*>(buf.get()), gbk_len);
        } else if (uconv_is_gbk(src_str.c_str())) {
            gbk_str = src_str;
        } else {
            return false;
        }
        return true;
    }

    static bool utf2gb18030(const std::string& src_str, std::string& gb18030_str) {
        // check if src_str is utf8
        if (is_utf8(src_str.c_str(), src_str.length(), 0) != 0) {
            uint32_t gb18030_len_max = src_str.length() + 1;
            auto buf = std::make_unique<unsigned char[]>(gb18030_len_max);
            int gb18030_len = utf8_to_gb18030((const unsigned char*)src_str.c_str(), src_str.length(),
                        buf.get(), gb18030_len_max, UCONV_INVCHAR_ERROR);
            if (gb18030_len == -1) {
                return false;
            }
            gb18030_str = std::string(reinterpret_cast<char*>(buf.get()), gb18030_len);
        } else if (is_gb18030_str(src_str.c_str(), src_str.length())) {
            gb18030_str = src_str;
        } else {
            return false;
        }
        return true;
    }
};

inline size_t get_bucket_count() {
    return (FLAGS_default_bimap_bucket_count == 0)
           ? (4 * std::thread::hardware_concurrency())
           : FLAGS_default_bimap_bucket_count;
}

}
