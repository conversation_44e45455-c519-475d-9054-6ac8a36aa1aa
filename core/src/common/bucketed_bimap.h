/**
 * MOCHOW - A Vector Database System.
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON> (<PERSON><PERSON><PERSON><PERSON>@baidu.com)
 * Description: concurrent bucketed bimap
 * Date: 2024/07/31
 */

#pragma once

#include <string>
#include <memory>
#include <vector>
#include <unordered_map>
#include <functional>

#include "core/src/common/logging.h"

#include "core/src/common/butex.h"
#include "core/src/common/flags.h"

namespace mochow::common {

template<
    typename KeyType,
    typename ValueType,
    typename KeyHash = std::hash<KeyType>,
    typename ValueHash = std::hash<ValueType>,
    typename KeyEqual = std::equal_to<KeyType>,
    typename ValueEqual = std::equal_to<ValueType>>
class BucketedBiMap final {
public:
    BucketedBiMap(const uint64_t bucket_count)
        : _forward_bucket_count(bucket_count) , _reverse_bucket_count(bucket_count) {
        _forward_buckets.resize(_forward_bucket_count);
        for (size_t i = 0; i < _forward_bucket_count; ++i) {
            _forward_buckets[i] = std::make_shared<Bucket<KeyType, ValueType, KeyHash, KeyEqual>>();
        }
        _reverse_buckets.resize(_reverse_bucket_count);
        for (size_t i = 0; i < _reverse_bucket_count; ++i) {
            _reverse_buckets[i] = std::make_shared<Bucket<ValueType, KeyType, ValueHash, ValueEqual>>();
        }
    }

    ~BucketedBiMap() = default;

    void insert(const KeyType& key, const ValueType& value) {
        size_t key_hash = _key_hash(key);
        size_t key_bucket_idx = key_hash % _forward_bucket_count;
        size_t value_hash = _value_hash(value);
        size_t value_bucket_idx = value_hash % _reverse_bucket_count;

        _forward_buckets[key_bucket_idx]->lock.lock();
        _reverse_buckets[value_bucket_idx]->lock.lock();

        _forward_buckets[key_bucket_idx]->map[key] = value;
        _reverse_buckets[value_bucket_idx]->map[value] = key;

        _reverse_buckets[value_bucket_idx]->lock.unlock();
        _forward_buckets[key_bucket_idx]->lock.unlock();
    }

    void erase(const KeyType& key) {
        ValueType value;
        bool b = this->get(key, value);
        if (!b) {
            return; 
        }

        size_t key_hash = _key_hash(key);
        size_t key_bucket_idx = key_hash % _forward_bucket_count;
        size_t value_hash = _value_hash(value);
        size_t value_bucket_idx = value_hash % _reverse_bucket_count;

        _forward_buckets[key_bucket_idx]->lock.lock();
        _reverse_buckets[value_bucket_idx]->lock.lock();

        {
            auto iter = _forward_buckets[key_bucket_idx]->map.find(key);
            if (iter != _forward_buckets[key_bucket_idx]->map.end()) {
                _forward_buckets[key_bucket_idx]->map.erase(iter);
            }
        }
        {
            auto iter = _reverse_buckets[value_bucket_idx]->map.find(value);
            if (iter != _reverse_buckets[value_bucket_idx]->map.end()) {
                _reverse_buckets[value_bucket_idx]->map.erase(iter);
            }
        }

        _reverse_buckets[value_bucket_idx]->lock.unlock();
        _forward_buckets[key_bucket_idx]->lock.unlock();
    }

    void rerase(const ValueType& value) {
        KeyType key;
        bool b = this->rget(value, key);
        if (!b) {
            return; 
        }

        size_t key_hash = _key_hash(key);
        size_t key_bucket_idx = key_hash % _forward_bucket_count;
        size_t value_hash = _value_hash(value);
        size_t value_bucket_idx = value_hash % _reverse_bucket_count;

        _forward_buckets[key_bucket_idx]->lock.lock();
        _reverse_buckets[value_bucket_idx]->lock.lock();

        {
            auto iter = _forward_buckets[key_bucket_idx]->map.find(key);
            if (iter != _forward_buckets[key_bucket_idx]->map.end()) {
                _forward_buckets[key_bucket_idx]->map.erase(iter);
            }
        }
        {
            auto iter = _reverse_buckets[value_bucket_idx]->map.find(value);
            if (iter != _reverse_buckets[value_bucket_idx]->map.end()) {
                _reverse_buckets[value_bucket_idx]->map.erase(iter);
            }
        }

        _reverse_buckets[value_bucket_idx]->lock.unlock();
        _forward_buckets[key_bucket_idx]->lock.unlock();
    }

    bool get(const KeyType& key, ValueType& value) const {
        size_t key_hash = _key_hash(key);
        size_t key_bucket_idx = key_hash % _forward_bucket_count;

        common::ScopedButex lock(_forward_buckets[key_bucket_idx]->lock);
        {
            auto iter = _forward_buckets[key_bucket_idx]->map.find(key);
            if (iter != _forward_buckets[key_bucket_idx]->map.end()) {
                value = iter->second;
                return true;
            }
        }
        return false;
    }

    bool rget(const ValueType& value, KeyType& key) const {
        size_t value_hash = _value_hash(value);
        size_t value_bucket_idx = value_hash % _reverse_bucket_count;

        common::ScopedButex lock(_reverse_buckets[value_bucket_idx]->lock);
        {
            auto iter = _reverse_buckets[value_bucket_idx]->map.find(value);
            if (iter != _reverse_buckets[value_bucket_idx]->map.end()) {
                key = iter->second;
                return true;
            }
        }
        return false;
    }

    size_t forward_bucket_count() const {
        return _forward_bucket_count;
    }

    size_t reverse_bucket_count() const {
        return _reverse_bucket_count;
    }

public:
    using ForeachFunction = std::function<void(const KeyType&, const ValueType&)>;
    using ReverseForeachFunction = std::function<void(const ValueType&, const KeyType&)>;

    void foreach(ForeachFunction func) const {
        for (auto& forward_bucket : _forward_buckets) {
            common::ScopedButex lock(forward_bucket->lock);
            for (auto& pair : forward_bucket->map) {
                func(pair.first, pair.second);
            }
        }
    }

    void rforeach(ReverseForeachFunction func) {
        for (auto& reverse_bucket : _reverse_buckets) {
            common::ScopedButex lock(reverse_bucket->lock);
            for (auto& pair : reverse_bucket->map) {
                func(pair.first, pair.second);
            }
        }
    }

public:
    template<typename T,
             typename U,
             typename THash = std::hash<T>,
             typename TEqual = std::equal_to<T>>
    class Bucket final {
    public:
        Bucket() = default;
        ~Bucket() = default;

        mutable Butex lock;
        std::unordered_map<T, U, THash, TEqual> map;
    };

private:
    uint64_t _forward_bucket_count;
    uint64_t _reverse_bucket_count;
    std::vector<std::shared_ptr<Bucket<KeyType, ValueType, KeyHash, KeyEqual>>> _forward_buckets{};
    std::vector<std::shared_ptr<Bucket<ValueType, KeyType, ValueHash, ValueEqual>>> _reverse_buckets{};

    KeyHash _key_hash{};
    ValueHash _value_hash{};
};

template<typename KeyType, typename ValueType, typename KeyHash, typename ValueHash>
using BucketedBiMapPtr = std::unique_ptr<BucketedBiMap<KeyType, ValueType, KeyHash, ValueHash>>;
template<typename KeyType, typename ValueType, typename KeyHash, typename ValueHash>
using BucketedBiMapRef = std::shared_ptr<BucketedBiMap<KeyType, ValueType, KeyHash, ValueHash>>;

} // namespace mochow::common
