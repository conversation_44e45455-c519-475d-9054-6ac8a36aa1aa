/**
 * MOCHOW - A Vector Database System.
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON> (<EMAIL>)
 * Date: 2023/09/26
 * Desciption: Definitions for using types
 *
 */

#pragma once

#include <cstdint>
#include <string>
#include <sstream>

namespace mochow {

// DBID & TBLID use same ID sequence.
// 5 bytes (1 trillion) should be enough.
using DBID = uint64_t;
using TBLID = uint64_t;
constexpr const DBID  DBID_MAX  = (1ULL << 40) - 1;
constexpr const DBID  DBID_MIN  = 1;
constexpr const TBLID TBLID_MAX = (1ULL << 40) - 1;
constexpr const TBLID TBLID_MIN = 1;

// Table Partition ID, 3-bytes should be enough
using TPID = uint32_t;
constexpr const TPID  TPID_MAX  = (1ULL << 24) - 1;
constexpr const TPID  TPID_MIN  = 0;

// TabletID = TBLID (5-bytes) + TPID (3-bytes)
using TabletID = uint64_t;

// COLID & CFID use same ID sequence inside a table.
// 3 bytes (16M) should be enough.
using COLID = uint32_t;
using CFID = uint32_t;
constexpr const COLID COLID_MAX = (1ULL << 24) - 1;
constexpr const CFID  CFID_MAX  = (1ULL << 24) - 1;

using IDXID = uint32_t;
constexpr const IDXID IDXID_MAX = (1ULL << 24) - 1;

using DIM = uint32_t;

// INCID use 5 bytes (1 trillion) should be enough.
using INCID = uint64_t;
constexpr const INCID INCID_MAX = (1ULL << 40) - 1;
// to specify an invalid inc_id. used in ann/knn search.
constexpr const INCID INVALID_INCID = UINT64_MAX;

using SEGID = uint64_t;
constexpr const SEGID SEGID_MAX = UINT64_MAX;

using ROWID = INCID;

constexpr const uint64_t DEFAULT_TTL = 0;

// major version: increase while user trigger major compaction.
// persist in master.
using IDXMAJOR = uint32_t;
// minor version: increase while moss auto rebuilding index.
using IDXMINOR = uint32_t;
// minor version start from 1 to distinguish from default index version, which
// has minor version = 0.
constexpr const IDXMINOR IDXMINOR_MIN = 1;
// IDXVERSION = index major version + index minor version
//
// difference between index id and index version:
// an index id refers to a specific index schema, e.g. HNSW to column 'col1'.
// an index version refers to a specific index instance. e.g. the third HNSW
// vector index for column 'col1'. an index id matches several index version.
using IDXVERSION = uint64_t;

// 0 is the default value. both major and minor version should start from 1.
constexpr const IDXVERSION IDXVERSION_DEFAULT = 0;

// for trace id.
using TRACEID = uint64_t;

// 0 is the default value.
constexpr const TRACEID TRACEID_DEFAULT = 0;


constexpr inline bool is_valid_incid(INCID inc) {
    return inc <= INCID_MAX;
}

static inline TabletID make_tablet_id(const TBLID table_id, const TPID tp_id) {
    return (table_id << 24) + tp_id;
}

static inline TBLID tblid(const TabletID tablet_id) {
    return (tablet_id >> 24);
}

static inline TPID tpid(const TabletID tablet_id) {
    return ((tablet_id << 40) >> 40);
}

static inline INCID incid(const uint64_t increment_key) {
    return static_cast<INCID>((increment_key << 24) >> 24);
}

static inline IDXVERSION make_index_version(const IDXMAJOR major, const IDXMINOR minor) {
    return ((uint64_t)major << 32) + minor;
}

static inline IDXMAJOR major_index_version(const IDXVERSION version) {
    return static_cast<IDXMAJOR>(version >> 32);
}

static inline IDXMINOR minor_index_version(const IDXVERSION version) {
    return static_cast<IDXMINOR>((version << 32) >> 32);
}

static inline uint64_t make_increment_key(const TPID tp_id, const INCID incid) {
    return ((uint64_t)tp_id << 40) + incid;
}
}
