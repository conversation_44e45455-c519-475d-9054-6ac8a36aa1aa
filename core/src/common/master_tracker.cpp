/**
 * MOCHOW - A Vector Database System.
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved.
 *
 */

#include <iomanip>
#include <netdb.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <map>

#include <boost/bind.hpp>
#include <base/fast_rand.h>
#include <baidu/rpc/controller.h>
#include <baidu/rpc/channel.h>

#include "baidu/vdb/mochow/core/src/proto/common.pb.h"
#include "baidu/vdb/mochow/core/src/proto/master.pb.h"
#include "core/src/common/master_tracker.h"
#include "core/src/common/rpc/rpc_stub.h"

namespace mochow::common {

#ifndef UNIT_TEST
DEFINE_int32(refresh_master_interval_s, 30, "refresh master interval in second");
#else
DEFINE_int32(refresh_master_interval_s, 3, "refresh master interval in second");
#endif

int MasterTracker::start(const base::EndPoint& local_addr, std::string token, std::string master_addr,
                         uint32_t refresh_master_interval_s) {
    if (FLAGS_master_address.empty() && master_addr.empty()) {
        LOG(ERROR) << "Fail to start MasterTracker due to master address is empty!";
        return -1;
    }
    auto addr = master_addr.empty() ? FLAGS_master_address: master_addr;
    return LeaderTracker::start(local_addr, token, addr, refresh_master_interval_s);
}

base::EndPoint MasterTracker::get_master() {
    return get_leader();
}

std::vector<base::EndPoint> MasterTracker::get_master_list() {
    return get_list();
}

void MasterTracker::get_backup_masters(const base::EndPoint& addr, std::vector<base::EndPoint>* backup_addrs) {
    return get_followers(addr, backup_addrs);
}

void MasterTracker::set_master(const base::EndPoint& addr, std::vector<base::EndPoint>* addrs) {
    set_leader_and_followers(addr, addrs);
}

void MasterTracker::check_leader(const std::vector<base::EndPoint>& addrs,
        std::map<base::EndPoint, int>* candidates) {
    for (size_t i = 0; i < addrs.size(); i++) {
        /*
        baidu::rpc::Controller cntl;
        baidu::rpc::ChannelOptions options;
        options.connection_type = baidu::rpc::CONNECTION_TYPE_SINGLE;
        options.max_retry = 0;
        options.connect_timeout_ms = 3000;
        options.timeout_ms = 5000;

        baidu::rpc::Channel channel;
        if (0 != channel.Init(addrs[i], &options)) {
            continue;
        }*/

        mochow::master::pb::CheckLeaderRequest request;
        request.set_req_addr(common::endpoint2int(_local_addr));
        mochow::master::pb::CheckLeaderResponse response;
        common::MasterStub stub;
        common::RpcCallOptions options;
        options.need_retry = false;
        options.print_trace_log_on_final_success = true;
        common::SynchronizedClosure closure;
        stub.check_leader(addrs[i], &request, &response, &closure, &options);
        closure.wait();

        if (0 == response.status().code()) {
            auto it = candidates->find(addrs[i]);
            if (it != candidates->end()) {
                it->second += 1;
            } else {
                candidates->insert(std::make_pair(addrs[i], 1));
            }
        } else if (response.status().has_leader_hint()) {
            mochow::pb::NodeId leader_addr = response.status().leader_hint();
            auto addr = common::node2endpoint(leader_addr);
            auto it = candidates->find(addr);
            if (it != candidates->end()) {
                it->second += 1;
            } else {
                candidates->insert(std::make_pair(addr, 1));
            }
        }
    }
}

bool is_master_tracker_started() {
    return g_master_tracker->is_started();
}

int start_master_tracker(const base::EndPoint& local_addr,
                         std::string token,
                         std::string master_addr,
                         int32_t refresh_master_interval_s) {
    g_master_tracker->AddRef();
    return g_master_tracker->start(local_addr, token, master_addr, refresh_master_interval_s);
}

void stop_master_tracker() {
    g_master_tracker->stop();
    g_master_tracker->Release();
}

}

