/**
 * MOCHOW - A Vector Database System.
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON> (<EMAIL>)
 * Date: 2023/10/17
 * Desciption: Definitions for Index State
 *
 */

#pragma once

#include <string>

namespace mochow::common {

class IndexState final {
public:
    static constexpr const char* INVALID = "INVALID";
    static constexpr const char* BUILDING = "BUILDING";
    static constexpr const char* NORMAL = "NORMAL";

    static inline bool is_valid_index_state(const std::string& state) {
        return state == BUILDING || state == NORMAL;
    }

    static inline bool is_valid_index_state_str(const std::string& state) {
        return state == INVALID || state == BUILDING || state == NORMAL;
    }

private:
    IndexState() = default;
    ~IndexState() = default;
};

}
