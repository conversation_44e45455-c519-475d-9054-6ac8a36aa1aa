#pragma once

#include <gflags/gflags.h>

namespace baidu::rpc {
    DECLARE_int32(idle_timeout_second);
    DECLARE_int32(defer_close_second);
}

namespace mochow {

DECLARE_bool(standalone_mode);

DECLARE_string(token);
DECLARE_string(master_address);
DECLARE_int32(raft_election_timeout_ms);
DECLARE_int32(raft_snapshot_interval_s);
DECLARE_uint64(idle_timeout_s);

DECLARE_uint64(os_page_size);
DECLARE_uint64(default_bimap_bucket_count);

DECLARE_int32(datanode_total_thread_count);
DECLARE_double(datanode_load_from_disk_thread_percentage);
DECLARE_double(datanode_tablet_index_builder_thread_percentage);
DECLARE_double(zhuque_posix_file_system_thread_percentage);
DECLARE_double(vindex_global_build_thread_percentage);
DECLARE_double(vindex_global_search_thread_percentage);
DECLARE_double(moss_dumper_and_loader_thread_percentage);
DECLARE_double(moss_compactor_thread_percentage);
DECLARE_int32(hnsw_search_ef_upper_limit);

DECLARE_string(encryption_algorithm_name);
DECLARE_string(encryption_key);

DECLARE_uint64(build_thread_pool_max_capacity);
DECLARE_uint64(search_thread_pool_max_capacity);

DECLARE_uint64(datanode_memory_quota_in_mb);
DECLARE_double(default_post_filter_amplification_factor);
DECLARE_double(datanode_memory_reserved_in_gb);

DECLARE_uint64(thread_pool_auto_adjust_check_interval_s);

DECLARE_bool(enable_token_bucket);
DECLARE_uint64(token_bucket_max_tokens);
DECLARE_uint64(token_bucket_fill_rate);

}
