/**
 * MOCHOW - A Vector Database System.
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON> (<EMAIL>)
 * Date: 2023/09/18
 * Desciption: Implementations for the transformations among EndPoint/Str/NodeId
 *
 */

#pragma once

#include <base/endpoint.h>
#include <base/string_printf.h>
#include <base/strings/string_number_conversions.h>
#include "raft/configuration.h"
#include "baidu/vdb/mochow/core/src/proto/common.pb.h"

namespace mochow::common {

// EndPoint <-> uint64 == ip(32~63bit) + port(0~15bit)

inline uint64_t endpoint2int(const base::EndPoint& addr) {
    uint64_t ip = static_cast<uint64_t>(base::ip2int(addr.ip));
    uint64_t port = static_cast<uint64_t>(addr.port);
    return (ip << 32) | port;
}

inline uint64_t ipport2int(uint64_t ip, uint64_t port) {
    return (ip << 32) | (port & 0x000000000000FFFFull);
}

inline ::base::EndPoint int2endpoint(const uint64_t id) {
    uint32_t ip = static_cast<uint32_t>(id >> 32);
    uint32_t port = static_cast<uint32_t>(id & 0x000000000000FFFFull);
    ::base::EndPoint ret(base::int2ip(ip), port);
    return ret;
}

// EndPoint <-> mochow::pb::NodeId

inline mochow::pb::NodeId endpoint2node(const base::EndPoint& addr) {
    mochow::pb::NodeId id;
    id.set_ip(addr.ip.s_addr);
    id.set_port(addr.port);
    return id;
}

inline base::EndPoint node2endpoint(const mochow::pb::NodeId& id) {
    base::EndPoint addr;
    addr.ip.s_addr = id.ip();
    addr.port = id.port();
    return addr;
}

// EndPoint <-> String

inline std::string endpoint2str(const base::EndPoint& addr) {
    base::EndPointStr str = base::endpoint2str(addr);
    return std::string(str.c_str());
}

inline std::string ip2str(const base::ip_t& ip) {
    base::IPStr str = base::ip2str(ip);
    return std::string(str.c_str());
}

inline int str2endpoint(const char* ip_and_port_str, base::EndPoint* point) {
    return base::str2endpoint(ip_and_port_str, point);
}

inline int str2endpoint(const char* ip_str, int port, base::EndPoint* point) {
    return base::str2endpoint(ip_str, port, point);
}

// mochow::pb::NodeId <-> String

inline int str2node(const char* str, mochow::pb::NodeId* node) {
    base::EndPoint addr;
    int ret = base::str2endpoint(str, &addr);
    if (ret != 0) {
        return ret;
    }
    *node = endpoint2node(addr);
    return 0;
}

inline std::string node2str(const mochow::pb::NodeId& id) {
    base::EndPoint addr = node2endpoint(id);
    return base::endpoint2str(addr).c_str();
}

// mochow::pb::RaftPeerId <-> PeerId

inline mochow::pb::RaftPeerId peerid2pb(const raft::PeerId& peer_id) {
    mochow::pb::RaftPeerId id;
    id.mutable_addr()->set_ip(peer_id.addr.ip.s_addr);
    id.mutable_addr()->set_port(peer_id.addr.port);
    id.set_index(peer_id.idx);
    return id;
}

inline raft::PeerId pb2peerid(const mochow::pb::RaftPeerId& id) {
    raft::PeerId peer_id;
    peer_id.addr = node2endpoint(id.addr());
    peer_id.idx = id.index();
    return peer_id;
}

}
