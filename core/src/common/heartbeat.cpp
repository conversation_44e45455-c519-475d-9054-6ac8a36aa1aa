/**
 * MOCHOW - A Vector Database System.
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved.
 *
 */

#include "core/src/common/utils.h"
#include "core/src/common/heartbeat.h"
#include "core/src/common/rpc/rpc_call.h"
#include "core/src/common/rpc/rpc_stub.h"
#include "baidu/vdb/mochow/core/src/proto/common.pb.h"

namespace mochow::common {

DEFINE_int32(heartbeat_interval_s, 2, "Heartbeat interval (in second)");
BAIDU_RPC_VALIDATE_GFLAG(heartbeat_interval_s , baidu::rpc::PositiveInteger);

void Heartbeat::start(base::EndPoint listen_addr, uint32_t heartbeat_interval_second) {
    interval_second = heartbeat_interval_second;
    _listen_addr = listen_addr;
    _is_stopped = false;
    LOG(NOTICE) << "Start heartbeater";

    int ret = bthread_timer_add(&_heartbeat_timer,
                      base::milliseconds_from_now(interval_second.load() * 1000),
                      Heartbeat::on_heartbeat_timer, this);
    assert(ret == 0);
}

void Heartbeat::stop() {
    _is_stopped = true;
    bthread_timer_del(_heartbeat_timer);
    LOG(NOTICE) << "Stop heartbeater";
}

void* Heartbeat::heartbeat_rpc_func(void* args) {
    Heartbeat* scheduler = static_cast<Heartbeat*>(args);
    scheduler->heartbeat_rpc();
    return nullptr;
}

void Heartbeat::on_heartbeat_timer(void* args) {
    Heartbeat* scheduler = static_cast<Heartbeat*>(args);
    if (scheduler->_is_stopped) {
        return;
    }
    bthread_t tid;
    int ret = bthread_start_background(&tid, NULL, heartbeat_rpc_func, scheduler);
    if (ret != 0) {
        LOG(ERROR) << "Fail to start bthread for heartbeater";
    }
    ret = bthread_timer_add(&scheduler->_heartbeat_timer,
                      base::milliseconds_from_now(scheduler->interval_second.load() * 1000),
                      Heartbeat::on_heartbeat_timer, scheduler);
    if (ret != 0) {
        LOG(ERROR) << "Fail to add bthread timer for heartbeater";
    }
}

void add_basic_metrics_in_heartbeat(pb::BasicMetrics* basic_metrics) {
    std::string cpu_str = bvar::Variable::describe_exposed("process_cpu_usage");
    try {
        basic_metrics->set_cpu_usage(std::stof(cpu_str));
    } catch (...) {
        LOG(WARNING) << "Add cpu usage with 0 due to invalid process_cpu_usage:" << cpu_str;
        basic_metrics->set_cpu_usage(0);
    }
    basic_metrics->set_memory_usage(mem_usage());
    basic_metrics->set_memory_available_in_bytes(get_available_mem_size_in_bytes());
    boost::any value;
    if (0 == bvar::Variable::get_exposed("process_io_read_bytes_second", &value)) {
        basic_metrics->set_net_read(boost::any_cast<size_t>(value));
    }
    if (0 == bvar::Variable::get_exposed("process_io_write_bytes_second", &value)) {
        basic_metrics->set_net_write(boost::any_cast<size_t>(value));
    }
    if (0 == bvar::Variable::get_exposed("process_disk_read_bytes_second", &value)) {
        basic_metrics->set_disk_read(boost::any_cast<size_t>(value));
    }
    if (0 == bvar::Variable::get_exposed("process_disk_write_bytes_second", &value)) {
        basic_metrics->set_disk_write(boost::any_cast<size_t>(value));
    }
    std::string memory = bvar::Variable::describe_exposed("process_memory_resident");
    basic_metrics->set_rss_size(atoll(memory.data()));
}

}
