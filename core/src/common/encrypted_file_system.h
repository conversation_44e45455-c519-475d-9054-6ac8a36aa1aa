#pragma once

#include <atomic>
#include <openssl/aes.h>

#include "sfl/errors.h"
#include "sfl/ithreadpool.h"
#include "sfl/prioritized_threadpool.h"
#include "sfl/shared_mutex.h"
#include "sfl/uuid_type.h"
#include "sfl/ifile_system.h"
#include "sfl/return.h"

#include "core/src/common/sm4.h"
#include "core/src/common/logging.h"

namespace mochow::common {

constexpr uint64_t g_encrypted_file_system_default_thread_count = 10;
static constexpr const char* AES_CTR_ENCRYPTION = "aes_ctr";
static constexpr const char* SM4_CTR_ENCRYPTION = "sm4_ctr";

class BlockCipher {
public:
    virtual ~BlockCipher() {}

    virtual size_t block_size() = 0;

    virtual BRESULT encrypt(char* data) = 0;

    virtual BRESULT decrypt(char* data) = 0;
};

class AESCipher : public BlockCipher {
public:
    AESCipher(const std::string& key);

    virtual ~AESCipher(){};
    static const char* Type() {
        return "AESCipher";
    }

    virtual size_t block_size() override;

    virtual BRESULT encrypt(char* data) override;

    virtual BRESULT decrypt(char* data) override;

private:
    size_t _block_size;
    AES_KEY _aes_key;
};

class SM4Cipher : public BlockCipher {
public:
    SM4Cipher(const std::string& key);

    virtual ~SM4Cipher(){};
    static const char* Type() {
        return "SM4Cipher";
    }

    virtual size_t block_size() override;

    virtual BRESULT encrypt(char* data) override;

    virtual BRESULT decrypt(char* data) override;

private:
    size_t _block_size;
    SM4_KEY _sm4_key;
};

class BlockAccessCipherStream {
public:
    virtual ~BlockAccessCipherStream() {}

    virtual size_t block_size() = 0;

    virtual BRESULT encrypt(uint64_t file_offset, char* data, size_t data_size);

    virtual BRESULT decrypt(uint64_t file_offset, char* data, size_t data_size);

protected:
    virtual void alloc_scratch(std::string&) = 0;

    virtual BRESULT encrypt_block(uint64_t block_index, char* data, char* scratch) = 0;

    virtual BRESULT decrypt_block(uint64_t block_index, char* data, char* scratch) = 0;
};

class CTRCipherStream final : public BlockAccessCipherStream {
private:
    std::shared_ptr<BlockCipher> _cipher;
    std::string _iv;
    uint64_t _initial_counter;

public:
    CTRCipherStream(const std::shared_ptr<BlockCipher>& c, const char* iv, uint64_t initial_counter) :
            _cipher(c), _iv(iv, c->block_size()), _initial_counter(initial_counter) {}
    virtual ~CTRCipherStream() {}

    size_t block_size() override {
        return _cipher->block_size();
    }

protected:
    void alloc_scratch(std::string& scratch) override;

    BRESULT encrypt_block(uint64_t block_index, char* data, char* scratch) override;

    BRESULT decrypt_block(uint64_t block_index, char* data, char* scratch) override;
};

class EncryptionProvider {
public:
    virtual ~EncryptionProvider() {}

    virtual size_t get_prefix_length() const = 0;

    virtual BRESULT create_new_prefix(const std::string& fname, char* prefix, size_t prefix_length) const = 0;

    virtual BRESULT create_cipher_stream(
            const std::string& fname,
            char* prefix,
            size_t prefix_length,
            std::unique_ptr<BlockAccessCipherStream>* result) = 0;
};

class CTREncryptionProvider : public EncryptionProvider {
public:
    explicit CTREncryptionProvider(const std::shared_ptr<BlockCipher>& c = nullptr);
    virtual ~CTREncryptionProvider() {}

    static const char* kClassName() {
        return "CTR";
    }
    size_t get_prefix_length() const override;
    BRESULT create_new_prefix(const std::string& fname, char* prefix, size_t prefix_length) const override;
    BRESULT create_cipher_stream(
            const std::string& fname,
            char* prefix,
            size_t prefix_length,
            std::unique_ptr<BlockAccessCipherStream>* result) override;

    BRESULT create_cipher_stream_from_prefix(
            const std::string& fname,
            uint64_t initial_counter,
            const char* iv,
            const char* prefix,
            std::unique_ptr<BlockAccessCipherStream>* result);

private:
    constexpr static size_t default_prefix_length = 4096;
    std::shared_ptr<BlockCipher> _cipher;
};

class EncryptedFileSystemOption final {
public:
    EncryptedFileSystemOption() = default;

    EncryptedFileSystemOption(
            std::string name,
            const uint64_t thread_count,
            const std::shared_ptr<EncryptionProvider>& provider) :
            _name(std::move(name)), _thread_count(thread_count), _provider(provider) {}

    EncryptedFileSystemOption(EncryptedFileSystemOption&& other) noexcept :
            _name(std::move(other._name)), _thread_count(std::move(other._thread_count)) {
        other._name = "EncryptedFileSystem." + baidu::sfl::UUID().unparse();
        other._thread_count = g_encrypted_file_system_default_thread_count;
        other._provider = _provider;
    }

    EncryptedFileSystemOption& operator=(EncryptedFileSystemOption&& other) noexcept {
        if (this == &other) {
            return *this;
        }

        _name = std::move(other._name);
        _thread_count = std::move(other._thread_count);

        other._name = "EncryptedFileSystem." + baidu::sfl::UUID().unparse();
        other._thread_count = g_encrypted_file_system_default_thread_count;
        other._provider = _provider;

        return *this;
    }

    DEFAULT_COPY(EncryptedFileSystemOption)

    std::string get_name() const {
        return _name;
    }

    void set_name(std::string name) {
        _name = std::move(name);
    }

    uint64_t get_thread_count() const {
        return _thread_count;
    }

    void set_thread_count(const uint64_t thread_count) {
        _thread_count = thread_count;
    }

    void set_encryption_provider(const std::shared_ptr<EncryptionProvider>& provider) {
        _provider = std::move(provider);
    }

    std::shared_ptr<EncryptionProvider> get_provider() const {
        return _provider;
    }

    std::string _name = "EncryptedFileSystem." + baidu::sfl::UUID().unparse();

    uint64_t _thread_count = g_encrypted_file_system_default_thread_count;

    std::shared_ptr<EncryptionProvider> _provider;

};

class EncryptedFile final : public baidu::sfl::IFile {
public:
    EncryptedFile(
            std::string path_name,
            int fd,
            const uint64_t open_file_flags,
            const uint64_t offset,
            std::unique_ptr<BlockAccessCipherStream>&& stream) :
            _path_name(std::move(path_name)), _fd(fd), _open_file_flags(open_file_flags), _stream(std::move(stream)) {}

    ~EncryptedFile() override {
        if (_fd != -1) {
            int result = ::fsync(_fd);
            auto errnum = errno;

            // We ignore the failure of fsync because we are in destructor.
            if (result != 0) {
                LOG(WARNING) << "fsync failed. _fd=" << _fd << ", _path_name=" << _path_name
                             << ", _open_file_flags=" << _open_file_flags << ", errno=" << errnum
                             << ", error_message=" << std::strerror(errnum);
            }

            result = ::close(_fd);
            errnum = errno;

            if (result != 0) {
                LOG(WARNING) << "close failed. _fd=" << _fd << ", _path_name=" << _path_name
                             << ", _open_file_flags=" << _open_file_flags << ", errno=" << errnum
                             << ", error_message=" << std::strerror(errnum);
            }

            _fd = -1;
        }
    }

    std::string get_path() const {
        return _path_name;
    }

    BRESULT encrypt(uint64_t file_offset, char* data, size_t data_size) {
        return _stream->encrypt(file_offset, data, data_size);
    };

    BRESULT decrypt(uint64_t file_offset, char* data, size_t data_size) {
        return _stream->decrypt(file_offset, data, data_size);
    };

    DISABLE_COPY_AND_MOVE(EncryptedFile)

private:

    const std::string _path_name;

    int _fd = -1;

    const uint64_t _open_file_flags = 0;

    baidu::sfl::SharedMutex _shared_mutex;

    std::unique_ptr<BlockAccessCipherStream> _stream;

    friend class EncryptedFileSystem;
};

class EncryptedFileSystem final : public baidu::sfl::IFileSystem {
public:
    EncryptedFileSystem() = default;

    ~EncryptedFileSystem() override = default;

    DISABLE_COPY_AND_MOVE(EncryptedFileSystem)

    NODISCARD BRESULT initialize(const EncryptedFileSystemOption& option);

    NODISCARD BRESULT
    open(const char* const path_name,
         const uint64_t open_file_flags,
         const uint64_t timeout_us,
         const baidu::sfl::IUserOption* const user_option,
         baidu::sfl::FileHandle& file_handle) override;

    NODISCARD BRESULT
    close(baidu::sfl::FileHandle& file_handle,
          const uint64_t close_file_flags,
          const uint64_t timeout_us,
          const baidu::sfl::IUserOption* const user_option) override;

    NODISCARD BRESULT
    pread(const baidu::sfl::FileHandle& file_handle,
          const uint64_t read_file_flags,
          void* const buffer,
          const uint64_t buffer_length,
          const uint64_t file_offset,
          const uint64_t timeout_us,
          const baidu::sfl::IUserOption* const user_option,
          uint64_t* const read_length,
          baidu::sfl::AsyncCallback const callback,
          baidu::sfl::AsyncContext* const context) override;

    NODISCARD BRESULT
    append(const baidu::sfl::FileHandle& file_handle,
           const uint64_t append_file_flags,
           const void* const buffer,
           const uint64_t buffer_length,
           const uint64_t timeout_us,
           const baidu::sfl::IUserOption* const user_option) override;

    NODISCARD BRESULT
    fsync(const baidu::sfl::FileHandle& file_handle,
          const uint64_t fsync_file_flags,
          const uint64_t timeout_us,
          const baidu::sfl::IUserOption* const user_option) override;

    NODISCARD BRESULT
    size(const char* const path_name,
         const uint64_t size_file_flags,
         const uint64_t timeout_us,
         const baidu::sfl::IUserOption* const user_option,
         uint64_t& file_size) override;

    NODISCARD BRESULT
    size(const baidu::sfl::FileHandle& file_handle,
         const uint64_t size_file_flags,
         const uint64_t timeout_us,
         const baidu::sfl::IUserOption* const user_option,
         uint64_t& file_size) override;

    NODISCARD BRESULT
    mtime(const char* const path_name,
          const uint64_t mtime_file_flags,
          const uint64_t timeout_us,
          const baidu::sfl::IUserOption* const user_option,
          std::time_t& file_mtime) override;

    NODISCARD BRESULT
    mtime(const baidu::sfl::FileHandle& file_handle,
          const uint64_t mtime_file_flags,
          const uint64_t timeout_us,
          const baidu::sfl::IUserOption* const user_option,
          std::time_t& file_mtime) override;

    NODISCARD BRESULT
    exist(const char* const path_name,
          const uint64_t timeout_us,
          const baidu::sfl::IUserOption* const user_option,
          bool& found) override;

    NODISCARD BRESULT
    rename(const char* const old_path_name,
           const char* const new_path_name,
           const uint64_t rename_flags,
           const uint64_t timeout_us,
           const baidu::sfl::IUserOption* const user_option) override;

    NODISCARD BRESULT
    link(const char* const old_path_name,
         const char* const new_path_name,
         const uint64_t link_flags,
         const uint64_t timeout_us,
         const baidu::sfl::IUserOption* const user_option) override;

    NODISCARD BRESULT
    symlink(const char* const old_path_name,
            const char* const new_path_name,
            const uint64_t symlink_flags,
            const uint64_t timeout_us,
            const baidu::sfl::IUserOption* const user_option) override;

    NODISCARD BRESULT
    lsdir(const char* const path_name,
          const uint64_t timeout_us,
          const baidu::sfl::IUserOption* const user_option,
          std::vector<baidu::sfl::DirectoryEntryDescriptor>& entries) override;

    NODISCARD BRESULT
    mkdir(const char* const path_name,
          const uint64_t mkdir_flags,
          const uint64_t timeout_us,
          const baidu::sfl::IUserOption* const user_option) override;

    NODISCARD BRESULT
    remove(const char* const path_name,
           const uint64_t remove_flags,
           const uint64_t timeout_us,
           const baidu::sfl::IUserOption* const user_option) override;

    NODISCARD BRESULT truncate_lease(const char* const path_name) override;

private:
    void initialize_threadpool();

    NODISCARD BRESULT submit_task(baidu::sfl::IThreadpool::Task task);

    NODISCARD BRESULT read_sync(
            const baidu::sfl::FileHandle& file_handle,
            const uint64_t read_file_flags,
            void* const buffer,
            const uint64_t buffer_length,
            const uint64_t file_offset,
            uint64_t* const read_length);

    NODISCARD BRESULT read_async(
            const baidu::sfl::FileHandle& file_handle,
            const uint64_t read_file_flags,
            void* const buffer,
            const uint64_t buffer_length,
            const uint64_t file_offset,
            uint64_t* const read_length,
            baidu::sfl::AsyncCallback const callback,
            baidu::sfl::AsyncContext* const context);

    NODISCARD BRESULT mkdir_internal(const std::string& directory);

    NODISCARD BRESULT remove_directory(std::string directory);

    NODISCARD BRESULT
    new_writable_file(const char* const path_name, const uint64_t open_file_flags, baidu::sfl::FileHandle& file_handle);

    NODISCARD BRESULT
    new_readable_file(const char* const path_name, const uint64_t open_file_flags, baidu::sfl::FileHandle& file_handle);

private:
    std::atomic<bool> _initialized{false};

    std::string _file_system_name;

    uint64_t _thread_count = g_encrypted_file_system_default_thread_count;

    std::unique_ptr<baidu::sfl::PrioritizedThreadpool> _threadpool;

    std::once_flag _once_flag;

    std::shared_ptr<EncryptionProvider> _provider;
};

}  // namespace mochow::common