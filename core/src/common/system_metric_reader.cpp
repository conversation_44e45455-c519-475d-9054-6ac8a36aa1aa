/**
 * MOCHOW - A Vector Database System.
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON> (<PERSON><PERSON><PERSON><PERSON>@baidu.com)
 * Date: 2024/04/26
 * Desciption: Implementations for system variable reader.
 *
 */

#include "core/src/common/system_metric_reader.h"

#include <memory>

#include "core/src/common/logging.h"
#include "core/src/common/flags.h"

namespace mochow {

static constexpr uint64_t system_metric_cached_interval_us = 1000 * 1000;   // 100ms

SystemMetricReaderPtr<ProcStat> g_proc_stat_reader =
    std::make_unique<SystemMetricReader<ProcStat>>(read_proc_stat, system_metric_cached_interval_us);
SystemMetricReaderPtr<ProcMemory> g_proc_memory_reader =
    std::make_unique<SystemMetricReader<ProcMemory>>(read_proc_memory, system_metric_cached_interval_us);
SystemMetricReaderPtr<LoadAverage> g_load_average_reader =
    std::make_unique<SystemMetricReader<LoadAverage>>(read_load_average, system_metric_cached_interval_us);
SystemMetricReaderPtr<ProcIO> g_proc_io_reader =
    std::make_unique<SystemMetricReader<ProcIO>>(read_proc_io, system_metric_cached_interval_us);

// ======================================

static bool read_proc_stat(ProcStat& stat) {
    ::base::ScopedFILE fp("/proc/self/stat", "r");
    if (NULL == fp) {
        LOG(WARNING) << "Fail to open /proc/self/stat";
        return false;
    }
    errno = 0;
    if (fscanf(fp, "%10d %*s %c "
               "%10d %10d %10d %10d %10d "
               "%10u %20lu %20lu %20lu "
               "%20lu %20lu %20lu %20lu %20lu "
               "%20ld %20ld %20ld",
               &stat.pid, &stat.state,
               &stat.ppid, &stat.pgrp, &stat.session, &stat.tty_nr, &stat.tpgid,
               &stat.flags, &stat.minflt, &stat.cminflt, &stat.majflt,
               &stat.cmajflt, &stat.utime, &stat.stime, &stat.cutime, &stat.cstime,
               &stat.priority, &stat.nice, &stat.num_threads) != 19) {
        LOG(WARNING) << "Fail to fscanf /proc/self/stat";
        return false;
    }
    return true;
};

static bool read_proc_memory(ProcMemory& m) {
    ::base::ScopedFILE fp("/proc/self/statm", "r");
    if (NULL == fp) {
        LOG(WARNING) << "Fail to open /proc/self/statm";
        return false;
    }
    errno = 0;
    if (fscanf(fp, "%20ld %20ld %20ld %20ld %20ld %20ld %20ld",
               &m.size, &m.resident, &m.share,
               &m.trs, &m.lrs, &m.drs, &m.dt) != 7) {
        LOG(WARNING) << "Fail to fscanf /proc/self/statm";
        return false;
    }
    return true;
}

static bool read_load_average(LoadAverage& m) {
    ::base::ScopedFILE fp("/proc/loadavg", "r");
    if (NULL == fp) {
        LOG(WARNING) << "Fail to open /proc/loadavg";
        return false;
    }
    errno = 0;
    if (fscanf(fp, "%38lf %38lf %38lf",
               &m.loadavg_1m, &m.loadavg_5m, &m.loadavg_15m) != 3) {
        LOG(WARNING) << "Fail to fscanf /proc/loadavg";
        return false;
    }
    return true;
}

static bool read_proc_io(ProcIO& s) {
    ::base::ScopedFILE fp("/proc/self/io", "r");
    if (NULL == fp) {
        LOG(WARNING) << "Fail to open /proc/self/io";
        return false;
    }
    errno = 0;
    if (fscanf(fp, "%*s %20lu %*s %20lu %*s %20lu %*s %20lu %*s %20lu %*s %20lu %*s %20lu",
               &s.rchar, &s.wchar, &s.syscr, &s.syscw,
               &s.read_bytes, &s.write_bytes, &s.cancelled_write_bytes) != 7) {
        LOG(WARNING) << "Fail to fscanf /proc/self/io";
        return false;
    }
    return true;
}

} // namespace mochow
