#pragma once

#include <openssl/opensslconf.h>
#include <openssl/e_os2.h>

#define SM4_BLOCK_SIZE 16
#define SM4_KEY_SCHEDULE 32

namespace mochow::common {

typedef struct SM4_KEY_st {
    uint32_t rk[SM4_KEY_SCHEDULE];
} SM4_KEY;

int ossl_sm4_set_key(const uint8_t *key, SM4_KEY *ks);

void ossl_sm4_encrypt(const uint8_t *in, uint8_t *out, const SM4_KEY *ks);

void ossl_sm4_decrypt(const uint8_t *in, uint8_t *out, const SM4_KEY *ks);

}  // namespace mochow::common