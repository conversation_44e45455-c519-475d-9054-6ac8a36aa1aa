
#include "core/src/common/logging.h"
#include "core/src/common/meta/rbac.h"

namespace mochow::common {

bool Role::deserialize(const mochow::pb::PRole& prole) {
    clear();

    if (!prole.has_role()) {
        LOG(ERROR) << "Fail to deserialize role from pb due to missing some fields,"
                   << " has_role:" << prole.has_role();
        return false;
    }

    _name = prole.role();

    if (prole.has_parent()) {
        _parent = prole.parent();
    }

    if (prole.has_priv_tree()) {
        if (!_priv_tree.deserialize(prole.priv_tree())) {
            LOG(ERROR) << "Fail to deserialize role from pb due to deserialize privilege tree failed";
            return false;
        }
    }

    return true;
}

void Role::serialize(mochow::pb::PRole* prole) const {
    prole->Clear();
    prole->set_role(_name);
    prole->set_parent(_parent);

    if (!_priv_tree.empty()) {
        _priv_tree.serialize(prole->mutable_priv_tree());
    }
}

bool User::deserialize(const mochow::pb::PUser& puser) {
    clear();

    if (!puser.has_username() || !puser.has_password()) {
        LOG(ERROR) << "Fail to deserialize user from pb due to missing some fields,"
                   << " has_username:" << puser.has_username()
                   << " has_password:" << puser.has_password();
        return false;
    }

    _username = puser.username();
    _password = puser.password();

    if (puser.has_parent()) {
        _parent = puser.parent();
    }

    for (int i = 0; i < puser.child_users_size(); ++i) {
        _child_users.insert(puser.child_users(i));
    }

    for (int i = 0; i < puser.child_roles_size(); ++i) {
        _child_roles.insert(puser.child_roles(i));
    }

    if (puser.has_priv_tree()) {
        if (!_priv_tree.deserialize(puser.priv_tree())) {
            LOG(ERROR) << "Fail to deserialize user from pb due to deserialize privilege tree failed";
            return false;
        }
    }

    // Do NOT deserialize roles, the caller will handle.

    return true;
}

void User::serialize(mochow::pb::PUser* puser) const {
    puser->Clear();
    puser->set_username(_username);
    puser->set_password(_password);
    puser->set_parent(_parent);

    for (const auto& username : _child_users) {
        puser->add_child_users(username);
    }

    for (const auto& role_name : _child_roles) {
        puser->add_child_roles(role_name);
    }

    for (const auto& [role_name, _] : _roles) {
        puser->add_roles(role_name);
    }

    if (!_priv_tree.empty()) {
        _priv_tree.serialize(puser->mutable_priv_tree());
    }
}

bool RBACManager::deserialize(const mochow::pb::PRBACInfo& pinfo) {
    if (!pinfo.has_raft_log_index()) {
        return false;
    }

    _raft_index = pinfo.raft_log_index();

    for (int i = 0; i < pinfo.roles_size(); ++i) {
        auto role = std::make_shared<Role>();
        if (!role->deserialize(pinfo.roles(i))) {
            return false;
        }
        std::ignore = add_role_without_lock(role);
    }

    for (int i = 0; i < pinfo.users_size(); ++i) {
        auto user = std::make_shared<User>();
        if (!user->deserialize(pinfo.users(i))) {
            return false;
        }
        for (int j = 0; j < pinfo.users(i).roles_size(); ++j) {
            auto role = get_role_without_lock(pinfo.users(i).roles(j));
            if (role == nullptr) {
                return false;
            }
            std::ignore = user->grant_role(role);
        }
        std::ignore = add_user_without_lock(user);
    }

    return true;
}

void RBACManager::serialize(mochow::pb::PRBACInfo* pinfo) const {
    pinfo->Clear();

    pinfo->set_raft_log_index(raft_index());

    for (const auto& [_, role] : _roles) {
        role->serialize(pinfo->add_roles());
    }

    for (const auto& [_, user] : _users) {
        user->serialize(pinfo->add_users());
    }
}

void initialize_rbac_manager(RBACManager* rbac_manager) {
    // create system user
    auto system_user = std::make_shared<common::User>(
            S_MOCHOW_SYSTEM_USERNAME, S_MOCHOW_SYSTEM_PASSWORD, "");
    bool ok = rbac_manager->add_user(system_user);
    LOG_AND_ASSERT(ok);

    // create root user
    auto root_user = rbac_manager->create_user(
            S_MOCHOW_ROOT_USERNAME, S_MOCHOW_ROOT_PASSWORD, S_MOCHOW_SYSTEM_USERNAME);
    LOG_AND_ASSERT(root_user != nullptr);

    // create ADMIN role
    auto admin_role = rbac_manager->create_role(
            S_MOCHOW_BUILTIN_ADMIN_ROLE, S_MOCHOW_ROOT_USERNAME);
    LOG_AND_ASSERT(admin_role != nullptr);

    // grant ALL privileges to ADMIN role
    ok = admin_role->grant_privileges_by_bitset("*", "*", *g_privilege_bitset_ALL);
    LOG_AND_ASSERT(ok);

    // grant ADMIN role to __system and root
    ok = system_user->grant_role(admin_role);
    LOG_AND_ASSERT(ok);
    ok = root_user->grant_role(admin_role);
    LOG_AND_ASSERT(ok);

    // grant USAGE privilege to __system and root
    ok = system_user->grant_privilege("*", "*", Privilege::USAGE);
    LOG_AND_ASSERT(ok);
    ok = root_user->grant_privilege("*", "*", Privilege::USAGE);
    LOG_AND_ASSERT(ok);
}

}
