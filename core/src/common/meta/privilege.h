/**
 * MOCHOW - A Vector Database System.
 * Copyright (c) 2024 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON> (<EMAIL>)
 * Date: 2024/06/05
 * Description: Definitions for RBAC Privileges
 *
 */

#pragma once

#include <array>
#include <bitset>
#include <string_view>
#include <unordered_map>

namespace mochow {

using PrivilegeId = size_t;

constexpr const size_t MOCHOW_PRIVILEGE_COUNT_MAX = 64;
constexpr const size_t MOCHOW_PRIVILEGE_COUNT = 32;
constexpr const size_t INVALID_PRIVILEGE_ID = MOCHOW_PRIVILEGE_COUNT_MAX;

// Privilege Definitions and Utilities.

class Privilege final {
public:
    // 0 ~ 7
    static constexpr PrivilegeId USAGE              = 0;
    static constexpr PrivilegeId CREATE_USER        = 1;
    static constexpr PrivilegeId DROP_USER          = 2;
    static constexpr PrivilegeId PASSWORD           = 3;
    static constexpr PrivilegeId CREATE_ROLE        = 4;
    static constexpr PrivilegeId DROP_ROLE          = 5;
    static constexpr PrivilegeId GRANT_REVOKE       = 6;    // Grant/Revoke
    static constexpr PrivilegeId SHOW_USER          = 7;

    // 8 ~ 15
    static constexpr PrivilegeId SHOW_ROLE          = 8;
    static constexpr PrivilegeId CREATE_DATABASE    = 9;
    static constexpr PrivilegeId ALTER_DATABASE     = 10;
    static constexpr PrivilegeId DROP_DATABASE      = 11;
    static constexpr PrivilegeId SHOW_DATABASE      = 12;
    static constexpr PrivilegeId CREATE_TABLE       = 13;
    static constexpr PrivilegeId DROP_TABLE         = 14;
    static constexpr PrivilegeId SHOW_TABLE         = 15;

    // 16 ~ 23
    static constexpr PrivilegeId QUERY              = 16;
    static constexpr PrivilegeId SELECT             = 17;
    static constexpr PrivilegeId SEARCH             = 18;
    static constexpr PrivilegeId INSERT             = 19;
    static constexpr PrivilegeId UPSERT             = 20;
    static constexpr PrivilegeId UPDATE             = 21;
    static constexpr PrivilegeId DELETE             = 22;
    static constexpr PrivilegeId SET_TTL            = 23;

    // 24 ~ 31
    static constexpr PrivilegeId ALTER_TABLE        = 24;   // AddField/DropField/CreateIndex/DropIndex
    static constexpr PrivilegeId CONFIG_TABLE       = 25;
    static constexpr PrivilegeId CONFIG_INDEX       = 26;
    static constexpr PrivilegeId BUILD_INDEX        = 27;
    static constexpr PrivilegeId CDC_ADMIN          = 28;
    static constexpr PrivilegeId PARTITION          = 29;
    static constexpr PrivilegeId BULKLOAD           = 30;
    static constexpr PrivilegeId ALIAS              = 31;   // Alias/Unalias

    // Sentinel
    static constexpr PrivilegeId END                = 31;
};

class PrivilegeName {
public:
    static constexpr const char* INVALID            = "INVALID";

    static constexpr const char* USAGE              = "USAGE";
    static constexpr const char* CREATE_USER        = "CREATE_USER";
    static constexpr const char* DROP_USER          = "DROP_USER";
    static constexpr const char* PASSWORD           = "PASSWORD";
    static constexpr const char* CREATE_ROLE        = "CREATE_ROLE";
    static constexpr const char* DROP_ROLE          = "DROP_ROLE";
    static constexpr const char* GRANT_REVOKE       = "GRANT_REVOKE";
    static constexpr const char* SHOW_USER          = "SHOW_USER";

    static constexpr const char* SHOW_ROLE          = "SHOW_ROLE";
    static constexpr const char* CREATE_DATABASE    = "CREATE_DATABASE";
    static constexpr const char* ALTER_DATABASE     = "ALTER_DATABASE";
    static constexpr const char* DROP_DATABASE      = "DROP_DATABASE";
    static constexpr const char* SHOW_DATABASE      = "SHOW_DATABASE";
    static constexpr const char* CREATE_TABLE       = "CREATE_TABLE";
    static constexpr const char* DROP_TABLE         = "DROP_TABLE";
    static constexpr const char* SHOW_TABLE         = "SHOW_TABLE";

    static constexpr const char* QUERY              = "QUERY";
    static constexpr const char* SELECT             = "SELECT";
    static constexpr const char* SEARCH             = "SEARCH";
    static constexpr const char* INSERT             = "INSERT";
    static constexpr const char* UPSERT             = "UPSERT";
    static constexpr const char* UPDATE             = "UPDATE";
    static constexpr const char* DELETE             = "DELETE";
    static constexpr const char* SET_TTL            = "SET_TTL";

    static constexpr const char* ALTER_TABLE        = "ALTER_TABLE";
    static constexpr const char* CONFIG_TABLE       = "CONFIG_TABLE";
    static constexpr const char* CONFIG_INDEX       = "CONFIG_INDEX";
    static constexpr const char* BUILD_INDEX        = "BUILD_INDEX";
    static constexpr const char* CDC_ADMIN          = "CDC_ADMIN";
    static constexpr const char* PARTITION          = "PARTITION";
    static constexpr const char* BULKLOAD           = "BULKLOAD";
    static constexpr const char* ALIAS              = "ALIAS";
};

static const std::array<const char*, MOCHOW_PRIVILEGE_COUNT> g_privilege_name_array = {
    PrivilegeName::USAGE,
    PrivilegeName::CREATE_USER,
    PrivilegeName::DROP_USER,
    PrivilegeName::PASSWORD,
    PrivilegeName::CREATE_ROLE,
    PrivilegeName::DROP_ROLE,
    PrivilegeName::GRANT_REVOKE,
    PrivilegeName::SHOW_USER,

    PrivilegeName::SHOW_ROLE,
    PrivilegeName::CREATE_DATABASE,
    PrivilegeName::ALTER_DATABASE,
    PrivilegeName::DROP_DATABASE,
    PrivilegeName::SHOW_DATABASE,
    PrivilegeName::CREATE_TABLE,
    PrivilegeName::DROP_TABLE,
    PrivilegeName::SHOW_TABLE,

    PrivilegeName::QUERY,
    PrivilegeName::SELECT,
    PrivilegeName::SEARCH,
    PrivilegeName::INSERT,
    PrivilegeName::UPSERT,
    PrivilegeName::UPDATE,
    PrivilegeName::DELETE,
    PrivilegeName::SET_TTL,

    PrivilegeName::ALTER_TABLE,
    PrivilegeName::CONFIG_TABLE,
    PrivilegeName::CONFIG_INDEX,
    PrivilegeName::BUILD_INDEX,
    PrivilegeName::CDC_ADMIN,
    PrivilegeName::PARTITION,
    PrivilegeName::BULKLOAD,
    PrivilegeName::ALIAS,
};

static const std::unordered_map<std::string, PrivilegeId> g_privilege_name_id_map = {
    {PrivilegeName::USAGE,              0},
    {PrivilegeName::CREATE_USER,        1},
    {PrivilegeName::DROP_USER,          2},
    {PrivilegeName::PASSWORD,           3},
    {PrivilegeName::CREATE_ROLE,        4},
    {PrivilegeName::DROP_ROLE,          5},
    {PrivilegeName::GRANT_REVOKE,       6},
    {PrivilegeName::SHOW_USER,          7},

    {PrivilegeName::SHOW_ROLE,          8},
    {PrivilegeName::CREATE_DATABASE,    9},
    {PrivilegeName::ALTER_DATABASE,     10},
    {PrivilegeName::DROP_DATABASE,      11},
    {PrivilegeName::SHOW_DATABASE,      12},
    {PrivilegeName::CREATE_TABLE,       13},
    {PrivilegeName::DROP_TABLE,         14},
    {PrivilegeName::SHOW_TABLE,         15},

    {PrivilegeName::QUERY,              16},
    {PrivilegeName::SELECT,             17},
    {PrivilegeName::SEARCH,             18},
    {PrivilegeName::INSERT,             19},
    {PrivilegeName::UPSERT,             20},
    {PrivilegeName::UPDATE,             21},
    {PrivilegeName::DELETE,             22},
    {PrivilegeName::SET_TTL,            23},

    {PrivilegeName::ALTER_TABLE,        24},
    {PrivilegeName::CONFIG_TABLE,       25},
    {PrivilegeName::CONFIG_INDEX,       26},
    {PrivilegeName::BUILD_INDEX,        27},
    {PrivilegeName::CDC_ADMIN,          28},
    {PrivilegeName::PARTITION,          29},
    {PrivilegeName::BULKLOAD,           30},
    {PrivilegeName::ALIAS,              31},
};

static_assert(Privilege::END + 1 == MOCHOW_PRIVILEGE_COUNT, "Privilege::END + 1 not equals to MOCHOW_PRIVILEGE_COUNT");

static bool is_valid_privilege_id(const PrivilegeId priv_id) {
    return priv_id < MOCHOW_PRIVILEGE_COUNT;
}

static const char* get_privilege_name_by_id(const PrivilegeId priv_id) {
    if (is_valid_privilege_id(priv_id)) {
        return g_privilege_name_array[priv_id];
    } else {
        return PrivilegeName::INVALID;
    }
}

static PrivilegeId get_privilege_id_by_name(const std::string& privilege) {
    auto iter = g_privilege_name_id_map.find(privilege);
    return iter != g_privilege_name_id_map.end() ? iter->second : INVALID_PRIVILEGE_ID;
}

// Privilege Group Definitions and Utilities.

using PrivilegeBitset = std::bitset<MOCHOW_PRIVILEGE_COUNT_MAX>;

class PrivilegeGroupName {
public:
    static constexpr const char* ALL                 = "ALL";
    static constexpr const char* SYSTEM_ALL          = "SYSTEM_ALL";
    static constexpr const char* TABLE_ALL           = "TABLE_ALL";
    static constexpr const char* TABLE_CONTROL       = "TABLE_CONTROL";
    static constexpr const char* TABLE_READONLY      = "TABLE_READONLY";
    static constexpr const char* TABLE_READWRITE     = "TABLE_READWRITE";
};

class PrivilegeGroupBitString {
public:
    //                                                 63 ~ 56   55 ~ 48   47 ~ 40   39 ~ 32   31 ~ 24   23 ~ 16   15 ~ 8    7 ~ 0 
    static constexpr const char* ALL                = "11111111""11111111""11111111""11111111""11111111""11111111""11111111""11111110";
    static constexpr const char* SYSTEM_ALL         = "00000000""00000000""00000000""00000000""00000000""00000000""00000011""11111110";
    static constexpr const char* TABLE_ALL          = "00000000""00000000""00000000""00000000""11111111""11111111""11100000""00000000";
    static constexpr const char* TABLE_CONTROL      = "00000000""00000000""00000000""00000000""11111111""10000000""11100000""00000000";
    static constexpr const char* TABLE_READONLY     = "00000000""00000000""00000000""00000000""00000000""00000111""00000000""00000000";
    static constexpr const char* TABLE_READWRITE    = "00000000""00000000""00000000""00000000""00000000""01111111""00000000""00000000";
};

// Since the following constructor for std::bitset do not support `constexpr` until C++23,
// thus we cannot initialize the following object inside a class as a static member.
// See https://en.cppreference.com/w/cpp/utility/bitset/bitset
static const PrivilegeBitset* g_privilege_bitset_ALL            = new PrivilegeBitset(PrivilegeGroupBitString::ALL, MOCHOW_PRIVILEGE_COUNT_MAX);
static const PrivilegeBitset* g_privilege_bitset_SYSTEM_ALL     = new PrivilegeBitset(PrivilegeGroupBitString::SYSTEM_ALL, MOCHOW_PRIVILEGE_COUNT_MAX);
static const PrivilegeBitset* g_privilege_bitset_TABLE_ALL      = new PrivilegeBitset(PrivilegeGroupBitString::TABLE_ALL, MOCHOW_PRIVILEGE_COUNT_MAX);
static const PrivilegeBitset* g_privilege_bitset_TABLE_CONTROL  = new PrivilegeBitset(PrivilegeGroupBitString::TABLE_CONTROL, MOCHOW_PRIVILEGE_COUNT_MAX);
static const PrivilegeBitset* g_privilege_bitset_TABLE_READONLY = new PrivilegeBitset(PrivilegeGroupBitString::TABLE_READONLY, MOCHOW_PRIVILEGE_COUNT_MAX);
static const PrivilegeBitset* g_privilege_bitset_TABLE_READWRITE = new PrivilegeBitset(PrivilegeGroupBitString::TABLE_READWRITE, MOCHOW_PRIVILEGE_COUNT_MAX);

static const std::unordered_map<std::string, const PrivilegeBitset*> g_privilege_bitset_map = {
    {PrivilegeGroupName::ALL,               g_privilege_bitset_ALL},
    {PrivilegeGroupName::SYSTEM_ALL,        g_privilege_bitset_SYSTEM_ALL},
    {PrivilegeGroupName::TABLE_ALL,         g_privilege_bitset_TABLE_ALL},
    {PrivilegeGroupName::TABLE_CONTROL,     g_privilege_bitset_TABLE_CONTROL},
    {PrivilegeGroupName::TABLE_READONLY,    g_privilege_bitset_TABLE_READONLY},
    {PrivilegeGroupName::TABLE_READWRITE,   g_privilege_bitset_TABLE_READWRITE},
};

static const PrivilegeBitset* get_privilege_group_bitset(const std::string& priv_group) {
    auto priv_bitset_iter = g_privilege_bitset_map.find(priv_group);
    if (priv_bitset_iter != g_privilege_bitset_map.end()) {
        return priv_bitset_iter->second;
    } else {
        return nullptr;
    }
}

static inline bool is_valid_privilege_group(const std::string& priv_group) {
    return get_privilege_group_bitset(priv_group) != nullptr;
}

static inline bool is_all_privilege_group(const std::string& priv_group) {
    return priv_group == PrivilegeGroupName::ALL;
}

static inline bool is_system_privilege_group(const std::string& priv_group) {
    return priv_group == PrivilegeGroupName::SYSTEM_ALL;
}

static inline bool is_system_privilege(const PrivilegeId priv_id) {
    return g_privilege_bitset_SYSTEM_ALL->test(priv_id);
}

static std::string translate_privilege_bitset_to_string(
            const std::bitset<MOCHOW_PRIVILEGE_COUNT_MAX>& priv_bitset) {
    std::string str;
    // Use MOCHOW_PRIVILEGE_COUNT but NOT priv_bitset.size()
    for (PrivilegeId priv_id = 0; priv_id < MOCHOW_PRIVILEGE_COUNT; ++priv_id) {
        if (priv_bitset.test(priv_id)) {
            str.append(get_privilege_name_by_id(priv_id)).append(",");
        }
    }
    if (!str.empty()) {
        return str.substr(0, str.size() - 1);
    }
    return str;
}

static void translate_privilege_bitset_to_string_vector(
            const std::bitset<MOCHOW_PRIVILEGE_COUNT_MAX>& priv_bitset,
            std::vector<const char*>* priv_strings) {
    // Use MOCHOW_PRIVILEGE_COUNT but NOT priv_bitset.size()
    for (PrivilegeId priv_id = 0; priv_id < MOCHOW_PRIVILEGE_COUNT; ++priv_id) {
        if (priv_bitset.test(priv_id)) {
            priv_strings->push_back(get_privilege_name_by_id(priv_id));
        }
    }
}

static bool is_valid_privilege_bitset_string(const char* priv_str) {
    if (priv_str == nullptr) { return false; }
    auto length = std::strlen(priv_str);
    if (length != MOCHOW_PRIVILEGE_COUNT_MAX) { return false; }
    for (size_t i = 0; i < length; ++i) {
        if (*(priv_str + i) != '0' && *(priv_str + i) != '1') { return false; }
    }
    return true;
}

static bool is_valid_privilege_bitset_string(const std::string& priv_string) {
    return is_valid_privilege_bitset_string(priv_string.c_str());
}

}
