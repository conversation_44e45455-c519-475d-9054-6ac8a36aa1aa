/***************************************************************************
 *
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

#pragma once

#include "core/src/common/meta/meta_base.h"

namespace mochow::master {
base::Status relocate_datanode_addr(const base::EndPoint&, const base::EndPoint&);
}

namespace mochow::common {

class Proxy final : public NodeBase {
public:
    Proxy(const base::EndPoint& addr, const std::string name)
            : NodeBase(addr), _name(name) {
        _is_alive = true;
    }
    ~Proxy() = default;

    std::string name() const {
        return _name;
    }
    std::string full_name() const {
        return common::endpoint2str(_addr) + ":" + _name;
    }
    void serialize(pb::PProxy* proxy_info) const {
        common::ScopedButex lock(_butex);
        proxy_info->mutable_proxy_addr()->CopyFrom(common::endpoint2node(_addr));
        proxy_info->set_name(_name);
        proxy_info->set_is_alive(_is_alive);
        proxy_info->set_version(_version);
    }
    mochow::pb::OperationMetrics operation_metrics() const {
        common::ScopedButex lock(_butex);
        return _metrics;
    }
    void set_operation_metrics(const mochow::pb::OperationMetrics& metrics) {
        common::ScopedButex lock(_butex);
        _metrics.CopyFrom(metrics);
    }
private:
    std::string _name;
    mochow::pb::OperationMetrics _metrics;
};

class ProxyManager final : public ManagerBase {
public:
    bool add_proxy(const ProxyRef& proxy);
    bool drop_proxy(const std::string& full_name);
    ProxyRef get_proxy(const std::string& full_name);
    void list_proxy(std::vector<ProxyRef>* proxy_list);

    std::string get_metrics();
    void refresh_metrics();

    void clear() {
        _proxy_map.clear();
        _metrics = "";
    }

private:
    std::map<std::string, ProxyRef> _proxy_map;
    std::string _metrics;
};

struct DataNodeOptions {
    base::EndPoint addr;
    std::string az_name;
    std::string rack_name;
    std::string state = common::DataNodeState::NORMAL;
    uint64_t create_time = 0; // microsceonds
    bool is_add_from_heartbeat = false;
    std::string version;
};

class DataNode final : public NodeBase, public std::enable_shared_from_this<DataNode> {
public:
    DataNode(const DataNodeOptions& options) :
            NodeBase(options.addr),
            _az_name(options.az_name),
            _rack_name(options.rack_name),
            _state(options.state),
            _create_time(options.create_time) {
        if (options.is_add_from_heartbeat) {
            _online_time = options.create_time;
            _last_active_timestamp = options.create_time / 1000000ul;
            _last_updated_timestamp = options.create_time / 1000000ul;
            _version = options.version;
            _is_alive = true;
        }
    }
    DataNode() : NodeBase() {}
    ~DataNode() = default;

    std::string az_name() const {
        common::ScopedButex lock(_butex);
        return _az_name;
    }
    void set_az_name(const std::string& az_name) {
        common::ScopedButex lock(_butex);
        _az_name = az_name;
    }
    std::string rack_name() const {
        common::ScopedButex lock(_butex);
        return _rack_name;
    }
    void set_rack_name(const std::string& rack_name) {
        common::ScopedButex lock(_butex);
        _rack_name = rack_name;
    }
    std::string state() const {
        common::ScopedButex lock(_butex);
        return _state;
    }
    void set_state(const std::string& state) {
        common::ScopedButex lock(_butex);
        _state = state;
    }
    uint64_t create_time() const {
        common::ScopedButex lock(_butex);
        return _create_time;
    }
    void set_create_time(uint64_t create_time) {
        common::ScopedButex lock(_butex);
        _create_time = create_time;
    }
    uint64_t offline_time() const {
        common::ScopedButex lock(_butex);
        return _offline_time;
    }
    void set_offline_time(uint64_t offline_time) {
        common::ScopedButex lock(_butex);
        _offline_time = offline_time;
    }
    uint64_t online_time() const {
        common::ScopedButex lock(_butex);
        return _online_time;
    }
    void set_online_time(uint64_t online_time) {
        common::ScopedButex lock(_butex);
        _online_time = online_time;
    }
    size_t replica_num() const {
        common::ScopedButex lock(_butex);
        return _replica_map.size();
    }

    ReplicaRef get_replica(uint64_t id);
    bool add_replica(const ReplicaRef& replica);
    bool drop_replica(uint64_t id);
    void list_replica(std::vector<ReplicaRef>* replica_list) const;
    void list_tablet_id(std::vector<uint64_t>* id_list) const;

    void serialize(pb::PDataNode* node_info) const;
    NODISCARD bool deserialize(const pb::PDataNode& info);

    void set_enable_encryption() {
        common::ScopedButex lock(_butex);
        _enable_encryption = true;
    }

    bool enable_encryption() const {
        common::ScopedButex lock(_butex);
        return _enable_encryption;
    }

    void clear_replica_without_lock();

    void clear_replica() {
        common::ScopedButex lock(_butex);
        clear_replica_without_lock();
    }

    void clear() {
        clear_replica_without_lock();
    }

private:
    std::map<uint64_t, ReplicaRef> _replica_map;
    std::string _az_name;
    std::string _rack_name;
    std::string _state;
    uint64_t _create_time = 0;  // microseconds
    uint64_t _offline_time = 0; // microseconds
    uint64_t _online_time = 0;  // microseconds

    friend base::Status mochow::master::relocate_datanode_addr(const base::EndPoint&, const base::EndPoint&);
    bool _enable_encryption = false;
};

class DataNodeManager final : public ManagerBase {
public:
    DataNodeRef get_node(const base::EndPoint& addr);
    bool add_node(const DataNodeRef& node_ptr);
    bool drop_node(const base::EndPoint& addr);
    void list_node(std::vector<DataNodeRef>* node_list);
    void list_node_addr(std::vector<uint64_t>* node_addr_list);
    void list_az(std::vector<std::string>* az_names);

    bool enable_encryption() { return _enable_encryption; }

    void check_enable_encryption() {
        common::ScopedButex lock(_butex);
        if (_enable_encryption) {
            return;
        }
        for (auto& [_, node] : _node_map) {
            if (!node->enable_encryption()) {
                return;
            }
        }
        _enable_encryption = true;
    }

    bool empty() {
        common::ScopedButex lock(_butex);
        return _node_map.empty();
    }

    void clear() {
        for (auto& [_, node] : _node_map) {
            node->clear();
        }
        _node_map.clear();
    }

private:
    std::map<base::EndPoint, DataNodeRef> _node_map;
    bool _enable_encryption = false;
};

}
