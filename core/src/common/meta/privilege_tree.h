/**
 * MOCHOW - A Vector Database System.
 * Copyright (c) 2024 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON> (<EMAIL>)
 * Date: 2024/06/07
 * Description: Declarations for Privilege Tree
 *
 */

#pragma once

#include <memory>
#include <bitset>
#include <string>
#include <ostream>
#include "sfl/type_traits.h"
#include "sfl/logging.h"
#include "core/src/common/butex.h"
#include "core/src/common/meta/privilege.h"
#include "baidu/vdb/mochow/core/src/proto/common.pb.h"

namespace mochow::common {

constexpr static size_t PCM = MOCHOW_PRIVILEGE_COUNT_MAX;

//=============================== RBAC Object =================================

struct RBACObject {
    std::string database;
    std::string table;

    RBACObject(const char* _database, const char* _table)
            : database(_database), table(_table) {}

    RBACObject(const std::string& _database, const std::string& _table)
            : database(_database), table(_table) {}

    // Default constructor only for deserialization
    RBACObject() = default;

    bool is_root() const { return database == "*" && table == "*"; }
    bool is_database() const { return database != "*" && table == "*"; }
    bool is_table() const { return database != "*" && table != "*"; }

    bool is_same_as(const RBACObject& object) const {
        return database == object.database && table == object.table;
    }

    bool is_parent_as(const RBACObject& object) const {
        if (is_root() && object.is_database()) {
            return true;
        }
        if (is_database() && object.is_table() && database == object.database) {
            return true;
        }
        return false;
    }

    bool is_child_as(const RBACObject& object) const {
        if (is_database() && object.is_root()) {
            return true;
        }
        if (is_table() && object.is_database() && database == object.database) {
            return true;
        }
        return false;
    }

    bool is_ancestor_as(const RBACObject& object) const {
        if (is_root() && (object.is_database() || object.is_table())) {
            return true;
        }
        if (is_database() && object.is_table() && database == object.database) {
            return true;
        }
        return false;
    }

    bool is_ancestor_or_same_as(const RBACObject& object) const {
        return is_ancestor_as(object) || is_same_as(object);
    }

    static bool is_root_rbac_object(const RBACObject& object) {
        return object.database == "*" && object.table == "*";
    }

    static bool is_valid_rbac_object(const RBACObject& object) {
        if (object.database.empty() || object.table.empty()) {
            return false;
        }
        if (object.database == "*" && object.table != "*") {
            return false;
        }
        return true;
    }

    bool deserialize(const mochow::pb::PRBACObject& pobj);
    void serialize(mochow::pb::PRBACObject* pobj) const;

    void clear() {
        database.clear();
        table.clear();
    }

    std::string to_string() const {
        return database + "." + table;
    }
};

inline std::ostream& operator<<(std::ostream& os, const RBACObject& object) {
    os << object.to_string();
    return os;
}

inline bool operator==(const RBACObject& lhs, const RBACObject& rhs) {
    return lhs.database == rhs.database && lhs.table == rhs.table;
}

inline bool operator!=(const RBACObject& lhs, const RBACObject& rhs) {
    return !(lhs == rhs);
}

inline bool operator<(const RBACObject& lhs, const RBACObject& rhs) {
    if (lhs.database < rhs.database) { return true; }
    else { return lhs.database == rhs.database && lhs.table < rhs.table; }
}

inline bool operator>(const RBACObject& lhs, const RBACObject& rhs) {
    if (lhs.database > rhs.database) { return true; }
    else { return lhs.database == rhs.database && lhs.table > rhs.table; }
}

inline bool operator<=(const RBACObject& lhs, const RBACObject& rhs) {
    return !(lhs > rhs);
}

inline bool operator>=(const RBACObject& lhs, const RBACObject& rhs) {
    return !(lhs < rhs);
}

//=========================== Privilege Tree Node =============================

class PrivilegeTreeNode;
using PrivilegeTreeNodeRef = std::shared_ptr<PrivilegeTreeNode>;
using PrivilegeTreeNodeMap = std::map<RBACObject, PrivilegeTreeNodeRef>;

class PrivilegeTreeNode {
public:
    PrivilegeTreeNode(const RBACObject& object, const size_t level)
            : _object(object), _level(level) {}
    ~PrivilegeTreeNode() {
        _parent.reset();
        _children.clear();
    }

    // Default constructor only for deserialization
    PrivilegeTreeNode() = default;

    uint32_t level() const { return _level; }
    bool is_implicit() const { return _is_implicit; }
    void set_implicit(bool is_implicit) { _is_implicit = is_implicit; }

    bool has_any_privilege() const { return _priv_bitset.any(); }

    void grant_privilege(const size_t priv_id) { _priv_bitset.set(priv_id); }
    void revoke_privilege(const size_t priv_id) { _priv_bitset.reset(priv_id); }

    void set_privilege_bitset(const std::bitset<PCM>& priv_bitset) {
        _priv_bitset = priv_bitset;
    }

    void grant_privileges_by_bitset(const std::bitset<PCM>& priv_bitset) {
        for (size_t i = 0; i < PCM; ++i) {
            if (priv_bitset.test(i)) {
                _priv_bitset.set(i);
            }
        }
    }

    void revoke_privileges_by_bitset(const std::bitset<PCM>& priv_bitset) {
        for (size_t i = 0; i < PCM; ++i) {
            if (priv_bitset.test(i)) {
                _priv_bitset.reset(i);
            }
        }
    }

    bool is_leaf() const { return _children.size() == 0; }
    void set_parent(const PrivilegeTreeNodeRef parent) { _parent = parent; }
    std::weak_ptr<PrivilegeTreeNode> get_parent() const { return _parent; }
    const std::set<PrivilegeTreeNodeRef>& get_children() { return _children; }

    void add_child(const PrivilegeTreeNodeRef node) {
        LOG_AND_ASSERT(node != nullptr);
        bool ok = _children.insert(node).second;
        LOG_AND_ASSERT(ok);
    }

    void remove_child(const PrivilegeTreeNodeRef node) {
        LOG_AND_ASSERT(node != nullptr);
        _children.erase(node);
    }

    const RBACObject& get_rbac_object() const { return _object; }
    std::bitset<PCM>& get_privilege_bitset() { return _priv_bitset; }

    bool deserialize(const mochow::pb::PPrivilegeTreeNode& pnode);
    void serialize(mochow::pb::PPrivilegeTreeNode* pnode) const;

    std::string to_string(bool is_human_readable = false) const {
        std::string str;
        str.append(_object.to_string()).append(" : ");
        str.append(!is_human_readable ? _priv_bitset.to_string()
                   : translate_privilege_bitset_to_string(_priv_bitset));
        return str;
    }

    void clear() {
        _object.clear();
        _level = 0;
        _is_implicit = false;
        _priv_bitset.reset();
        _parent.reset();
        _children.clear();
    }

private:
    RBACObject _object;
    uint32_t _level = 0;
    bool _is_implicit = false;
    std::bitset<PCM> _priv_bitset;
    std::weak_ptr<PrivilegeTreeNode> _parent;
    std::set<PrivilegeTreeNodeRef> _children;
};

inline bool operator==(const PrivilegeTreeNodeRef& lhs, const PrivilegeTreeNodeRef& rhs) {
    return lhs->get_rbac_object() == rhs->get_rbac_object();
}

inline bool operator!=(const PrivilegeTreeNodeRef& lhs, const PrivilegeTreeNodeRef& rhs) {
    return lhs->get_rbac_object() != rhs->get_rbac_object();
}

inline bool operator<(const PrivilegeTreeNodeRef& lhs, const PrivilegeTreeNodeRef& rhs) {
    return lhs->get_rbac_object() < rhs->get_rbac_object();
}

inline bool operator>(const PrivilegeTreeNodeRef& lhs, const PrivilegeTreeNodeRef& rhs) {
    return lhs->get_rbac_object() > rhs->get_rbac_object();
}

inline bool operator<=(const PrivilegeTreeNodeRef& lhs, const PrivilegeTreeNodeRef& rhs) {
    return lhs->get_rbac_object() <= rhs->get_rbac_object();
}

inline bool operator>=(const PrivilegeTreeNodeRef& lhs, const PrivilegeTreeNodeRef& rhs) {
    return lhs->get_rbac_object() >= rhs->get_rbac_object();
}

//============================ Privilege Tree ================================

class PrivilegeTree {
public:
    PrivilegeTree() = default;
    ~PrivilegeTree() = default;

    bool authorize(const std::string& database, const std::string& table,
                   const size_t priv_id);

    bool authorize(const std::string& database, const std::string& table,
                   const PrivilegeBitset& filter_priv_bitset);

    bool grant_privileges_by_bitset(const std::string& database, const std::string& table,
                                    const PrivilegeBitset& priv_bitset);

    bool revoke_privileges_by_bitset(const std::string& database, const std::string& table,
                                     const PrivilegeBitset& priv_bitset);

    bool grant_privilege(const std::string& database, const std::string& table,
                         const size_t priv_id);

    bool revoke_privilege(const std::string& database, const std::string& table,
                          const size_t priv_id);

    void display_tree(PrivilegeTreeNodeMap* tree_nodes,
                      bool is_display_implicit_node = false) const;

    bool deserialize(const mochow::pb::PPrivilegeTree& ptree);
    void serialize(mochow::pb::PPrivilegeTree* ptree) const;

    std::string to_string(bool is_human_readable = false) const;

    bool empty() const { return _root == nullptr; }

    void clear() { _root.reset(); }

private:
    PrivilegeTreeNodeRef establish_privilege_node_path(const RBACObject& object);

    void trim_tree();

private:
    common::Butex _butex;
    PrivilegeTreeNodeRef _root;
};

}
