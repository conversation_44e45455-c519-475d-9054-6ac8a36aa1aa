/**
 * MOCHOW - A Vector Database System.
 * Copyright (c) 2024 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON> (<EMAIL>)
 * Date: 2024/06/07
 * Description: Definitions for Privilege Tree
 *
 */

#include <list>
#include <stack>
#include <vector>
#include <exception>
#include "core/src/common/logging.h"
#include "core/src/common/pbutils.h"
#include "core/src/common/meta/privilege_tree.h"

namespace mochow::common {

bool RBACObject::deserialize(const mochow::pb::PRBACObject& pobj) {
    if (!pobj.has_database() || !pobj.has_table()) {
        LOG(ERROR) << "Fail to deserialize rbac object from pb due to missing some fields,"
                   << " has_database:" << pobj.has_database()
                   << " has_table:" << pobj.has_table();
        return false;
    }

    database = pobj.database();
    table = pobj.table();

    return true;
}

void RBACObject::serialize(mochow::pb::PRBACObject* pobj) const {
    pobj->set_database(database);
    pobj->set_table(table);
}

bool PrivilegeTreeNode::deserialize(const mochow::pb::PPrivilegeTreeNode& pnode) {
    clear();

    if (!_object.deserialize(pnode.object())) {
        LOG(ERROR) << "Fail to deserialize privilege tree node from pb due to deserialize rbac object failed";
        return false;
    }

    if (!pnode.has_level() || !pnode.has_is_implicit() || !pnode.has_priv_bitset()) {
        LOG(ERROR) << "Fail to deserialize privilege tree node from pb due to missing some fields,"
                   << " has_level:" << pnode.has_level()
                   << " has_is_implicit:" << pnode.has_is_implicit()
                   << " has_priv_bitset:" << pnode.has_priv_bitset();
        return false;
    }

    _level = pnode.level();
    _is_implicit = pnode.is_implicit();

    if (!is_valid_privilege_bitset_string(pnode.priv_bitset())) {
        LOG(ERROR) << "Fail to deserialize privilege tree node from pb due to priv bitset is invalid,"
                   << " priv_bitset:" << pnode.priv_bitset();
        return false;
    }

    try {
        PrivilegeBitset priv_bitset(pnode.priv_bitset().c_str(), pnode.priv_bitset().size());
        _priv_bitset = priv_bitset;
    } catch (std::exception& e) {
        LOG(ERROR) << "Fail to deserialize privilege tree node from pb due to parse priv bitset failed,"
                   << " priv_bitset:" << pnode.priv_bitset();
        return false;
    }

    return true;
}

void PrivilegeTreeNode::serialize(mochow::pb::PPrivilegeTreeNode* pnode) const {
    pnode->Clear();
    _object.serialize(pnode->mutable_object());
    pnode->set_level(_level);
    pnode->set_is_implicit(_is_implicit);
    pnode->set_priv_bitset(_priv_bitset.to_string());
}

bool PrivilegeTree::deserialize(const mochow::pb::PPrivilegeTree& ptree) {
    clear();

    // Emtpy tree
    if (ptree.nodes_size() == 0) {
        return true;
    }

    // Generate all nodes
    PrivilegeTreeNodeMap tree_nodes;
    for (int i = 0; i < ptree.nodes_size(); ++i) {
        auto node = std::make_shared<PrivilegeTreeNode>();
        if (!node->deserialize(ptree.nodes(i))) {
            LOG(ERROR) << "Fail to deserialize privilege tree from pb due to parse tree node failed";
            return false;
        }
        if (!tree_nodes.insert(std::make_pair(node->get_rbac_object(), node)).second) {
            LOG(ERROR) << "Fail to deserialize privilege tree from pb due to rbac object duplicated,"
                       << " object:" << common::pb2json(ptree.nodes(i).object());
            return false;
        }
    }

    // Construct whole tree
    size_t valid_node_count = 0;

    // 1. construct root node
    RBACObject root_object("*", "*");
    auto iter = tree_nodes.find(root_object);
    if (iter == tree_nodes.end()) {
        LOG(ERROR) << "Fail to deserialize privilege tree from pb due to cannot find root node";
        return false;
    }
    if (iter->second->level() != 1) {
        LOG(ERROR) << "Fail to deserialize privilege tree from pb due to root node level is not 1,"
                   << " level:" << iter->second->level();
        return false;
    }
    _root = iter->second;
    ++valid_node_count;

    // 2. construct database nodes
    for (const auto& [object, node] : tree_nodes) {
        if (node->level() != 2) {
            continue;
        }
        if (!object.is_child_as(root_object)) {
            LOG(ERROR) << "Fail to deserialize privilege tree from pb due to tree node with level 2 is not"
                       << " a child as root node, node_object:" << object.to_string();
            return false;
        }
        _root->add_child(node);
        node->set_parent(_root);
        ++valid_node_count;
    }

    // 3. construct table nodes
    for (const auto& [object, node] : tree_nodes) {
        if (node->level() != 3) {
            continue;
        }
        RBACObject parent_object(object.database, "*");
        iter = tree_nodes.find(parent_object);
        if (iter == tree_nodes.end()) {
            LOG(ERROR) << "Fail to deserialize privilege tree from pb due to cannot find parent node for tree"
                       << " node with level 3, node_object:" << object.to_string();
            return false;
        }
        if (iter->second->level() != 2) {
            LOG(ERROR) << "Fail to deserialize privilege tree from pb due to parent node of tree"
                       << " node with level 3 is not level 2,"
                       << " parent_node_object:" << iter->second->get_rbac_object().to_string()
                       << " parent_node_level:" << iter->second->level();
            return false;
        }
        auto parent = iter->second;
        parent->add_child(node);
        node->set_parent(parent);
        ++valid_node_count;
    }

    if (valid_node_count < tree_nodes.size()) {
        LOG(ERROR) << "Fail to deserialize privilege tree from pb due to protobuf message has some redundant"
                       << " tree nodes, valid_tree_nodes:" << valid_node_count
                       << " total_tree_nodes:" << tree_nodes.size();
        return false;
    }

    return true;
}

void PrivilegeTree::serialize(mochow::pb::PPrivilegeTree* ptree) const {
    ptree->Clear();

    PrivilegeTreeNodeMap tree_nodes;
    display_tree(&tree_nodes, true);

    for (const auto [_, node] : tree_nodes) {
        auto* pnode = ptree->add_nodes();
        node->serialize(pnode);
    }
}

static bool authorize_within_subtree(const PrivilegeTreeNodeRef node,
                                     const RBACObject& object,
                                     const PrivilegeId priv_id) {
    const auto& children = node->get_children();
    for (const auto child : children) {
        if (child->get_rbac_object().is_ancestor_or_same_as(object)) {
            return authorize_within_subtree(child, object, priv_id);
        }
    }

    return node->get_privilege_bitset().test(priv_id);
}

bool PrivilegeTree::authorize(const std::string& database,
                              const std::string& table,
                              const size_t priv_id) {
    RBACObject object(database, table);
    if (!RBACObject::is_valid_rbac_object(object)) {
        return false;
    }

    SCOPED_LOCK_BUTEX(_butex);

    if (_root == nullptr) { return false; }

    return authorize_within_subtree(_root, object, priv_id);
}

static bool authorize_within_subtree(const PrivilegeTreeNodeRef node,
                                     const RBACObject& object,
                                     const PrivilegeBitset& filter_priv_bitset) {
    const auto& children = node->get_children();
    for (const auto child : children) {
        if (child->get_rbac_object().is_ancestor_or_same_as(object)) {
            return authorize_within_subtree(child, object, filter_priv_bitset);
        }
    }

    const auto& priv_bitset = node->get_privilege_bitset();
    for (PrivilegeId priv_id = 0; priv_id < priv_bitset.size(); ++priv_id) {
        if (filter_priv_bitset.test(priv_id) && !priv_bitset.test(priv_id)) {
            return false;
        }
    }

    return true;
}

bool PrivilegeTree::authorize(const std::string& database,
                              const std::string& table,
                              const PrivilegeBitset& filter_priv_bitset) {
    RBACObject object(database, table);
    if (!RBACObject::is_valid_rbac_object(object)) {
        return false;
    }

    SCOPED_LOCK_BUTEX(_butex);

    if (_root == nullptr) { return false; }

    return authorize_within_subtree(_root, object, filter_priv_bitset);
}

PrivilegeTreeNodeRef PrivilegeTree::establish_privilege_node_path(
                                    const RBACObject& object) {

    PrivilegeTreeNodeRef sub_tree_root = nullptr;
    std::list<PrivilegeTreeNodeRef> node_path;
    node_path.push_back(_root);
    while (!node_path.empty()) {
        const auto node = node_path.front();
        node_path.pop_front();

        if (node->get_rbac_object().is_same_as(object)) {
            node->set_implicit(false);
            sub_tree_root = node;
            break;
        }

        const auto children = node->get_children();
        bool is_child_found = false;
        for (const auto child : children) {
            if (child->get_rbac_object().is_ancestor_or_same_as(object)) {
                is_child_found = true;
                node_path.push_back(child);
                break;
            }
        }

        if (!is_child_found) {
            auto level = node->level() + 1;
            LOG_AND_ASSERT(level > 1);
            RBACObject level_object(object.database, level >= 3 ? object.table : "*");
            auto level_node = std::make_shared<PrivilegeTreeNode>(level_object, level);
            level_node->set_privilege_bitset(node->get_privilege_bitset());
            level_node->set_parent(node);
            level_node->set_implicit(level_node->get_rbac_object().is_ancestor_as(object));
            node->add_child(level_node);
            node_path.push_back(level_node);
        }
    }

    return sub_tree_root;
}

void PrivilegeTree::display_tree(PrivilegeTreeNodeMap* tree_nodes,
                                 bool is_display_implicit_node) const {
    LOG_AND_ASSERT(tree_nodes != nullptr);
    LOG_AND_ASSERT(tree_nodes->empty());

    if (_root == nullptr) { return; }

    std::vector<PrivilegeTreeNodeRef> nodes;
    nodes.push_back(_root);
    for(size_t idx = 0; idx < nodes.size(); ++idx) {
        const auto children = nodes[idx]->get_children();
        for (const auto child : children) {
            nodes.push_back(child);
        }
    }

    for (const auto node : nodes) {
        if (node->is_implicit() && !is_display_implicit_node) {
            continue;
        }

        bool ok = tree_nodes->insert(
                std::make_pair(node->get_rbac_object(), node)).second;
        LOG_AND_ASSERT(ok);
    }
}

void PrivilegeTree::trim_tree() {
    if (_root == nullptr) { return; }

    // Iterates the whole tree by BFS.
    std::vector<PrivilegeTreeNodeRef> nodes;
    nodes.push_back(_root);
    for (size_t idx = 0; idx < nodes.size(); ++idx) {
        const auto children = nodes[idx]->get_children();
        for (const auto child : children) {
            nodes.push_back(child);
        }
    }

    // From leaf to root, check and trim the node which has exactly
    // same with its parent' privileges.
    for (size_t idx = nodes.size() - 1; idx > 0; --idx) {
        auto parent_wptr = nodes[idx]->get_parent();
        LOG_AND_ASSERT(!parent_wptr.expired());
        auto& parent = (*parent_wptr.lock());
        if (nodes[idx]->get_privilege_bitset() == parent.get_privilege_bitset()) {
            if (nodes[idx]->is_leaf()) {
                nodes[idx]->set_parent(nullptr);
                parent.remove_child(nodes[idx]);
            } else {
                nodes[idx]->set_implicit(true);
            }
        }
    }

    if (_root->is_leaf() && !_root->has_any_privilege()) {
        _root.reset();
    }
}

bool PrivilegeTree::grant_privilege(const std::string& database,
                                    const std::string& table,
                                    const size_t priv_id) {
    RBACObject object(database, table);
    if (!RBACObject::is_valid_rbac_object(object)) {
        return false;
    }

    SCOPED_LOCK_BUTEX(_butex);

    // If root is null, create a root with empty privileges.
    if (_root == nullptr) {
        RBACObject root_rbac_object("*", "*");
        _root = std::make_shared<PrivilegeTreeNode>(root_rbac_object, 1);
        _root->set_implicit(true);
    }

    // Check and establish the whole node path if target node not exist,
    // and return the sub tree node.
    auto sub_tree_root = establish_privilege_node_path(object);
    LOG_AND_ASSERT(sub_tree_root != nullptr);

    std::stack<PrivilegeTreeNodeRef> node_stack;
    node_stack.push(sub_tree_root);

    while (!node_stack.empty()) {
        // Grant privilege
        auto node = node_stack.top();
        node_stack.pop();
        node->grant_privilege(priv_id);

        const auto children = node->get_children();
        for (const auto child : children) {
            node_stack.push(child);
        }
    }

    trim_tree();

    return true;
}

bool PrivilegeTree::revoke_privilege(const std::string& database,
                                     const std::string& table,
                                     const size_t priv_id) {
    RBACObject object(database, table);
    if (!RBACObject::is_valid_rbac_object(object)) {
        return false;
    }

    SCOPED_LOCK_BUTEX(_butex);

    // If root is null, then do nothing, seen as succeeded.
    if (_root == nullptr) { return true; }

    // Check and establish the whole node path if target node not exist,
    // and return the sub tree node.
    auto sub_tree_root = establish_privilege_node_path(object);
    LOG_AND_ASSERT(sub_tree_root != nullptr);

    std::stack<PrivilegeTreeNodeRef> node_stack;
    node_stack.push(sub_tree_root);

    while (!node_stack.empty()) {
        // Grant privilege
        auto node = node_stack.top();
        node_stack.pop();
        node->revoke_privilege(priv_id);

        const auto children = node->get_children();
        for (const auto child : children) {
            node_stack.push(child);
        }
    }

    trim_tree();

    return true;
}

bool PrivilegeTree::grant_privileges_by_bitset(
                                    const std::string& database,
                                    const std::string& table,
                                    const PrivilegeBitset& priv_bitset) {
    RBACObject object(database, table);
    if (!RBACObject::is_valid_rbac_object(object)) {
        return false;
    }

    SCOPED_LOCK_BUTEX(_butex);

    // If root is null, create a root with empty privileges.
    if (_root == nullptr) {
        RBACObject root_rbac_object("*", "*");
        _root = std::make_shared<PrivilegeTreeNode>(root_rbac_object, 1);
        _root->set_implicit(true);
    }

    // Check and establish the whole node path if target node not exist,
    // and return the sub tree node.
    auto sub_tree_root = establish_privilege_node_path(object);
    LOG_AND_ASSERT(sub_tree_root != nullptr);

    std::stack<PrivilegeTreeNodeRef> node_stack;
    node_stack.push(sub_tree_root);

    while (!node_stack.empty()) {
        // Grant privilege
        auto node = node_stack.top();
        node_stack.pop();
        node->grant_privileges_by_bitset(priv_bitset);

        const auto children = node->get_children();
        for (const auto child : children) {
            node_stack.push(child);
        }
    }

    trim_tree();

    return true;
}

bool PrivilegeTree::revoke_privileges_by_bitset(
                                    const std::string& database,
                                    const std::string& table,
                                    const PrivilegeBitset& priv_bitset) {
    RBACObject object(database, table);
    if (!RBACObject::is_valid_rbac_object(object)) {
        return false;
    }

    SCOPED_LOCK_BUTEX(_butex);

    // If root is null, then do nothing, seen as succeeded.
    if (_root == nullptr) { return true; }

    // Check and establish the whole node path if target node not exist,
    // and return the sub tree node.
    auto sub_tree_root = establish_privilege_node_path(object);
    LOG_AND_ASSERT(sub_tree_root != nullptr);

    std::stack<PrivilegeTreeNodeRef> node_stack;
    node_stack.push(sub_tree_root);

    while (!node_stack.empty()) {
        // Grant privilege
        auto node = node_stack.top();
        node_stack.pop();
        node->revoke_privileges_by_bitset(priv_bitset);

        const auto children = node->get_children();
        for (const auto child : children) {
            node_stack.push(child);
        }
    }

    trim_tree();

    return true;
}

std::string PrivilegeTree::to_string(bool is_human_readable) const {
    PrivilegeTreeNodeMap tree_nodes;
    display_tree(&tree_nodes);

    std::string str = "\n";
    for (const auto& [object, node] : tree_nodes) {
        str.append(object.to_string()).append(" : ");
        str.append(!is_human_readable ? node->get_privilege_bitset().to_string()
                   : translate_privilege_bitset_to_string(node->get_privilege_bitset()));
        str.append("\n");
    }

    return str;
}

}
