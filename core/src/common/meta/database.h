/***************************************************************************
 *
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

#pragma once

#include "core/src/common/meta/meta_base.h"
#include "core/src/schema/id_allocator.h"
#include "core/src/schema/index_species.h"

namespace mochow::common {

class IndexBase : public MetaBase {
public:
    IndexBase() = default;
    ~IndexBase() = default;

    std::string index_species() const {
        return _index_species;
    }

protected:
    std::string _index_species = schema::IndexSpecies::INVALID;
};

using IndexBaseRef = std::shared_ptr<IndexBase>;

class ScalarIndex final : public IndexBase {
public:
    ScalarIndex() {
        IndexBase::_index_species = schema::IndexSpecies::SCALAR;
    }
    ~ScalarIndex() = default;

    IDXID id() const {
        return _id;
    }
    std::string name() const {
        return _name;
    }
    std::string state() const {
        common::ScopedButex lock(_butex);
        return _state;
    }
    void set_state(const std::string& state) {
        common::ScopedButex lock(_butex);
        _state = state;
    }
    mochow::schema::ScalarIndexSchema get_schema() const {
        common::ScopedButex lock(_butex);
        return _schema;
    }
    void set_schema(const mochow::schema::ScalarIndexSchema& schema) {
        common::ScopedButex lock(_butex);
        _schema = schema;
    }
    void serialize_only_schema(mochow::pb::PScalarIndexSchema* pschema) const;
    void serialize(mochow::pb::PScalarIndex* psindex) const;
    NODISCARD bool deserialize(const mochow::pb::PScalarIndex& psindex);

private:
    IDXID _id = 0;
    std::string _name;
    std::string _state = common::IndexState::INVALID;
    mochow::schema::ScalarIndexSchema _schema;
};

using ScalarIndexRef = std::shared_ptr<ScalarIndex>;

class VectorIndex final : public IndexBase {
public:
    VectorIndex() {
        IndexBase::_index_species = schema::IndexSpecies::VECTOR;
    }
    ~VectorIndex() = default;

    IDXID id() const {
        return _id;
    }
    std::string name() const {
        return _name;
    }
    void set_index_version(IDXVERSION version) {
        common::ScopedButex lock(_butex);
        _index_version = version;
    }
    IDXVERSION index_version() const {
        common::ScopedButex lock(_butex);
        return _index_version;
    }
    void set_expected_index_version(IDXVERSION version) {
        common::ScopedButex lock(_butex);
        _expected_index_version = version;
    }
    IDXVERSION expected_index_version() const {
        common::ScopedButex lock(_butex);
        return _expected_index_version;
    }
    std::string state() const {
        common::ScopedButex lock(_butex);
        return _state;
    }
    void set_state(const std::string& state) {
        common::ScopedButex lock(_butex);
        _state = state;
    }
    mochow::schema::VectorIndexSchema get_schema() const {
        common::ScopedButex lock(_butex);
        return _schema;
    }

    void serialize_only_schema(mochow::pb::PVectorIndexSchema* pschema) const;
    void serialize(mochow::pb::PVectorIndex* pvindex) const;
    NODISCARD bool deserialize(const mochow::pb::PVectorIndex& pvindex);

    void set_is_auto_build(bool is_auto_build) {
        common::ScopedButex lock(_butex);
        _is_auto_build = is_auto_build;
    }

    bool is_auto_build() {
        common::ScopedButex lock(_butex);
        return _is_auto_build;
    }

    void set_auto_build_index_param(const mochow::pb::AutoBuildIndexParam& auto_build_index_param) {
        common::ScopedButex lock(_butex);
        LOG_AND_ASSERT(_is_auto_build);
        _auto_build_index_param.CopyFrom(auto_build_index_param);
    }
    void clear_auto_build_index_param() {
        common::ScopedButex lock(_butex);
        _auto_build_index_param.Clear();
    }

    mochow::pb::AutoBuildIndexParam auto_build_index_param() {
        common::ScopedButex lock(_butex);
        return _auto_build_index_param;
    }

private:
    IDXID _id = 0;
    std::string _name;
    bool _is_auto_build = false;
    IDXVERSION _index_version = 0;
    IDXVERSION _expected_index_version = 0;
    std::string _state = common::IndexState::INVALID;
    mochow::schema::VectorIndexSchema _schema;
    mochow::pb::AutoBuildIndexParam _auto_build_index_param;
};

using VectorIndexRef = std::shared_ptr<VectorIndex>;

class InvertedIndex final : public IndexBase {
public:
    InvertedIndex() {
        IndexBase::_index_species = schema::IndexSpecies::INVERTED;
    }

    ~InvertedIndex() = default;

    IDXID id() const {
        return _id;
    }
    std::string name() const {
        return _name;
    }
    std::string state() const {
        common::ScopedButex lock(_butex);
        return _state;
    }
    void set_state(const std::string& state) {
        common::ScopedButex lock(_butex);
        _state = state;
    }

    mochow::schema::InvertedIndexSchema get_schema() const {
        common::ScopedButex lock(_butex);
        return _schema;
    }

    void serialize_only_schema(mochow::pb::PInvertedIndexSchema* pindex) const;

    void serialize(mochow::pb::PInvertedIndex* pindex) const;

    NODISCARD bool deserialize(const mochow::pb::PInvertedIndex& pindex);

private:
    IDXID _id = 0;
    std::string _name;
    std::string _state = common::IndexState::INVALID;
    mochow::schema::InvertedIndexSchema _schema;
};

using InvertedIndexRef = std::shared_ptr<InvertedIndex>;
using InvertedIndexPtr = std::unique_ptr<InvertedIndex>;

class Table final : public MetaBase {
public:
    Table(const DBRef db) : _db(db) {
        _column_id_allocator = std::make_shared<schema::IdAllocator>();
        _index_id_allocator = std::make_shared<schema::IdAllocator>();
    }

    ~Table() = default;

    DBRef db() const {
        return _db;
    }
    TBLID id() const {
        return _id;
    }
    std::string name() const {
        return _name;
    }
    void set_replication(int32_t replication) {
        common::ScopedButex lock(_butex);
        _replication = replication;
    }
    int32_t replication() const {
        common::ScopedButex lock(_butex);
        return _replication;
    }
    uint64_t create_time() const {
        common::ScopedButex lock(_butex);
        return _create_time;
    }
    void set_create_time(uint64_t create_time) {
        common::ScopedButex lock(_butex);
        _create_time = create_time;
    }
    mochow::pb::PPartition partition() {
        common::ScopedButex lock(_butex);
        return _partition;
    }
    void set_partition(const mochow::pb::PPartition& partition) {
        common::ScopedButex lock(_butex);
        _partition = partition;
    }
    std::string description() const {
        common::ScopedButex lock(_butex);
        return _description;
    }
    void set_description(const std::string& description) {
        common::ScopedButex lock(_butex);
        _description = description;
    }
    void set_max_tp_id(const TPID max_tp_id) {
        common::ScopedButex lock(_butex);
        _max_tp_id = max_tp_id;
    }
    uint64_t schema_version() const {
        common::ScopedButex lock(_butex);
        return _schema_version;
    }
    void set_schema_version(const uint64_t schema_version) {
        common::ScopedButex lock(_butex);
        _schema_version = schema_version;
    }
    std::string state() const {
        common::ScopedButex lock(_butex);
        return _state;
    }
    void set_state(const std::string& state) {
        common::ScopedButex lock(_butex);
        _state = state;
    }
    bool enable_dynamic_field() const {
        common::ScopedButex lock(_butex);
        return _enable_dynamic_field;
    }
    void set_enable_dynamic_field(bool enable_dynamic_field) {
        common::ScopedButex lock(_butex);
        _enable_dynamic_field = enable_dynamic_field;
    }
    schema::IdAllocatorRef column_id_allocator() const {
        return _column_id_allocator;
    }
    schema::IdAllocatorRef index_id_allocator() const {
        return _index_id_allocator;
    }
    mochow::schema::TableSchema get_table_schema() const {
        common::ScopedButex lock(_butex);
        return _schema;
    }
    void set_table_schema(const mochow::schema::TableSchema& schema) {
        common::ScopedButex lock(_butex);
        _schema = schema;
    }
    ScalarIndexRef find_scalar_index(const std::string& index_name) {
        common::ScopedButex lock(_butex);
        auto iter = _scalar_indexes.find(index_name);
        return iter != _scalar_indexes.end() ? iter->second : nullptr;
    }
    ScalarIndexRef find_scalar_index(const IDXID index_id) {
        common::ScopedButex lock(_butex);
        for (const auto& [_, index] : _scalar_indexes) {
            if (index->id() == index_id) {
                return index;
            }
        }
        return nullptr;
    }
    bool add_scalar_index(const ScalarIndexRef index) {
        common::ScopedButex lock(_butex);
        return _scalar_indexes.insert(std::make_pair(index->name(), index)).second;
    }
    void remove_scalar_index(const std::string& index_name) {
        common::ScopedButex lock(_butex);
        _scalar_indexes.erase(index_name);
    }
    VectorIndexRef find_vector_index(const std::string& index_name) {
        common::ScopedButex lock(_butex);
        auto iter = _vector_indexes.find(index_name);
        return iter != _vector_indexes.end() ? iter->second : nullptr;
    }
    VectorIndexRef find_vector_index(const IDXID index_id) {
        common::ScopedButex lock(_butex);
        for (const auto& [_, index] : _vector_indexes) {
            if (index->id() == index_id) {
                return index;
            }
        }
        return nullptr;
    }
    bool add_vector_index(const VectorIndexRef index) {
        common::ScopedButex lock(_butex);
        return _vector_indexes.insert(std::make_pair(index->name(), index)).second;
    }
    void remove_vector_index(const std::string& index_name) {
        auto vector_index = find_vector_index(index_name);
        if (vector_index == nullptr) {
            return;
        }

        common::ScopedButex lock(_butex);
        for (const auto& [_, tablet] : _tablet_map) {
            tablet->remove_index_ctx(vector_index->id());
        }
        _vector_indexes.erase(index_name);
    }

    InvertedIndexRef find_inverted_index(const std::string& index_name) {
        common::ScopedButex lock(_butex);
        auto iter = _inverted_indexes.find(index_name);

        if (iter != _inverted_indexes.end()) {
            return iter->second;
        }

        return nullptr;
    }

    bool add_inverted_index(const InvertedIndexRef index) {
        common::ScopedButex lock(_butex);
        return _inverted_indexes.emplace(index->name(), index).second;
    }

    void remove_inverted_index(const std::string& name) {
        common::ScopedButex lock(_butex);
        _inverted_indexes.erase(name);
    }


    std::map<std::string, VectorIndexRef> list_vector_index() {
        common::ScopedButex lock(_butex);
        return _vector_indexes;
    }

    size_t tablet_num() const {
        common::ScopedButex lock(_butex);
        return _tablet_map.size();
    }
    void add_alias(const std::string& alias) {
        common::ScopedButex lock(_butex);
        _aliases.insert(alias);
    }
    void remove_alias(const std::string& alias) {
        common::ScopedButex lock(_butex);
        _aliases.erase(alias);
    }
    const std::set<std::string> list_alias() {
        common::ScopedButex lock(_butex);
        return _aliases;
    }

    mochow::pb::TableMetrics metric() {
        common::ScopedButex lock(_butex);
        return _metrics;
    }

    void set_metric(const mochow::pb::TableMetrics& metrics) {
        common::ScopedButex lock(_butex);
        _metrics = metrics;
    }

    bool is_index_ready(const IDXID index_id, const IDXVERSION index_version) const {
        common::ScopedButex lock(_butex);
        for (const auto& [_, tablet] : _tablet_map) {
            if (!tablet->is_index_ready(index_id, index_version)) {
                return false;
            }
        }
        return true;
    }

    void list_scalar_index(std::vector<ScalarIndexRef>* indexes) const;

    void list_vector_index(std::vector<VectorIndexRef>* indexes) const;

    void list_inverted_index(std::vector<InvertedIndexRef>* indexes) const;

    IndexBaseRef show_index(const std::string& index_name) const;

    void list_building_index(std::vector<std::pair<IDXID, IDXVERSION>>* index_list) const;

    bool is_all_tablet_normal();

    bool add_tablet(const TabletRef& tablet);

    bool drop_tablet(TPID id);

    TabletRef get_tablet(TPID id);

    void list_tablet(std::vector<TabletRef>* tablet_list);

    uint64_t get_ttl() const {
        return _ttl;
    }

    void set_datanode_memory_reserved_in_gb(const double& datanode_memory_reserved_in_gb) {
        common::ScopedButex lock(_butex);
        _datanode_memory_reserved_in_gb = datanode_memory_reserved_in_gb;
    }
    double datanode_memory_reserved_in_gb() const {
        common::ScopedButex lock(_butex);
        return _datanode_memory_reserved_in_gb;
    }

    bool is_scalar_index_ready(const IDXID index_id) const {
        common::ScopedButex lock(_butex);
        for (const auto& [_, tablet] : _tablet_map) {
            if (!tablet->is_scalar_index_ready(index_id)) {
                return false;
            }
        }
        return true;
    }

    void serialize_only_schema(mochow::pb::PSchema* pschema) const;

    void serialize(mochow::pb::PTable* info) const;

    NODISCARD bool deserialize(const mochow::pb::PTable& info);

    void clear_tablet() {
        common::ScopedButex lock(_butex);
        for (auto& [_, tablet] : _tablet_map) {
            tablet->clear_replica();
        }
        _tablet_map.clear();
    }

    void clear() {
        for (auto& [_, tablet] : _tablet_map) {
            tablet->clear_replica();
        }
        _tablet_map.clear();
        for (auto& [_, index] : _scalar_indexes) {
            index.reset();
        }
        _scalar_indexes.clear();
        for (auto& [_, index] : _vector_indexes) {
            index.reset();
        }
        _vector_indexes.clear();
        _inverted_indexes.clear();
        _aliases.clear();
        _column_id_allocator.reset();
        _index_id_allocator.reset();
    }

private:
    TBLID _id = 0;
    DBRef _db;
    std::map<TPID, TabletRef> _tablet_map;
    std::string _name;
    int32_t _replication = 0;
    uint64_t _create_time = 0;
    std::string _description;
    std::string _state = common::TableState::NORMAL;
    bool _enable_dynamic_field = false;
    TPID _max_tp_id = 0;
    schema::IdAllocatorRef _column_id_allocator;
    schema::IdAllocatorRef _index_id_allocator;
    std::map<std::string, ScalarIndexRef> _scalar_indexes;
    std::map<std::string, VectorIndexRef> _vector_indexes;
    std::map<std::string, InvertedIndexRef> _inverted_indexes;
    mochow::pb::PPartition _partition;
    mochow::schema::TableSchema _schema;
    uint64_t _schema_version = 0;
    std::set<std::string> _aliases;
    uint64_t _ttl = DEFAULT_TTL;
    double _datanode_memory_reserved_in_gb = 0;
    // collect from datanode_heartbeat; won't persist them since it's metric.
    mochow::pb::TableMetrics _metrics;
};

class Database final : public MetaBase {
public:
    Database() = default;
    ~Database() = default;

    DBID id() const {
        return _id;
    }
    std::string name() const {
        return _name;
    }
    std::string description() const {
        common::ScopedButex lock(_butex);
        return _description;
    }
    void set_description(const std::string& description) {
        common::ScopedButex lock(_butex);
        _description = description;
    }
    bool empty() const {
        common::ScopedButex lock(_butex);
        return _table_map.empty();
    }
    size_t table_num() const {
        common::ScopedButex lock(_butex);
        return _table_map.size();
    }

    bool add_table(const TableRef& table);
    bool drop_table(const std::string& name);
    TableRef get_table(const std::string& name);
    void list_table(std::vector<TableRef>* table_list);
    void clear_table() {
        common::ScopedButex lock(_butex);
        _table_map.clear();
    }

    bool alias_table(const std::string& table_name, const std::string& alias);
    void unalias_table(const std::string& alias);

    void serialize(mochow::pb::PDatabase* info) const;
    NODISCARD bool deserialize(const mochow::pb::PDatabase& info);

    void clear() {
        for (auto& [_, table]: _table_map) {
            table->clear();
        }
        _table_map.clear();
        _table_aliases.clear();
    }

private:
    std::map<std::string, TableRef> _table_map;
    DBID _id = 0;
    std::string _name;
    std::string _description;
    std::map<std::string, std::string> _table_aliases; // alias -> source_table_name
};

class DatabaseManager final : public ManagerBase {
public:
    bool add_db(const DBRef db);
    bool drop_db(const std::string& name);
    bool add_table(const TableRef table);
    bool drop_table(const TBLID table_id);

    DBRef get_db(const std::string& name);
    void list_db(std::vector<DBRef>* db_list);

    TableRef get_table(const TBLID table_id);
    TableRef get_table(const std::string& db_name, const std::string& table_name);
    void list_table(std::vector<TableRef>* table_list);

    bool add_tablet(const TabletRef& tablet);
    bool drop_tablet(uint64_t id);
    TabletRef get_tablet(uint64_t id);
    void list_tablet(std::vector<TabletRef>* tablet_list);
    size_t replica_num();

    void clear() {
        for (auto& [_, tablet] : _tablet_map) {
            tablet->clear();
        }
        _tablet_map.clear();
        for (auto& [_, table] : _table_map) {
            table->clear();
        }
        _table_map.clear();
        for (auto& [_, db] : _db_map) {
            db->clear();
        }
        _db_map.clear();
    }

private:
    std::map<uint64_t, TabletRef> _tablet_map;
    std::map<TBLID, TableRef> _table_map;
    std::map<std::string, DBRef> _db_map;
};

}
