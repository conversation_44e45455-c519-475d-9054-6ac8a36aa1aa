#pragma once

#include <string_view>
#include <unordered_set>
#include "core/src/common/meta/meta_base.h"
#include "core/src/common/meta/privilege.h"
#include "core/src/common/meta/privilege_tree.h"

// The password string is an encrypted string with hex format. How to check:
// 1. Call g_password_aes_encryptor->aes_encrypt() the password plain text to cipher text;
// 2. Call binary_to_hex_string() to transfer the password cipher text to readable format.
constexpr const char* S_MOCHOW_ROOT_USERNAME = "root";
constexpr const char* S_MOCHOW_ROOT_PASSWORD = "128f6f7670d3508d43cd920d709d9de2";

constexpr const char* S_MOCHOW_SYSTEM_USERNAME = "__system";
constexpr const char* S_MOCHOW_SYSTEM_PASSWORD = "cb591795db101c8df137073739817210";

constexpr const char* S_MOCHOW_BUILTIN_ADMIN_ROLE = "ADMIN";

static inline bool is_root_user(const std::string& username) {
    return username == S_MOCHOW_ROOT_USERNAME;
}

static inline bool is_system_user(const std::string& username) {
    return username == S_MOCHOW_SYSTEM_USERNAME;
}

static inline bool is_builtin_user(const std::string& username) {
    return is_root_user(username) || is_system_user(username);
}

static inline bool is_builtin_admin_role(const std::string& role_name) {
    return role_name == S_MOCHOW_BUILTIN_ADMIN_ROLE;
}

namespace mochow::common {

class Role final : public MetaBase {
public:
    Role() = default;
    ~Role() = default;

    Role(const std::string& name, const std::string& parent)
            : _name(name), _parent(parent) {}

    std::string name() const {
        SCOPED_LOCK_BUTEX(_butex);
        return _name;
    }

    std::string get_parent() const {
        SCOPED_LOCK_BUTEX(_butex);
        return _parent;
    }

    void set_parent(const std::string& parent) {
        SCOPED_LOCK_BUTEX(_butex);
        _parent = parent;
    }

    bool authorize(const std::string& database, const std::string& table,
                   const PrivilegeId priv_id) {
        SCOPED_LOCK_BUTEX(_butex);
        return _priv_tree.authorize(database, table, priv_id);
    }

    bool authorize(const std::string& database, const std::string& table,
                   const PrivilegeBitset& priv_bitset) {
        SCOPED_LOCK_BUTEX(_butex);
        return _priv_tree.authorize(database, table, priv_bitset);
    }

    void show_privileges(std::map<RBACObject, PrivilegeBitset>* privileges) {
        SCOPED_LOCK_BUTEX(_butex);
        PrivilegeTreeNodeMap tree_nodes;
        _priv_tree.display_tree(&tree_nodes, false);
        for (const auto& [object, node] : tree_nodes) {
            privileges->insert(std::make_pair(object, node->get_privilege_bitset()));
        }
    }

    bool grant_privileges_by_bitset(const std::string& database, const std::string& table,
                                    const PrivilegeBitset& priv_bitset) {
        SCOPED_LOCK_BUTEX(_butex);
        return _priv_tree.grant_privileges_by_bitset(database, table, priv_bitset);
    }

    bool revoke_privileges_by_bitset(const std::string& database, const std::string& table,
                                     const PrivilegeBitset& priv_bitset) {
        SCOPED_LOCK_BUTEX(_butex);
        return _priv_tree.revoke_privileges_by_bitset(database, table, priv_bitset);
    }

    bool grant_privilege(const std::string& database, const std::string& table,
                         const PrivilegeId priv_id) {
        SCOPED_LOCK_BUTEX(_butex);
        return _priv_tree.grant_privilege(database, table, priv_id);
    }

    bool revoke_privilege(const std::string& database, const std::string& table,
                          const PrivilegeId priv_id) {
        SCOPED_LOCK_BUTEX(_butex);
        return _priv_tree.revoke_privilege(database, table, priv_id);
    }

    bool deserialize(const mochow::pb::PRole& prole);
    void serialize(mochow::pb::PRole* prole) const;

    void clear() {
        _name.clear();
        _priv_tree.clear();
    }

private:
    std::string _name;
    std::string _parent;
    PrivilegeTree _priv_tree;
};

using RoleRef = std::shared_ptr<Role>;

class User final : public MetaBase {
public:
    User() = default;
    ~User() = default;

    User(const std::string& username, const std::string& password,
         const std::string& parent)
        : _username(username), _password(password), _parent(parent) {}

    std::string username() {
        SCOPED_LOCK_BUTEX(_butex);
        return _username;
    }

    std::string password() {
        SCOPED_LOCK_BUTEX(_butex);
        return _password;
    }

    bool authenticate(const std::string& username, const std::string& password) {
        SCOPED_LOCK_BUTEX(_butex);
        return username == _username && password == _password;
    }

    bool reset_password(const std::string& username, const std::string& password) {
        SCOPED_LOCK_BUTEX(_butex);
        if (username != _username) { return false; }
        _password = password;
        return true;
    }

    bool authorize(const std::string& database, const std::string& table,
                   const PrivilegeId priv_id) {
        SCOPED_LOCK_BUTEX(_butex);
        for (const auto& [_, role] : _roles) {
            if (role->authorize(database, table, priv_id)) { return true; }
        }
        return _priv_tree.authorize(database, table, priv_id);
    }

    bool authorize(const std::string& database, const std::string& table,
                   const PrivilegeBitset& priv_bitset) {
        SCOPED_LOCK_BUTEX(_butex);
        for (const auto& [_, role] : _roles) {
            if (role->authorize(database, table, priv_bitset)) { return true; }
        }
        return _priv_tree.authorize(database, table, priv_bitset);
    }

    bool grant_privileges_by_bitset(const std::string& database, const std::string& table,
                                    const PrivilegeBitset& priv_bitset) {
        SCOPED_LOCK_BUTEX(_butex);
        return _priv_tree.grant_privileges_by_bitset(database, table, priv_bitset);
    }

    bool revoke_privileges_by_bitset(const std::string& database, const std::string& table,
                                     const PrivilegeBitset& priv_bitset) {
        SCOPED_LOCK_BUTEX(_butex);
        return _priv_tree.revoke_privileges_by_bitset(database, table, priv_bitset);
    }

    bool grant_privilege(const std::string& database, const std::string& table,
                         const PrivilegeId priv_id) {
        SCOPED_LOCK_BUTEX(_butex);
        return _priv_tree.grant_privilege(database, table, priv_id);
    }

    bool revoke_privilege(const std::string& database, const std::string& table,
                          const PrivilegeId priv_id) {
        SCOPED_LOCK_BUTEX(_butex);
        return _priv_tree.revoke_privilege(database, table, priv_id);
    }

    bool grant_role(const RoleRef role) {
        SCOPED_LOCK_BUTEX(_butex);
        return _roles.insert(std::make_pair(role->name(), role)).second;
    }

    bool revoke_role(const std::string& role_name) {
        SCOPED_LOCK_BUTEX(_butex);
        auto iter = _roles.find(role_name);
        if (iter != _roles.end()) {
            iter->second.reset();
            _roles.erase(iter);
        }
        return true;
    }

    bool is_role_granted(const std::string& role_name) {
        SCOPED_LOCK_BUTEX(_butex);
        return _roles.count(role_name) > 0;
    }

    void list_role(std::vector<RoleRef>* roles) {
        SCOPED_LOCK_BUTEX(_butex);
        for (const auto& [_, role] : _roles) {
            roles->push_back(role);
        }
    }

    // only standalone privileges
    void show_privileges(std::map<RBACObject, PrivilegeBitset>* privileges) {
        SCOPED_LOCK_BUTEX(_butex);
        PrivilegeTreeNodeMap tree_nodes;
        _priv_tree.display_tree(&tree_nodes, false);
        for (const auto& [object, node] : tree_nodes) {
            privileges->insert(std::make_pair(object, node->get_privilege_bitset()));
        }
    }

    void select_role_by_privilege(const std::string& database, 
                                  const std::string& table,
                                  const PrivilegeId priv_id,
                                  std::vector<RoleRef>* roles) {
        SCOPED_LOCK_BUTEX(_butex);
        for (const auto& [_, role] : _roles) {
            if (role->authorize(database, table, priv_id)) {
                roles->push_back(role);
            }
        }
    }

    std::string get_parent() const {
        SCOPED_LOCK_BUTEX(_butex);
        return _parent;
    }

    void set_parent(const std::string& parent) {
        SCOPED_LOCK_BUTEX(_butex);
        _parent = parent;
    }

    void add_child_user(const std::string& username) {
        SCOPED_LOCK_BUTEX(_butex);
        _child_users.insert(username);
    }

    void remove_child_user(const std::string& username) {
        SCOPED_LOCK_BUTEX(_butex);
        _child_users.erase(username);
    }

    void add_child_role(const std::string& role_name) {
        SCOPED_LOCK_BUTEX(_butex);
        _child_roles.insert(role_name);
    }

    void remove_child_role(const std::string& role_name) {
        SCOPED_LOCK_BUTEX(_butex);
        _child_roles.erase(role_name);
    }

    std::set<std::string> list_child_user() {
        SCOPED_LOCK_BUTEX(_butex);
        return _child_users;
    }

    std::set<std::string> list_child_role() {
        SCOPED_LOCK_BUTEX(_butex);
        return _child_roles;
    }

    void list_authorized_database(std::unordered_set<std::string>& authoried_database_list, const PrivilegeId priv_id) {
        SCOPED_LOCK_BUTEX(_butex);
        // check role's privileges
        for (const auto& [_, role] : _roles) {
            std::map<common::RBACObject, PrivilegeBitset> privileges;
            role->show_privileges(&privileges);
            for (const auto& [object, priv_bitset] : privileges) {
                // privilege not match
                if (!priv_bitset.test(priv_id)) {
                    continue;
                }
                authoried_database_list.insert(object.database);
                if (object.is_root()) {
                    return;
                }
            }
        }
        // check user's privileges
        std::map<common::RBACObject, PrivilegeBitset> privileges;
        PrivilegeTreeNodeMap tree_nodes;
        _priv_tree.display_tree(&tree_nodes, false);
        for (const auto& [object, node] : tree_nodes) {
            privileges.insert(std::make_pair(object, node->get_privilege_bitset()));
        }
        for (const auto& [object, priv_bitset] : privileges) {
            // privilege not match
            if (!priv_bitset.test(priv_id)) {
                continue;
            }
            authoried_database_list.insert(object.database);
            if (object.is_root()) {
                return;
            }
        }
    }

    void list_authorized_table(std::unordered_set<std::string>& authoried_table_list, const std::string& database, const PrivilegeId priv_id) {
        SCOPED_LOCK_BUTEX(_butex);
        // check role's privileges
        for (const auto& [_, role] : _roles) {
            std::map<common::RBACObject, PrivilegeBitset> privileges;
            role->show_privileges(&privileges);
            for (const auto& [object, priv_bitset] : privileges) {
                // privilege not match
                if (!priv_bitset.test(priv_id)) {
                    continue;
                }
                // database not match
                if (!object.is_root() && object.database != database) {
                    continue;
                }
                authoried_table_list.insert(object.table);
                if (object.is_root() || object.is_database()) {
                    return;
                }
            }
        }
        // check user's privileges
        std::map<common::RBACObject, PrivilegeBitset> privileges;
        PrivilegeTreeNodeMap tree_nodes;
        _priv_tree.display_tree(&tree_nodes, false);
        for (const auto& [object, node] : tree_nodes) {
            privileges.insert(std::make_pair(object, node->get_privilege_bitset()));
        }
        for (const auto& [object, priv_bitset] : privileges) {
            // privilege not match
            if (!priv_bitset.test(priv_id)) {
                continue;
            }
            // database not match
            if (!object.is_root() && object.database != database) {
                continue;
            }
            authoried_table_list.insert(object.table);
            if (object.is_root() || object.is_database()) {
                return;
            }
        }
    }

    bool deserialize(const mochow::pb::PUser& user);
    void serialize(mochow::pb::PUser* user) const;

private:
    void clear() {
        _username.clear();
        _password.clear();
        _parent.clear();
        _child_users.clear();
        _child_roles.clear();
        _roles.clear();
        _priv_tree.clear();
    }

private:
    std::string _username;
    std::string _password;
    std::string _parent;
    std::set<std::string> _child_users;
    std::set<std::string> _child_roles;
    std::map<std::string, RoleRef> _roles;
    PrivilegeTree _priv_tree; // standalone privileges outside of roles
};

class RBACManager final : public ManagerBase {
public:
    UserRef create_user(const std::string& username, const std::string& password,
                  const std::string& parent) {
        SCOPED_LOCK_BUTEX(_butex);
        auto parent_user = get_user_without_lock(parent);
        if (parent_user == nullptr) { return nullptr; }
        auto user = std::make_shared<User>(username, password, parent);
        if (_users.insert(std::make_pair(username, user)).second) {
            parent_user->add_child_user(username);
            return user;
        }
        return nullptr;
    }

    bool add_user(const UserRef user) {
        SCOPED_LOCK_BUTEX(_butex);
        return add_user_without_lock(user);
    }

    UserRef get_user(const std::string& username) {
        SCOPED_LOCK_BUTEX(_butex);
        return get_user_without_lock(username);
    }

    bool authenticate(const std::string& username, const std::string& password) {
        auto user = get_user(username);
        return user != nullptr ? user->authenticate(username, password)
                               : false;
    }

    bool authorize_usage_privilege(const std::string& username) {
        auto user = get_user(username);
        return user != nullptr ? user->authorize("*", "*", Privilege::USAGE)
                               : false;
    }

    bool is_user_exist(const std::string& username) {
        return get_user(username) != nullptr;
    }

    void drop_user(const std::string& username) {
        SCOPED_LOCK_BUTEX(_butex);
        _users.erase(username);
    }

    void list_user(std::vector<UserRef>* users) {
        SCOPED_LOCK_BUTEX(_butex);
        for (const auto& [_, user] : _users) {
            users->push_back(user);
        }
    }

    RoleRef create_role(const std::string& role_name, const std::string& parent) {
        SCOPED_LOCK_BUTEX(_butex);
        auto parent_user = get_user_without_lock(parent);
        if (parent_user == nullptr) { return nullptr; }
        auto role = std::make_shared<Role>(role_name, parent);
        if (_roles.insert(std::make_pair(role_name, role)).second) {
            parent_user->add_child_role(role_name);
            return role;
        }
        return nullptr;
    }

    bool add_role(const RoleRef role) {
        SCOPED_LOCK_BUTEX(_butex);
        return add_role_without_lock(role);
    }

    RoleRef get_role(const std::string& role_name) {
        SCOPED_LOCK_BUTEX(_butex);
        return get_role_without_lock(role_name);
    }

    RoleRef get_builtin_admin_role() {
        return get_role(S_MOCHOW_BUILTIN_ADMIN_ROLE);
    }

    bool is_role_exist(const std::string& role_name) {
        SCOPED_LOCK_BUTEX(_butex);
        return get_role_without_lock(role_name) != nullptr;
    }

    bool drop_role(const std::string& role_name) {
        SCOPED_LOCK_BUTEX(_butex);
        // Check whether the target role is referenced by any user.
        for (const auto& [_, user] : _users) {
            if (user->is_role_granted(role_name)) {
                return false;
            }
        }
        // If not, remove the target role.
        auto iter = _roles.find(role_name);
        if (iter != _roles.end()) {
            iter->second.reset();
            _roles.erase(iter);
        }
        return true;
    }

    void list_role(std::vector<RoleRef>* roles) {
        SCOPED_LOCK_BUTEX(_butex);
        for (const auto& [_, role] : _roles) {
            roles->push_back(role);
        }
    }

    void select_user_by_role(const std::string& role,
                             std::vector<UserRef>* users) {
        SCOPED_LOCK_BUTEX(_butex);
        for (const auto& [_, user] : _users) {
            if (user->is_role_granted(role)) {
                users->push_back(user);
            }
        }
    }

    void select_user_by_privilege(const std::string& database, 
                                  const std::string& table,
                                  const PrivilegeId priv_id,
                                  std::vector<UserRef>* users) {
        SCOPED_LOCK_BUTEX(_butex);
        for (const auto& [_, user] : _users) {
            if (user->authorize(database, table, priv_id)) {
                users->push_back(user);
            }
        }
    }

    void select_user_by_privilege(const std::string& database, 
                                  const std::string& table,
                                  const PrivilegeBitset& priv_bitset,
                                  std::vector<UserRef>* users) {
        SCOPED_LOCK_BUTEX(_butex);
        for (const auto& [_, user] : _users) {
            if (user->authorize(database, table, priv_bitset)) {
                users->push_back(user);
            }
        }
    }

    void select_role_by_privilege(const std::string& database, 
                                  const std::string& table,
                                  const PrivilegeId priv_id,
                                  std::vector<RoleRef>* roles) {
        SCOPED_LOCK_BUTEX(_butex);
        for (const auto& [_, role] : _roles) {
            if (role->authorize(database, table, priv_id)) {
                roles->push_back(role);
            }
        }
    }

    void select_role_by_privilege(const std::string& database, 
                                  const std::string& table,
                                  const PrivilegeBitset& priv_bitset,
                                  std::vector<RoleRef>* roles) {
        SCOPED_LOCK_BUTEX(_butex);
        for (const auto& [_, role] : _roles) {
            if (role->authorize(database, table, priv_bitset)) {
                roles->push_back(role);
            }
        }
    }

    bool is_ancestor_to_user(const std::string& from_user, const std::string& to_user) {
        SCOPED_LOCK_BUTEX(_butex);
        auto user = get_user_without_lock(to_user);
        if (user == nullptr) { return false; }
        std::string parent = user->get_parent();
        while (!parent.empty()) {
            if (parent == from_user) { return true; }
            parent = get_user_without_lock(parent)->get_parent();
        }
        return false;
    }

    bool is_ancestor_to_role(const std::string& from_user, const std::string& to_role) {
        SCOPED_LOCK_BUTEX(_butex);
        auto role = get_role_without_lock(to_role);
        if (role == nullptr) { return false; }
        std::string parent = role->get_parent();
        while (!parent.empty()) {
            if (parent == from_user) { return true; }
            parent = get_user_without_lock(parent)->get_parent();
        }
        return false;
    }

    void clear() {
        _users.clear();
        _roles.clear();
    }

    void lock() { _butex.lock(); }
    void unlock() { _butex.unlock(); }

    bool deserialize(const mochow::pb::PRBACInfo& pinfo);
    void serialize(mochow::pb::PRBACInfo* pinfo) const;

private:
    UserRef get_user_without_lock(const std::string& username) const {
        auto iter = _users.find(username);
        return iter != _users.end() ? iter->second : nullptr;
    }

    RoleRef get_role_without_lock(const std::string& role_name) const {
        auto iter = _roles.find(role_name);
        return iter != _roles.end() ? iter->second : nullptr;
    }

    bool add_user_without_lock(const UserRef user) { 
        return _users.insert(std::make_pair(user->username(), user)).second;
    }

    bool add_role_without_lock(const RoleRef role) { 
        return _roles.insert(std::make_pair(role->name(), role)).second;
    }

private:
    std::map<std::string, UserRef> _users;
    std::map<std::string, RoleRef> _roles;
};

void initialize_rbac_manager(RBACManager* rbac_manager);

}
