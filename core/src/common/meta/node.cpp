/***************************************************************************
 *
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

#include "core/src/common/meta/node.h"
#include <cstdint>

#include "nlohmann/json.hpp"

#include "core/src/common/logging.h"
#include "core/src/common/meta/tablet.h"

namespace mochow::common {

bool ProxyManager::add_proxy(const ProxyRef& proxy) {
    common::ScopedButex lock(_butex);
    auto full_name = proxy->full_name();
    if (_proxy_map.count(full_name) > 0) {
        return false;
    }
    _add_op_counter++;
    _proxy_map[full_name] = proxy;
    return true;
}

bool ProxyManager::drop_proxy(const std::string& full_name) {
    common::ScopedButex lock(_butex);
    auto count = _proxy_map.erase(full_name);
    if (count == 0) {
        return false;
    }
    _drop_op_counter++;
    return true;
}

ProxyRef ProxyManager::get_proxy(const std::string& full_name) {
    common::ScopedButex lock(_butex);
    auto iter = _proxy_map.find(full_name);
    if (iter != _proxy_map.end()) {
        return iter->second;
    }
    return nullptr;
}

void ProxyManager::list_proxy(std::vector<ProxyRef>* proxy_list) {
    common::ScopedButex lock(_butex);
    for (const auto& [_, proxy] : _proxy_map) {
        proxy_list->push_back(proxy);
    }
}

std::string ProxyManager::get_metrics() {
    common::ScopedButex lock(_butex);
    return _metrics;
}

void ProxyManager::refresh_metrics() {
    nlohmann::json json;

    std::vector<std::string> metric_name = {"insert", "upsert", "update", "delete", "query", "search", "select",
                                            "batch_query", "batch_search", "multi_vector_search", "total"};
    std::vector<float> qps;
    std::vector<uint64_t> latency, http_4xx_fail_count, http_5xx_fail_count;
    http_4xx_fail_count.resize(metric_name.size(), 0);
    http_5xx_fail_count.resize(metric_name.size(), 0);
    qps.resize(metric_name.size(), 0);
    latency.resize(metric_name.size(), 0);

    std::vector<ProxyRef> proxy_list;
    list_proxy(&proxy_list);
    for (const auto& proxy : proxy_list) {
        http_4xx_fail_count[0] += proxy->operation_metrics().insert().http_4xx_fail_count();
        http_4xx_fail_count[1] += proxy->operation_metrics().upsert().http_4xx_fail_count();
        http_4xx_fail_count[2] += proxy->operation_metrics().update().http_4xx_fail_count();
        http_4xx_fail_count[3] += proxy->operation_metrics().delete_().http_4xx_fail_count();
        http_4xx_fail_count[4] += proxy->operation_metrics().query().http_4xx_fail_count();
        http_4xx_fail_count[5] += proxy->operation_metrics().search().http_4xx_fail_count();
        http_4xx_fail_count[6] += proxy->operation_metrics().select().http_4xx_fail_count();
        http_4xx_fail_count[7] += proxy->operation_metrics().batch_query().http_4xx_fail_count();
        http_4xx_fail_count[8] += proxy->operation_metrics().batch_search().http_4xx_fail_count();
        http_4xx_fail_count[9] += proxy->operation_metrics().multi_vector_search().http_4xx_fail_count();

        http_5xx_fail_count[0] += proxy->operation_metrics().insert().http_5xx_fail_count();
        http_5xx_fail_count[1] += proxy->operation_metrics().upsert().http_5xx_fail_count();
        http_5xx_fail_count[2] += proxy->operation_metrics().update().http_5xx_fail_count();
        http_5xx_fail_count[3] += proxy->operation_metrics().delete_().http_5xx_fail_count();
        http_5xx_fail_count[4] += proxy->operation_metrics().query().http_5xx_fail_count();
        http_5xx_fail_count[5] += proxy->operation_metrics().search().http_5xx_fail_count();
        http_5xx_fail_count[6] += proxy->operation_metrics().select().http_5xx_fail_count();
        http_5xx_fail_count[7] += proxy->operation_metrics().batch_query().http_5xx_fail_count();
        http_5xx_fail_count[8] += proxy->operation_metrics().batch_search().http_5xx_fail_count();
        http_5xx_fail_count[9] += proxy->operation_metrics().multi_vector_search().http_5xx_fail_count();

        qps[0] += proxy->operation_metrics().insert().float_qps();
        qps[1] += proxy->operation_metrics().upsert().float_qps();
        qps[2] += proxy->operation_metrics().update().float_qps();
        qps[3] += proxy->operation_metrics().delete_().float_qps();
        qps[4] += proxy->operation_metrics().query().float_qps();
        qps[5] += proxy->operation_metrics().search().float_qps();
        qps[6] += proxy->operation_metrics().select().float_qps();
        qps[7] += proxy->operation_metrics().batch_query().float_qps();
        qps[8] += proxy->operation_metrics().batch_search().float_qps();
        qps[9] += proxy->operation_metrics().multi_vector_search().float_qps();

        // avg_latency = sum(qps * latency) / sum(qps)
        latency[0] += proxy->operation_metrics().insert().float_qps() * proxy->operation_metrics().insert().latency_us();
        latency[1] += proxy->operation_metrics().upsert().float_qps() * proxy->operation_metrics().upsert().latency_us();
        latency[2] += proxy->operation_metrics().update().float_qps() * proxy->operation_metrics().update().latency_us();
        latency[3] += proxy->operation_metrics().delete_().float_qps() * proxy->operation_metrics().delete_().latency_us();
        latency[4] += proxy->operation_metrics().query().float_qps() * proxy->operation_metrics().query().latency_us();
        latency[5] += proxy->operation_metrics().search().float_qps() * proxy->operation_metrics().search().latency_us();
        latency[6] += proxy->operation_metrics().select().float_qps() * proxy->operation_metrics().select().latency_us();
        latency[7] += proxy->operation_metrics().batch_query().float_qps() * proxy->operation_metrics().batch_query().latency_us();
        latency[8] += proxy->operation_metrics().batch_search().float_qps() * proxy->operation_metrics().batch_search().latency_us();
        latency[9] += proxy->operation_metrics().multi_vector_search().float_qps() * proxy->operation_metrics().multi_vector_search().latency_us();
    }

    // total
    http_4xx_fail_count[10] = http_4xx_fail_count[0] + http_4xx_fail_count[1] + http_4xx_fail_count[2] + http_4xx_fail_count[3] + http_4xx_fail_count[4] + http_4xx_fail_count[5] + http_4xx_fail_count[6] + http_4xx_fail_count[7] + http_4xx_fail_count[8] + http_4xx_fail_count[9];
    http_5xx_fail_count[10] = http_5xx_fail_count[0] + http_5xx_fail_count[1] + http_5xx_fail_count[2] + http_5xx_fail_count[3] + http_5xx_fail_count[4] + http_5xx_fail_count[5] + http_5xx_fail_count[6] + http_5xx_fail_count[7] + http_5xx_fail_count[8] + http_5xx_fail_count[9];
    qps[10] = qps[0] + qps[1] + qps[2] + qps[3] + qps[4] + qps[5] + qps[6] + qps[7] + qps[8] + qps[9];
    latency[10] = latency[0] + latency[1] + latency[2] + latency[3] + latency[4] + latency[5] + latency[6] + latency[7] + latency[8] + latency[9];

    for (size_t i = 0; i < metric_name.size(); i++) {
        json[metric_name[i] + "_qps"] = std::isnan(qps[i]) ? 0 : qps[i];
        json[metric_name[i] + "_http_4xx_fail_count"] = http_4xx_fail_count[i];
        json[metric_name[i] + "_http_5xx_fail_count"] = http_5xx_fail_count[i];
        // here we want milli-second according to our design.
        auto latency_ms = (double)latency[i] / qps[i] / 1000;
        json[metric_name[i] + "_latency_avg"] = std::isnan(latency_ms) ? 0.0 : latency_ms;
    }

    common::ScopedButex lock(_butex);
    _metrics = json.dump(4);
}

bool DataNodeManager::add_node(const DataNodeRef& node) {
    common::ScopedButex lock(_butex);
    auto addr = node->addr();
    if (_node_map.count(addr) > 0) {
        return false;
    }
    _add_op_counter++;
    _node_map[addr] = node;
    return true;
}

bool DataNodeManager::drop_node(const base::EndPoint& addr) {
    common::ScopedButex lock(_butex);
    auto count = _node_map.erase(addr);
    if (count == 0) {
        return false;
    }
    _drop_op_counter++;
    return true;
}

DataNodeRef DataNodeManager::get_node(const base::EndPoint& addr) {
    common::ScopedButex lock(_butex);
    auto iter = _node_map.find(addr);
    if (iter != _node_map.end()) {
        return iter->second;
    }
    return nullptr;
}

void DataNodeManager::list_node(std::vector<DataNodeRef>* node_list) {
    common::ScopedButex lock(_butex);
    for (auto& [_, node] : _node_map) {
        node_list->push_back(node);
    }
}

void DataNodeManager::list_node_addr(std::vector<uint64_t>* node_addr_list) {
    common::ScopedButex lock(_butex);
    node_addr_list->clear();
    for (auto& [addr, _] : _node_map) {
        node_addr_list->push_back(common::endpoint2int(addr));
    }
}

void DataNodeManager::list_az(std::vector<std::string>* az_names) {
    std::set<std::string> az_set;

    {
        common::ScopedButex lock(_butex);
        for (auto& [_, node] : _node_map) {
            az_set.emplace(node->az_name());
        }
    }

    for (const auto& az: az_set) {
        az_names->emplace_back(az);
    }
}

bool DataNode::add_replica(const ReplicaRef& replica) {
    common::ScopedButex lock(_butex);
    auto long_id = replica->tablet_long_id();
    if (_replica_map.count(long_id) > 0) {
        return false;
    }
    _replica_map[long_id] = replica;
    return true;
}

bool DataNode::drop_replica(uint64_t id) {
    common::ScopedButex lock(_butex);
    auto count = _replica_map.erase(id);
    return count > 0;
}

ReplicaRef DataNode::get_replica(uint64_t id) {
    common::ScopedButex lock(_butex);
    auto iter = _replica_map.find(id);
    if (iter != _replica_map.end()) {
        return iter->second;
    }
    return nullptr;
}

void DataNode::list_replica(std::vector<ReplicaRef>* replica_list) const {
    common::ScopedButex lock(_butex);
    for (auto& [_, replica] : _replica_map) {
        replica_list->push_back(replica);
    }
}

void DataNode::list_tablet_id(std::vector<uint64_t>* id_list) const {
    common::ScopedButex lock(_butex);
    for (auto& [_, replica] : _replica_map) {
        id_list->push_back(replica->tablet_long_id());
    }
}

void DataNode::serialize(pb::PDataNode* node_info) const {
    common::ScopedButex lock(_butex);
    node_info->mutable_node_addr()->CopyFrom(common::endpoint2node(_addr));
    node_info->set_is_alive(_is_alive);
    node_info->set_az_name(_az_name);
    node_info->set_rack_name(_rack_name);
    node_info->set_state(_state);
    node_info->set_create_time(_create_time);
    node_info->set_online_time(_online_time);
    node_info->set_offline_time(_offline_time);
    node_info->set_version(_version);
    node_info->set_last_active_timestamp(_last_active_timestamp);
    if (_basic_metrics != nullptr) {
        node_info->mutable_basic_metrics()->CopyFrom(*_basic_metrics);
    }
}

bool DataNode::deserialize(const pb::PDataNode& info) {
    if (!info.has_node_addr()) {
        LOG(WARNING) << "Fail to deserialize datanode due to missing some fields,"
                   << " datanode_pb:" << common::pb2json(info);
        return false;
    }
    _addr = common::node2endpoint(info.node_addr());
    _version = info.version();
    _is_alive = info.is_alive();
    _az_name = info.az_name();
    _rack_name = info.rack_name();
    _state = info.state();
    _create_time = info.create_time();
    _online_time = info.online_time();
    _offline_time = info.offline_time();

    if (info.has_last_active_timestamp()) {
        _last_active_timestamp = info.last_active_timestamp();
    }
    if (info.has_basic_metrics()) {
        _basic_metrics->CopyFrom(info.basic_metrics());
    }

    return true;
}

void DataNode::clear_replica_without_lock() {
    for (auto& [_, replica] : _replica_map) {
        replica->clear();
    }
    _replica_map.clear();
}

}
