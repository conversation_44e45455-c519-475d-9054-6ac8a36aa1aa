/***************************************************************************
 *
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

#pragma once

#include "core/src/common/meta/meta_base.h"

namespace mochow::master {
base::Status relocate_datanode_addr(const base::EndPoint&, const base::EndPoint&);
}

namespace mochow::common {

struct IndexContext {
    IDXVERSION index_version;
    std::string index_state;
};

class Replica final : public MetaBase {
public:
    Replica(const TabletRef tablet, const DataNodeRef node);

    TabletRef tablet() const {
        return _tablet;
    }
    DataNodeRef node() const {
        return _datanode;
    }
    base::EndPoint node_addr() const {
        return _node_addr;
    }
    TabletID tablet_long_id() const {
        return _tablet_long_id;
    }
    int32_t replica_index() const {
        common::ScopedButex lock(_butex);
        return _replica_index;
    }
    void set_replica_index(int32_t replica_index) {
        common::ScopedButex lock(_butex);
        _replica_index = replica_index;
    }
    std::string state() const {
        common::ScopedButex lock(_butex);
        return _state;
    }
    void set_state(const std::string& state) {
        common::ScopedButex lock(_butex);
        _state = state;
    }
    uint64_t create_time() const {
        common::ScopedButex lock(_butex);
        return _create_time;
    }
    void set_create_time(uint64_t create_time) {
        common::ScopedButex lock(_butex);
        _create_time = create_time;
    }
    mochow::pb::ReplicaMetrics metrics() const {
        common::ScopedButex lock(_butex);
        return _metrics;
    }
    void set_metrics(const mochow::pb::ReplicaMetrics& metrics) {
        common::ScopedButex lock(_butex);
        _metrics.CopyFrom(metrics);
    }

    void serialize(mochow::pb::PReplica* info) const;
    NODISCARD bool deserialize(const mochow::pb::PReplica& info);

    void update_index_ctx(
            const IDXID index_id,
            const IDXVERSION index_version,
            const std::string& index_state) {
        common::ScopedButex lock(_butex);
        _index_ctxs[index_id].index_version = index_version;
        _index_ctxs[index_id].index_state = index_state;
    }

    // used for datanode heartbeat to update index version
    void try_update_index_ctx( 
            const IDXID index_id,
            const IDXVERSION index_version,
            const std::string& index_state) {
        common::ScopedButex lock(_butex);
        if (_index_ctxs.find(index_id) == _index_ctxs.end() ||
                index_version > _index_ctxs[index_id].index_version) {
            _index_ctxs[index_id].index_version = index_version;

            LOG_AND_ASSERT(common::IndexState::is_valid_index_state(index_state));
            _index_ctxs[index_id].index_state = index_state;
        }
    }

    IndexContext get_index_context(const IDXID index_id) {
        common::ScopedButex lock(_butex);
        return _index_ctxs[index_id];
    }

    std::map<IDXID, IndexContext> get_index_contexts() {
        common::ScopedButex lock(_butex);
        return _index_ctxs;
    }

    void remove_index_context(const IDXID index_id) {
        common::ScopedButex lock(_butex);
        _index_ctxs.erase(index_id);
    }

    bool is_index_ready(const IDXID index_id, const IDXVERSION index_version) {
        common::ScopedButex lock(_butex);
        if (_index_ctxs.find(index_id) == _index_ctxs.end()) {
            return false;
        }
        IDXMAJOR index_major_version = major_index_version(_index_ctxs[index_id].index_version);
        IDXMAJOR check_major_version = major_index_version(index_version);
        return index_major_version >= check_major_version && _index_ctxs[index_id].index_state == common::IndexState::NORMAL;
    }

    IndexContext get_scalar_index_context(const IDXID index_id) {
        common::ScopedButex lock(_butex);
        return _scalar_index_ctxs[index_id];
    }

    std::map<IDXID, IndexContext> get_scalar_index_contexts() {
        common::ScopedButex lock(_butex);
        return _scalar_index_ctxs;
    }

    void update_scalar_index_state(const IDXID index_id,
                                   const std::string& index_state) {
        common::ScopedButex lock(_butex);
        _scalar_index_ctxs[index_id].index_state = index_state;
    }

    bool is_scalar_index_ready(const IDXID index_id) {
        common::ScopedButex lock(_butex);
        if (_scalar_index_ctxs.find(index_id) == _scalar_index_ctxs.end()) {
            return false;
        }
        return _scalar_index_ctxs[index_id].index_state == common::IndexState::NORMAL;
    }

    void clear() {
        _tablet.reset();
        _datanode.reset();
    }

private:
    TabletRef _tablet;
    DataNodeRef _datanode;
    const base::EndPoint _node_addr;
    const TabletID _tablet_long_id;
    int32_t _replica_index = 0;
    std::string _state;
    uint64_t _create_time = 0;
    mochow::pb::ReplicaMetrics _metrics;
    std::map<IDXID, IndexContext> _index_ctxs;
    std::map<IDXID, IndexContext> _scalar_index_ctxs;

    friend base::Status mochow::master::relocate_datanode_addr(const base::EndPoint&, const base::EndPoint&);
};

class Tablet final : public MetaBase {
public:
    Tablet(const TableRef table, const TPID tp_id);

    TableRef table() const {
        return _table;
    }
    TPID id() const {
        return _id;
    }
    TabletID long_id() const {
        return _long_id;
    }
    std::string state() const {
        common::ScopedButex lock(_butex);
        return _state;
    }
    void set_state(const std::string& state) {
        common::ScopedButex lock(_butex);
        _state = state;
    }
    void update_index_ctx(
            const base::EndPoint& replica_addr,
            const IDXID index_id,
            const IDXVERSION index_version,
            const std::string& index_state) {
        common::ScopedButex lock(_butex);
        if (_replica_map.find(replica_addr) == _replica_map.end()){
            return;
        }
        _replica_map[replica_addr]->update_index_ctx(index_id, index_version, index_state);
    }
    void remove_index_ctx(const IDXID index_id) {
        common::ScopedButex lock(_butex);
        for (auto [_, replica] : _replica_map) {
            replica->remove_index_context(index_id);
        }
    }
    bool is_index_ready(const IDXID index_id, const IDXVERSION index_version);
    void update_scalar_index_state(
            const base::EndPoint& replica_addr,
            const IDXID index_id,
            const std::string& index_state) {
        common::ScopedButex lock(_butex);
        if (_replica_map.find(replica_addr) == _replica_map.end()) {
            return;
        }
        _replica_map[replica_addr]->update_scalar_index_state(index_id, index_state);
    }
    bool is_scalar_index_ready(const IDXID index_id) {
        for (auto [_, replica] : _replica_map) {
            if (!replica->is_scalar_index_ready(index_id)) {
                return false;
            }
        }
        return true;
    }
    int32_t hash_index() const {
        common::ScopedButex lock(_butex);
        return _hash_index;
    }
    void set_hash_index(int32_t hash_index) {
        common::ScopedButex lock(_butex);
        _hash_index = hash_index;
    }
    int64_t term() const {
        common::ScopedButex lock(_butex);
        return _term;
    }
    base::EndPoint leader_addr() const {
        common::ScopedButex lock(_butex);
        return _leader_addr;
    }

    void switch_leader_force(const base::EndPoint& node_addr) {
        common::ScopedButex lock(_butex);
        _leader_addr = node_addr;
    }

    void switch_leader(const int64_t term, const base::EndPoint& node_addr, const bool is_leader) {
        common::ScopedButex lock(_butex);
        if (term > _term) {
            _term = term;
            _leader_addr = is_leader ? node_addr : base::EndPoint();
        } else if (term == _term) {
            if (_leader_addr == base::EndPoint() && is_leader) {
                _leader_addr = node_addr;
            } else if (_leader_addr == node_addr && !is_leader) {
                _leader_addr = base::EndPoint();
            }
        }
    }
    int32_t tablet_version() const {
        common::ScopedButex lock(_butex);
        return _tablet_version;
    }
    void increase_tablet_version() {
        common::ScopedButex lock(_butex);
        ++_tablet_version;
    }

    size_t replica_num() const {
        common::ScopedButex lock(_butex);
        return _replica_map.size();
    }

    size_t normal_replica_num() const;
    size_t expect_replica_num() const;
    size_t majority_replica_num() const;
    bool has_joining_replica() const;
    size_t remain_normal_replica_num_when_remove(const base::EndPoint addr);

    ReplicaRef get_replica(base::EndPoint addr);
    bool add_replica(const ReplicaRef& replica);
    bool drop_replica(base::EndPoint addr);
    void list_replica(std::vector<ReplicaRef>* replica_list) const;
    void list_replica_addr(std::vector<base::EndPoint>* addr_list) const;

    void serialize(mochow::pb::PTablet* info) const;
    NODISCARD bool deserialize(const mochow::pb::PTablet& info);

    void clear_replica() {
        common::ScopedButex lock(_butex);
        for (auto [_, replica] : _replica_map) {
            replica->clear();
        }
        _replica_map.clear();
    }

    void clear() {
        for (auto [_, replica] : _replica_map) {
            replica->clear();
        }
        _replica_map.clear();
        _table.reset();
    }

private:
    TableRef _table;
    std::map<base::EndPoint, ReplicaRef> _replica_map;
    const TPID _id = static_cast<TPID>(-1);
    // table_id (5-bytes) + tp_id (3-bytes)
    const TabletID _long_id;
    std::string _state;
    // only for hash partition
    int32_t _hash_index;
    int64_t _term = 0;
    base::EndPoint _leader_addr;
    int32_t _tablet_version = 0;
};

}  // namespace mochow::master
