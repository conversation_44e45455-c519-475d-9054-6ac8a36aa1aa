/***************************************************************************
 *
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

#include "core/src/common/meta/node.h"
#include "core/src/common/meta/tablet.h"
#include "core/src/common/meta/database.h"

namespace mochow::common {

Replica::Replica(const TabletRef tablet, const DataNodeRef datanode) :
        _tablet(tablet), _datanode(datanode), _node_addr(datanode->addr()),
        _tablet_long_id(tablet->long_id()) {
}

void Replica::serialize(mochow::pb::PReplica* info) const {
    common::ScopedButex lock(_butex);
    info->mutable_tablet_id()->set_db_id(tablet()->table()->db()->id());
    info->mutable_tablet_id()->set_table_id(tblid(_tablet_long_id));
    info->mutable_tablet_id()->set_tp_id(tpid(_tablet_long_id));
    info->mutable_node_addr()->CopyFrom(common::endpoint2node(_node_addr));
    info->set_state(_state);
    info->set_create_time(_create_time);
    info->set_replica_index(_replica_index);
    for (const auto& [index_id, index_ctx] : _index_ctxs) {
        const auto& index_status = info->add_index_status();
        index_status->set_index_id(index_id);
        index_status->set_index_version(index_ctx.index_version);
        index_status->set_index_state(index_ctx.index_state);
    }
    for (const auto& [index_id, index_ctx] : _scalar_index_ctxs) {
        const auto& index_status = info->add_scalar_index_status();
        index_status->set_index_id(index_id);
        index_status->set_index_state(index_ctx.index_state);
    }
}

bool Replica::deserialize(const mochow::pb::PReplica& info) {
    if (info.tablet_id().db_id() != tablet()->table()->db()->id() ||
            info.tablet_id().table_id() != tablet()->table()->id() ||
            info.tablet_id().tp_id() != tablet()->id() ||
            common::node2endpoint(info.node_addr()) != _node_addr) {
        LOG(ERROR) << "Fail to deserialize replica from pb due to db/table/tablet id mismatch"
                   << " or node_addr mismatch,"
                   << " expected_db_id:" << tablet()->table()->db()->id()
                   << " db_id_from_pb:" << info.tablet_id().db_id()
                   << " expected_table_id:" << tablet()->table()->id()
                   << " table_id_from_pb:" << info.tablet_id().table_id()
                   << " expected_tablet_id:" << tablet()->id()
                   << " tablet_id_from_pb:" << info.tablet_id().tp_id()
                   << " expected_node_addr:" << common::endpoint2str(_node_addr)
                   << " node_addr_from_pb:" << common::node2str(info.node_addr());
        return false;
    }

    _state = info.state();
    _create_time = info.create_time();
    _replica_index = info.replica_index();
    for (int i=0; i< info.index_status_size(); ++i) {
        const auto& index_status = info.index_status(i);
        IndexContext ctx;
        ctx.index_state = index_status.index_state();
        ctx.index_version = index_status.index_version();
        _index_ctxs[index_status.index_id()] = ctx;
    }
    for (int i=0; i< info.scalar_index_status_size(); ++i) {
        const auto& index_status = info.scalar_index_status(i);
        IndexContext ctx;
        ctx.index_state = index_status.index_state();
        _scalar_index_ctxs[index_status.index_id()] = ctx;
    }

    return true;
}

Tablet::Tablet(const TableRef table, const TPID tp_id) :
        _table(table), _id(tp_id),
        _long_id(make_tablet_id(table->id(), tp_id)) {
}

size_t Tablet::normal_replica_num() const {
    common::ScopedButex lock(_butex);
    size_t count = 0;
    for (const auto& [_, r] : _replica_map) {
        if (r->state() == common::ReplicaState::NORMAL) {
            count++;
        }
    }
    return count;
}

size_t Tablet::expect_replica_num() const {
    // Avoid calling this code within the ‘_butex’ lock scope to prevent deadlock, as
    // Table::replication() also acquires a lock.
    size_t replication = table()->replication();

    common::ScopedButex lock(_butex);
    if (_state == common::TabletState::BALANCING) {
        return replication + 1;
    }
    return replication;
}

size_t Tablet::majority_replica_num() const {
    return table()->replication() / 2 + 1;
}

bool Tablet::has_joining_replica() const {
    common::ScopedButex lock(_butex);
    size_t ret = 0;
    for (const auto& [_, r] : _replica_map) {
        if (r->state() == common::ReplicaState::JOINING) {
            return true;
        }
    }
    return false;
}

size_t Tablet::remain_normal_replica_num_when_remove(const base::EndPoint addr) {
    size_t count = 0;  
    common::ScopedButex lock(_butex);
    for (const auto& [node_addr, r] : _replica_map) {
        if ((node_addr != addr) && (r->state() == common::ReplicaState::NORMAL)) {
            count++;
        }
    }
    return count;
}


bool Tablet::add_replica(const ReplicaRef& replica) {
    common::ScopedButex lock(_butex);
    auto addr = replica->node_addr();
    if (_replica_map.count(addr) > 0) {
        return false;
    }
    _replica_map[addr] = replica;
    return true;
}

bool Tablet::drop_replica(base::EndPoint addr) {
    common::ScopedButex lock(_butex);
    auto count = _replica_map.erase(addr);
    return count > 0;
}

ReplicaRef Tablet::get_replica(base::EndPoint addr) {
    common::ScopedButex lock(_butex);
    auto iter = _replica_map.find(addr);
    if (iter != _replica_map.end()) {
        return iter->second;
    }
    return nullptr;
}

void Tablet::list_replica(std::vector<ReplicaRef>* replica_list) const {
    common::ScopedButex lock(_butex);
    for (auto& [_, replica] : _replica_map) {
        replica_list->push_back(replica);
    }
}

void Tablet::list_replica_addr(std::vector<base::EndPoint>* addr_list) const {
    common::ScopedButex lock(_butex);
    for (const auto& [addr, _] : _replica_map) {
        addr_list->push_back(addr);
    }
}

void Tablet::serialize(mochow::pb::PTablet* info) const {
    common::ScopedButex lock(_butex);
    info->mutable_id()->set_db_id(table()->db()->id());
    info->mutable_id()->set_table_id(tblid(_long_id));
    info->mutable_id()->set_tp_id(_id);
    info->set_state(_state);
    info->set_hash_index(_hash_index);
    info->set_tablet_version(_tablet_version);
    for (const auto& [_, replica] : _replica_map) {
        replica->serialize(info->add_replicas());
    }
    info->set_term(_term);
    info->mutable_leader_addr()->CopyFrom(common::endpoint2node(_leader_addr));
}

bool Tablet::deserialize(const mochow::pb::PTablet& info) {
    if (info.id().db_id() != table()->db()->id() || 
            info.id().table_id() != table()->id() || info.id().tp_id() != _id) {
        LOG(ERROR) << "Fail to deserialize tablet from pb due to db/table/tablet id mismatch,"
                   << " expected_db_id:" << table()->db()->id()
                   << " db_id_from_pb:" << info.id().db_id()
                   << " expected_table_id:" << table()->id()
                   << " table_id_from_pb:" << info.id().table_id()
                   << " expected_tablet_id:" << _id
                   << " tablet_id_from_pb:" << info.id().tp_id();
        return false;
    }

    _state = info.state();
    _hash_index = info.hash_index();
    _tablet_version = info.tablet_version();
    if (info.has_term()) {
        _term = info.term();
    }
    if (info.has_leader_addr()) {
        _leader_addr = common::node2endpoint(info.leader_addr());
    }
    return true;
}

bool Tablet::is_index_ready(const IDXID index_id, const IDXVERSION index_version) {
    for (auto [_, replica] : _replica_map) {
        if (!replica->is_index_ready(index_id, index_version)) {
            auto index_context = replica->get_index_context(index_id);
            LOG(NOTICE) << "index not ready."
                << " table_id:" << table()->id()
                << " tablet_id:" << id()
                << " replica_index:" << replica->replica_index()
                << " index_id:" << index_id
                << " expect_index_version:" << index_version
                << " current_index_version:" << index_context.index_version;
            return false;
        }
    }
    return true;
}
}
