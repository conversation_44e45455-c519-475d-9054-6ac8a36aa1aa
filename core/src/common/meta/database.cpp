/***************************************************************************
 *
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

#include "core/src/common/meta/tablet.h"
#include "core/src/common/meta/database.h"

namespace mochow::common {

void ScalarIndex::serialize_only_schema(mochow::pb::PScalarIndexSchema* pschema) const {
    common::ScopedButex lock(_butex);
    _schema.serialize_to_pb(pschema);
}

void ScalarIndex::serialize(mochow::pb::PScalarIndex* psindex) const {
    common::ScopedButex lock(_butex);
    _schema.serialize_to_pb(psindex->mutable_schema());
    psindex->set_state(_state);
}

bool ScalarIndex::deserialize(const mochow::pb::PScalarIndex& psindex) {
    if (!_schema.deserialize_from_pb(psindex.schema())) {
        LOG(ERROR) << "Fail to deserialize scalar index from pb"
                   << " due to deserialize index schema failed,"
                   << " index_pb:" << common::pb2json(psindex.schema());
        return false;
    }

    _id = _schema.get_index_id();
    _name = _schema.get_index_name();

    if (psindex.has_state()) {
        _state = psindex.state();
    }

    return true;
}

void VectorIndex::serialize_only_schema(mochow::pb::PVectorIndexSchema* pschema) const {
    common::ScopedButex lock(_butex);
    _schema.serialize_to_pb(pschema);
}

void VectorIndex::serialize(mochow::pb::PVectorIndex* pvindex) const {
    common::ScopedButex lock(_butex);
    _schema.serialize_to_pb(pvindex->mutable_schema());
    pvindex->set_is_auto_build(_is_auto_build);
    pvindex->set_index_version(_index_version);
    pvindex->set_expected_index_version(_expected_index_version);
    pvindex->set_state(_state);
    pvindex->mutable_auto_build_index_param()->CopyFrom(_auto_build_index_param);
}

bool VectorIndex::deserialize(const mochow::pb::PVectorIndex& pvindex) {
    if (!_schema.deserialize_from_pb(pvindex.schema())) {
        LOG(ERROR) << "Fail to deserialize vector index from pb"
                   << " due to deserialize index schema failed,"
                   << " index_pb:" << common::pb2json(pvindex.schema());
        return false;
    }

    _id = _schema.get_index_id();
    _name = _schema.get_index_name();

    if (pvindex.has_is_auto_build()) {
        _is_auto_build = pvindex.is_auto_build();
    } else {
        // Assume auto build is disabled
        _is_auto_build = false;
    }

    if (pvindex.has_index_version()) {
        _index_version = pvindex.index_version();
    }

    if (pvindex.has_expected_index_version()) {
        _expected_index_version = pvindex.expected_index_version();
    }

    if (_expected_index_version < _index_version) {
        _expected_index_version = _index_version;
    }

    if (pvindex.has_state()) {
        _state = pvindex.state();
    }
    if (pvindex.has_auto_build_index_param()) {
        _auto_build_index_param = pvindex.auto_build_index_param();
    }

    return true;
}

void InvertedIndex::serialize_only_schema(mochow::pb::PInvertedIndexSchema* pindex) const {
    common::ScopedButex lock(_butex);
    _schema.serialize_to_pb(pindex);
}

void InvertedIndex::serialize(mochow::pb::PInvertedIndex* pindex) const {
    common::ScopedButex lock(_butex);
    _schema.serialize_to_pb(pindex->mutable_schema());
    pindex->set_state(_state);
}

bool InvertedIndex::deserialize(const mochow::pb::PInvertedIndex& pindex) {
    if (!_schema.deserialize_from_pb(pindex.schema())) {
        LOG(ERROR) << "Fail to deserialize inverted index from pb"
                   << " due to deserialize index schema failed,"
                   << " index_pb:" << common::pb2json(pindex.schema());
        return false;
    }

    _id = _schema.get_index_id();
    _name = _schema.get_index_name();

    if (pindex.has_state()) {
        _state = pindex.state();
    }

    return true;    
}

bool Table::add_tablet(const TabletRef& tablet) {
    common::ScopedButex lock(_butex);
    auto id = tablet->id();
    if (_tablet_map.count(id) != 0) {
        return false;
    }
    _tablet_map[id] = tablet;
    return true;
}

bool Table::drop_tablet(TPID id) {
    common::ScopedButex lock(_butex);
    auto count = _tablet_map.erase(id);
    return count > 0;
}

TabletRef Table::get_tablet(TPID id) {
    common::ScopedButex lock(_butex);
    auto iter = _tablet_map.find(id);
    if (iter != _tablet_map.end()) {
        return iter->second;
    }
    return nullptr;
}

void Table::list_tablet(std::vector<TabletRef>* tablet_list) {
    common::ScopedButex lock(_butex);
    for (auto& [_, tablet] : _tablet_map) {
        tablet_list->push_back(tablet);
    }
}

void Table::list_scalar_index(std::vector<ScalarIndexRef>* indexes) const {
    common::ScopedButex lock(_butex);
    for (const auto& [_, sindex] : _scalar_indexes) {
        indexes->push_back(sindex);
    }
}

void Table::list_vector_index(std::vector<VectorIndexRef>* indexes) const {
    common::ScopedButex lock(_butex);
    for (const auto& [_, sindex] : _vector_indexes) {
        indexes->push_back(sindex);
    }
}

void Table::list_inverted_index(std::vector<InvertedIndexRef>* indexes) const {
    common::ScopedButex lock(_butex);
    indexes->reserve(_inverted_indexes.size());
    for (const auto& [_, index] : _inverted_indexes) {
        indexes->push_back(index);
    }
}

IndexBaseRef Table::show_index(const std::string& index_name) const {
    common::ScopedButex lock(_butex);
    std::map<std::string, VectorIndexRef>::const_iterator
                                vindex_iter = _vector_indexes.find(index_name);
    if (vindex_iter != _vector_indexes.cend()) {
        const auto& vindex = vindex_iter->second;
        LOG_AND_ASSERT(vindex->index_species() == vindex->get_schema().get_index_species());
        return vindex;
    }
    std::map<std::string, ScalarIndexRef>::const_iterator
                                sindex_iter= _scalar_indexes.find(index_name);
    if (sindex_iter != _scalar_indexes.cend()) {
        const auto& sindex = sindex_iter->second;
        LOG_AND_ASSERT(schema::IndexSpecies::is_compatible_scalar_index_species(sindex->get_schema().get_index_species()));
        return sindex;
    }

    auto iter = _inverted_indexes.find(index_name);
    if (iter != _inverted_indexes.end()) {
        const auto&  inverted_index = iter->second;
        LOG_AND_ASSERT(inverted_index->index_species() == inverted_index->get_schema().get_index_species());
        return inverted_index;
    }
    return nullptr;
}

void Table::list_building_index(std::vector<std::pair<IDXID, IDXVERSION>>* index_list) const {
    common::ScopedButex lock(_butex);
    if (_state != common::TableState::NORMAL
            && _state != common::TableState::CREATING) {
        return;
    }
    for (const auto& [_, vindex] : _vector_indexes) {
        if (vindex->state() == common::IndexState::BUILDING) {
            index_list->emplace_back(vindex->id(), vindex->index_version());
        }
    }
}

bool Table::is_all_tablet_normal() {
    common::ScopedButex lock(_butex);
    for (auto& [_, tablet] : _tablet_map) {
        if (tablet->state() != common::TabletState::NORMAL) {
            return false;
        }
    }
    return true;
}

void Table::serialize_only_schema(mochow::pb::PSchema* pschema) const{
    common::ScopedButex lock(_butex);
    pschema->set_schema_version(_schema_version);
    pschema->set_schema_layer(static_cast<int32_t>(schema::SchemaLayer::INTERNAL));
    _schema.serialize_to_pb(pschema->mutable_table());
    for (const auto& [_, index] : _scalar_indexes) {
        auto pindex = pschema->add_scalar_indexes();
        index->serialize_only_schema(pindex);
    }
    for (const auto& [_, index] : _vector_indexes) {
        auto pindex = pschema->add_vector_indexes();
        index->serialize_only_schema(pindex);
    }

    for (const auto& [_, index] : _inverted_indexes) {
        auto pindex = pschema->add_inverted_indexes();
        index->serialize_only_schema(pindex);
    }
}

void Table::serialize(mochow::pb::PTable* info) const {
    common::ScopedButex lock(_butex);
    info->set_replication(_replication);
    info->set_create_time(_create_time);
    info->set_description(_description);
    info->set_max_tablet_id(_max_tp_id);
    info->set_max_column_id(_column_id_allocator->get_id());
    info->set_max_index_id(_index_id_allocator->get_id());
    info->mutable_partition()->CopyFrom(_partition);
    info->set_schema_version(_schema_version);
    info->set_schema_layer(static_cast<int32_t>(schema::SchemaLayer::INTERNAL));
    info->set_state(_state);
    info->set_enable_dynamic_field(_enable_dynamic_field);
    info->set_ttl(_ttl);
    info->set_datanode_memory_reserved_in_gb(_datanode_memory_reserved_in_gb);
    _schema.serialize_to_pb(info->mutable_schema());
    for (const auto& [_, index] : _scalar_indexes) {
        auto pindex = info->add_scalar_indexes();
        index->serialize(pindex);
    }
    for (const auto& [_, index] : _vector_indexes) {
        auto pindex = info->add_vector_indexes();
        index->serialize(pindex);
    }
    
    for (const auto& [_, index] : _inverted_indexes) {
        auto pindex = info->add_inverted_indexes();
        index->serialize(pindex);
    }

    for (const auto& alias : _aliases) {
        info->add_aliases(alias);
    }
}

bool Table::deserialize(const mochow::pb::PTable& info) {
    if (!_schema.deserialize_from_pb(info.schema())) {
        LOG(ERROR) << "Fail to deserialize table from pb"
                   << " due to deserialize table schema failed,"
                   << " schema:" << common::pb2json(info.schema());
        return false;
    }
    _id = _schema.get_table_id();
    _name = _schema.get_table_name();

    if (_schema.get_db_name() != db()->name()
            || _schema.get_db_id() != db()->id()) {
        LOG(ERROR) << "Fail to deserialize table from pb"
                   << " due to db name or id not match,"
                   << " input_db_name:" << _schema.get_db_name()
                   << " input_db_id:" << _schema.get_db_id()
                   << " expect_db_name:" << db()->name()
                   << " expect_db_id:" << db()->id();
        return false;
    }

    if (!info.has_replication()
            || !info.has_partition()
            || !info.has_create_time()
            || !info.has_enable_dynamic_field()
            || !info.has_max_column_id()
            || !info.has_max_index_id()
            || !info.has_schema_version()
            || !info.has_state()) {
        LOG(ERROR) << "Fail to deserialize table from pb due to missing some fields,"
                   << " has_replication:" << info.has_replication()
                   << " has_partition:" << info.has_partition()
                   << " has_create_time:" << info.has_create_time()
                   << " has_enable_dynamic_field:" << info.has_enable_dynamic_field()
                   << " has_max_column_id:" << info.has_max_column_id()
                   << " has_max_index_id:" << info.has_max_index_id()
                   << " has_schema_version:" << info.has_schema_version()
                   << " has_state:" << info.has_state();
        return false;
    }

    _replication = info.replication();
    _partition.CopyFrom(info.partition());
    _create_time = info.create_time();
    _enable_dynamic_field = info.enable_dynamic_field();
    _column_id_allocator->reset(info.max_column_id());
    _index_id_allocator->reset(info.max_index_id());
    _schema_version = info.schema_version();
    _description = info.description();
    _max_tp_id = info.max_tablet_id();
    _state = info.state();

    for (int i = 0; i < info.aliases_size(); ++i) {
        _aliases.insert(info.aliases(i));
    }

    if (info.has_ttl()) {
        _ttl = info.ttl();
    }

    if (info.datanode_memory_reserved_in_gb()) {
        _datanode_memory_reserved_in_gb = info.datanode_memory_reserved_in_gb();
    }

    for (int i = 0; i < info.scalar_indexes_size(); ++i) {
        const auto& pindex = info.scalar_indexes(i);
        ScalarIndexRef scalar_index = std::make_shared<ScalarIndex>();
        if (!scalar_index->deserialize(pindex)) {
            LOG(ERROR) << "Fail to deserialize table from pb"
                       << " due to deserialize scalar index failed,"
                       << " index_pb:" << common::pb2json(pindex);
            return false;
        }
        _scalar_indexes[scalar_index->name()] = scalar_index;
    }

    for (int i = 0; i < info.vector_indexes_size(); ++i) {
        const auto& pindex = info.vector_indexes(i);
        VectorIndexRef vector_index = std::make_shared<VectorIndex>();
        if (!vector_index->deserialize(pindex)) {
            LOG(ERROR) << "Fail to deserialize table from pb"
                       << " due to deserialize vector index failed,"
                       << " index_pb:" << common::pb2json(pindex);
            return false;
        }
        _vector_indexes[vector_index->name()] = vector_index;
    }

    for (uint32_t i = 0; i < info.inverted_indexes_size(); ++i) {
        const auto& pindex = info.inverted_indexes(i);
        auto inverted_index = std::make_shared<InvertedIndex>();
        const bool rc = inverted_index->deserialize(pindex);

        if (!rc) {
            LOG(ERROR) << "Fail to deserialize table from pb"
                       << " due to deserialize inverted index failed,"
                       << " index_pb:" << common::pb2json(pindex);
            return false;
        }

        _inverted_indexes[inverted_index->name()] = inverted_index;
    }

    return true;
}

bool Database::add_table(const TableRef& table) {
    common::ScopedButex lock(_butex);
    auto name = table->name();
    if (_table_map.count(name) > 0) {
        return false;
    }
    _table_map[name] = table;
    auto aliases = table->list_alias();
    for (const auto& alias : aliases) {
        bool ok = _table_aliases.insert(std::make_pair(alias, name)).second;
        LOG_AND_ASSERT(ok);
    }
    return true;
}

bool Database::drop_table(const std::string& name) {
    common::ScopedButex lock(_butex);
    std::string source_name = name;
    // Maybe name is a alias, need find the source table name
    auto iter = _table_aliases.find(name);
    if (iter != _table_aliases.end()) {
        source_name = iter->second;
    }
    // Find and erase all the alias
    iter = _table_aliases.begin();
    while(iter != _table_aliases.end()) {
        if (iter->second == source_name) {
            iter = _table_aliases.erase(iter);
        } else {
            ++iter;
        }
    }
    // Drop the source table
    auto count = _table_map.erase(source_name);
    return count > 0;
}

TableRef Database::get_table(const std::string& name) {
    common::ScopedButex lock(_butex);
    std::string source_name = name;
    // Maybe name is a alias, need find the source table name
    auto iter1 = _table_aliases.find(name);
    if (iter1 != _table_aliases.end()) {
        source_name = iter1->second;
    }
    // return source table
    auto iter2 = _table_map.find(source_name);
    if (iter2 != _table_map.end()) {
        return iter2->second;
    }
    return nullptr;
}

void Database::list_table(std::vector<TableRef>* table_list) {
    common::ScopedButex lock(_butex);
    for (auto& [_, table] : _table_map) {
        table_list->push_back(table);
    }
}

bool Database::alias_table(const std::string& table_name, const std::string& alias) {
    common::ScopedButex lock(_butex);
    std::string source_name = table_name;
    // Maybe table name is a alias, need find the source table name
    auto iter = _table_aliases.find(table_name);
    if (iter != _table_aliases.end()) {
        source_name = iter->second;
    }
    // Check source table exists
    if (_table_map.count(source_name) == 0) {
        return false;
    }
    // Check alias name exists
    if (_table_map.count(alias) > 0 || _table_aliases.count(alias) > 0) {
        return false;
    }
    bool ok = _table_aliases.insert(std::make_pair(alias, source_name)).second;
    LOG_AND_ASSERT(ok);
    return true;
}

void Database::unalias_table(const std::string& alias) {
    common::ScopedButex lock(_butex);
    _table_aliases.erase(alias);
}

bool DatabaseManager::add_db(const DBRef db) {
    common::ScopedButex lock(_butex);
    auto name = db->name();
    if (_db_map.count(name) != 0) {
        return false;
    }
    _add_op_counter++;
    _db_map[name] = db;
    return true;
}

void Database::serialize(mochow::pb::PDatabase* info) const {
    common::ScopedButex lock(_butex);
    info->set_db_id(_id);
    info->set_db_name(_name);
    info->set_description(_description);
}

bool Database::deserialize(const mochow::pb::PDatabase& info) {
    if (!info.has_db_id() || !info.has_db_name()) {
        LOG(ERROR) << "Fail to deserialize database due to missing some fields,"
                   << " database_pb:" << common::pb2json(info);
        return false;
    }
    _id = info.db_id();
    _name = info.db_name();
    if (info.has_description()) {
        _description = info.description();
    }
    return true;
}

bool DatabaseManager::drop_db(const std::string& name) {
    common::ScopedButex lock(_butex);
    auto count = _db_map.erase(name);
    if (count == 0) {
        return false;
    }
    _drop_op_counter++;
    return true;
}

DBRef DatabaseManager::get_db(const std::string& name) {
    common::ScopedButex lock(_butex);
    auto iter = _db_map.find(name);
    if (iter != _db_map.end()) {
        return iter->second;
    }
    return nullptr;
}

void DatabaseManager::list_db(std::vector<DBRef>* db_list) {
    common::ScopedButex lock(_butex);
    for (auto& [_, db] : _db_map) {
        db_list->push_back(db);
    }
}

bool DatabaseManager::add_table(const TableRef table) {
    common::ScopedButex lock(_butex);
    return _table_map.insert(std::make_pair(table->id(), table)).second;
}
 
bool DatabaseManager::drop_table(const TBLID table_id) {
    common::ScopedButex lock(_butex);
    auto iter = _table_map.find(table_id);
    if (iter != _table_map.end()) {
        _table_map.erase(iter);
        return true;
    }
    return false;
}

TableRef DatabaseManager::get_table(const TBLID table_id) {
    common::ScopedButex lock(_butex);
    auto iter = _table_map.find(table_id);
    return iter != _table_map.end() ? iter->second : nullptr;
}

TableRef DatabaseManager::get_table(const std::string& db_name,
                                    const std::string& table_name) {
    auto db = get_db(db_name);
    if (db) {
        return db->get_table(table_name);
    }
    return nullptr;
}

void DatabaseManager::list_table(std::vector<TableRef>* table_list) {
    common::ScopedButex lock(_butex);
    for (auto& [_, db] : _db_map) {
        db->list_table(table_list);
    }
}

bool DatabaseManager::add_tablet(const TabletRef& tablet) {
    common::ScopedButex lock(_butex);
    auto id = tablet->long_id();
    if (_tablet_map.count(id) != 0) {
        return false;
    }
    _tablet_map[id] = tablet;
    return true;
}

bool DatabaseManager::drop_tablet(uint64_t id) {
    common::ScopedButex lock(_butex);
    auto count = _tablet_map.erase(id);
    return count > 0;
}

TabletRef DatabaseManager::get_tablet(uint64_t id) {
    common::ScopedButex lock(_butex);
    auto iter = _tablet_map.find(id);
    if (iter != _tablet_map.end()) {
        return iter->second;
    }
    return nullptr;
}

void DatabaseManager::list_tablet(std::vector<TabletRef>* tablet_list) {
    common::ScopedButex lock(_butex);
    for (auto& [_, tablet] : _tablet_map) {
        tablet_list->push_back(tablet);
    }
}

size_t DatabaseManager::replica_num() {
    size_t ret = 0;
    common::ScopedButex lock(_butex);
    for (auto& [_, tablet] : _tablet_map) {
        ret += tablet->replica_num();
    }
    return ret;
}

}
