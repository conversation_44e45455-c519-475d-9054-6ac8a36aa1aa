#pragma once

#include <memory>
#include <atomic>
#include <map>
#include <set>
#include <string>
#include <vector> 
#include <algorithm>
#include <unordered_map>
#include <base/time.h>
#include <base/endpoint.h>
#include <sfl/attributes.h>

#include "core/src/common/butex.h"
#include "core/src/common/using.h"
#include "core/src/common/endpoint.h"
#include "core/src/common/pbutils.h"
#include "core/src/common/partition_type.h"
#include "core/src/common/index_state.h"
#include "core/src/common/table_state.h"
#include "core/src/common/tablet_state.h"
#include "core/src/common/replica_state.h"
#include "core/src/common/datanode_state.h"
#include "core/src/common/scalar_index_stage.h"
#include "core/src/schema/schema.h"
#include "baidu/vdb/mochow/core/src/proto/common.pb.h"
#include "baidu/vdb/mochow/core/src/proto/schema.pb.h"

namespace mochow::common {

class User;
class Database;
class Table;
class Tablet;
class Replica;
class Proxy;
class DataNode;

using UserRef = std::shared_ptr<User>;
using DBRef = std::shared_ptr<Database>;
using TableRef = std::shared_ptr<Table>;
using TabletRef = std::shared_ptr<Tablet>;
using ReplicaRef = std::shared_ptr<Replica>;
using ProxyRef = std::shared_ptr<Proxy>;
using DataNodeRef = std::shared_ptr<DataNode>;

// meta base
class MetaBase {
public:
    MetaBase() = default;

    void set_raft_index(uint64_t raft_index) {
        _raft_index = raft_index;
    }
    uint64_t raft_index() const {
        return _raft_index;
    }

protected:
    MetaBase(const mochow::common::MetaBase& rhs) {
        _raft_index = rhs._raft_index;
    }

protected:
    uint64_t _raft_index = 0;
    mutable common::MutexLock _butex;
};

class NodeBase : public MetaBase {
public:
    NodeBase() {
        _basic_metrics = std::make_shared<mochow::pb::BasicMetrics>();
    }

    NodeBase(const base::EndPoint& addr) : _addr(addr) {
        _basic_metrics = std::make_shared<mochow::pb::BasicMetrics>();
    }

    virtual ~NodeBase() = default;

    virtual base::EndPoint addr() const {
        return _addr;
    }
    std::string version() const {
        common::ScopedButex lock(_butex);
        return _version;
    }
    void set_version(const std::string& version) {
        common::ScopedButex lock(_butex);
        _version = version;
    }
    uint64_t last_updated_timestamp() const {
        return _last_updated_timestamp;
    }
    void set_last_updated_timestamp(const uint64_t timestamp) {
        _last_updated_timestamp = timestamp;
    }
    uint64_t last_active_timestamp() const {
        return _last_active_timestamp;
    }
    void set_last_active_timestamp(const uint64_t timestamp) {
        _last_active_timestamp = timestamp;
    }
    bool is_alive() const {
        return _is_alive;
    }
    void set_is_alive(const bool is_alive) {
        _flapping_counter += (_is_alive == is_alive ? 0 : 1);
        _is_alive = is_alive;
    }
    uint64_t flapping_counter() const {
        return _flapping_counter;
    }
    void set_basic_metrics(std::shared_ptr<mochow::pb::BasicMetrics> metrics) {
        common::ScopedButex lock(_butex);
        _basic_metrics = metrics;
    }
    std::shared_ptr<mochow::pb::BasicMetrics> basic_metrics() {
        common::ScopedButex lock(_butex);
        return _basic_metrics;
    }

protected:
    NodeBase(const mochow::common::NodeBase& rhs) : MetaBase(rhs) {
        _addr = rhs._addr;
        _version = rhs._version;
        _last_updated_timestamp = rhs._last_updated_timestamp.load();
        _last_active_timestamp = rhs._last_active_timestamp.load();
        _is_alive = rhs._is_alive.load();
        _flapping_counter = rhs._flapping_counter.load();
        _basic_metrics = rhs._basic_metrics;
    }

protected:
    base::EndPoint _addr;
    std::string _version;
    std::atomic<uint64_t> _last_updated_timestamp = 0;
    std::atomic<uint64_t> _last_active_timestamp = 0;
    std::atomic<bool> _is_alive = false;
    std::atomic<uint64_t> _flapping_counter = 0;
    std::shared_ptr<mochow::pb::BasicMetrics> _basic_metrics;
};

class ManagerBase {
public:
    void set_raft_index(uint64_t raft_index) {
        _raft_index = raft_index;
    }
    uint64_t raft_index() const {
        return _raft_index;
    }
    uint64_t get_add_op_counter() {
        return _add_op_counter;
    }
    uint64_t get_drop_op_counter() {
        return _drop_op_counter;
    }
protected:
    mutable common::Butex _butex;
    mutable uint64_t _raft_index = 0;
    std::atomic<uint64_t> _add_op_counter = 0;
    std::atomic<uint64_t> _drop_op_counter = 0;
};

}
