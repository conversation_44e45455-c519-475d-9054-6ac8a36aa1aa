/**
 * MOCHOW - A Vector Database System.
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON> (<EMAIL>)
 * Date: 2023/09/18
 * Desciption: Implementations for bvar definitions
 *
 */

#pragma once

#include <map>
#include <memory>
#include <bvar/bvar.h>
#include <cassert>
#include <base/time.h>
#include "core/src/proxy/error_code.h"
#include "nlohmann/json.hpp"

#include "core/src/common/butex.h"

namespace mochow::common {

#define MOCHOW_BVAR_ADDER(module, name) \
    bvar::Adder<int64_t> g_##module##_##name##_adder(#module, #name);

#define MOCHOW_BVAR_COUNTER(module, name) \
    bvar::Adder<int64_t> g_##module##_##name##_counter; \
    bvar::Window<bvar::Adder<int64_t> > g_##module##_##name##_counter_minute(#module, #name, &g_##module##_##name##_counter, 60);

#define MOCHOW_BVAR_AVERAGER(module, name) \
    bvar::IntRecorder g_##module##_##name##_averager; \
    bvar::Window<bvar::IntRecorder> g_##module##_##name##_averager_minute \
                        (#module, #name, &g_##module##_##name##_averager, 60);

#define MOCHOW_BVAR_AVERAGER_WITH_WINDOW(module, name, window) \
    bvar::IntRecorder g_##module##_##name##_averager; \
    bvar::Window<bvar::IntRecorder> g_##module##_##name##_averager_window \
                        (#module, #name, &g_##module##_##name##_averager, window);

#define MOCHOW_BVAR_COUNTER_WITH_QPS(module, name) \
    bvar::Adder<int64_t> g_##module##_##name##_counter; \
    bvar::PerSecond<bvar::Adder<int64_t> > g_##module##_##name##_counter_second(#module, #name"_qps", &g_##module##_##name##_counter);

#define MOCHOW_BVAR_THROUGHPUT(module, name) \
    bvar::Adder<int64_t> g_##module##_##name##_counter; \
    bvar::PerSecond<bvar::Adder<int64_t> > g_##module##_##name##_counter_second(#module, #name, &g_##module##_##name##_counter);

// Take care !!!
//  LATENCY contains all (included qps)
#define MOCHOW_BVAR_LATENCY(module, name) \
    bvar::LatencyRecorder g_##module##_##name##_latency(#module, #name);

#define MOCHOW_BVAR_RECORDER(module, name) \
    bvar::Status<int64_t> g_##module##_##name##_recorder(#module, #name, 0);

template <class T>
class BvarPerSecondWithTag {
public:
    typedef std::pair<std::shared_ptr<bvar::Adder<T> >, std::shared_ptr<bvar::PerSecond<bvar::Adder<T> > > > PBvarCounterWithQPS;
    BvarPerSecondWithTag(std::string module, std::string name) {
        _module = module;
        _name = name;
    }
    void put(std::string tag, T value) {
        {
            common::ScopedMutexLock lock(_mutex);
            auto it = _bvar_map.find(tag);
            if (it == _bvar_map.end()) {
                auto bvar_counter = std::make_shared<bvar::Adder<T> >();
                auto bvar_counter_second =
                    std::make_shared<bvar::PerSecond<bvar::Adder<T> > >(_module,
                            tag + "_" + _name, bvar_counter.get());
                _bvar_map[tag] = PBvarCounterWithQPS(bvar_counter, bvar_counter_second);
            }
        }
        auto bvar_counter_second = _bvar_map[tag].first;
        (*bvar_counter_second) << value;
    }
    std::vector<std::string> get_tags() {
        std::vector<std::string> tags{};
        {
            common::ScopedMutexLock lock(_mutex);
            for (auto& kv : _bvar_map) {
                tags.push_back(kv.first);
            }
        }
        return tags;
    }
    std::map<std::string, T> get_tags_and_value() {
        std::map<std::string, T> res{};
        {
            common::ScopedMutexLock lock(_mutex);
            for (auto& kv : _bvar_map) {
                res[kv.first] = kv.second.second->get_value(1);
            }
        }
        return res;
    }
private:
    common::MutexLock _mutex;
    std::string _module;
    std::string _name;
    std::map<std::string, PBvarCounterWithQPS> _bvar_map;
};

template <class T>
class BvarStatusWithTag {
public:
    BvarStatusWithTag(const std::string& module, const std::string& name) {
        _module = module;
        _name = name;
    }
    void put(const std::string& tag, T value) {
        std::shared_ptr<bvar::Status<T>> bvar_instance = nullptr;
        {
            common::ScopedMutexLock lock(_mutex);
            auto it = _bvar_map.find(tag);
            if (it == _bvar_map.end()) {
                bvar_instance =
                    std::make_shared<bvar::Status<T>>(_module,
                            tag + "_" + _name, T());
                _bvar_map[tag] = bvar_instance;
            } else {
                bvar_instance = it->second;
            }
        }
        (*bvar_instance).set_value(value);
    }
    T get(const std::string&tag) {
        std::shared_ptr<bvar::Status<T>> bvar_instance = nullptr;
        {
            common::ScopedMutexLock lock(_mutex);
            auto it = _bvar_map.find(tag);
            if (it == _bvar_map.end()) {
               return T();
            }
            bvar_instance = it->second;
        }
        return (*bvar_instance).get_value(); 
    }
    void reset() {
        common::ScopedMutexLock lock(_mutex);
        for (auto & [_, status] : _bvar_map) {
            status->set_value(T());
        }
    }
private:
    common::MutexLock _mutex;
    std::string _module;
    std::string _name;
    std::map<std::string, std::shared_ptr<bvar::Status<T>>> _bvar_map;
};

template <template <typename> class Reducer, typename T>
class BvarReducer {
public:
    BvarReducer(const std::string& module, const std::string& name, int window_second = -1) {
        _reducer.reset(new Reducer<T>());
        assert(_reducer != nullptr);
        if (window_second != -1) {
             _per_window_second.reset(new bvar::Window<Reducer<T> > (module, name, _reducer.get(),  window_second));
            assert(_per_window_second != nullptr);
        }
        _window_second = window_second;
    }
    void put(T value) {
        (*(_reducer.get())) << value;
    }
    T get() {
        if (_window_second != -1) {
            return _per_window_second->get_value();
        }
        return _reducer->get_value();
    }
    void reset() {
        _reducer->reset();
    }
private:
    std::unique_ptr<Reducer<T> > _reducer = nullptr;
    std::unique_ptr<bvar::Window<Reducer<T> > > _per_window_second = nullptr;
    int _window_second;
};

template <template <typename> class Reducer, typename T>
class BvarReducerWithTag {
public:
    typedef std::pair<std::shared_ptr<Reducer<T> >, std::shared_ptr<bvar::Window<Reducer<T> > > > PBvarReducerWindow;
    BvarReducerWithTag(const std::string& module, const std::string& name, int window_second = -1) {
        _module = module;
        _name = name;
        _window_second = window_second;
    }
    void put(const std::string& tag, T value) {
        std::shared_ptr<Reducer<T> > bvar_instance = nullptr;
        std::shared_ptr<bvar::Window<Reducer<T> > > bvar_reducer_window = nullptr;
        {
            common::ScopedMutexLock lock(_mutex);
            auto it = _bvar_map.find(tag);
            if (it == _bvar_map.end()) {
                if (_window_second != -1) {
                    bvar_instance = std::make_shared<Reducer<T>>();
                    assert(bvar_instance != nullptr);
                    bvar_reducer_window = std::make_shared<bvar::Window<Reducer<T> > >(
                        _module, tag + "_" + _name, bvar_instance.get(), _window_second);
                    assert(bvar_reducer_window != nullptr);
                } else {
                    bvar_instance = std::make_shared<Reducer<T>>(_module, tag + "_" + _name);
                    assert(bvar_instance != nullptr);
                }
                _bvar_map[tag] = PBvarReducerWindow(bvar_instance, bvar_reducer_window);
            } else {
                bvar_instance = it->second.first;
            }
        }
        *(bvar_instance) << value;
    }
    T get(const std::string& tag) {
        std::shared_ptr<Reducer<T> > bvar_instance = nullptr;
        std::shared_ptr<bvar::Window<Reducer<T> > > bvar_reducer_window = nullptr;
        {
            common::ScopedMutexLock lock(_mutex);
            auto it = _bvar_map.find(tag);
            if (it == _bvar_map.end()) {
               return T();
            }
            bvar_instance = it->second.first;
            bvar_reducer_window = it->second.second;
        }
        if (_window_second != -1) {
            return bvar_reducer_window->get_value(); 
        }
        return bvar_instance->get_value(); 
        
    }
    void reset() {
        common::ScopedMutexLock lock(_mutex);
        for (auto & [_, window] : _bvar_map) {
            window.first->reset();
        }
    }
private:
    common::MutexLock _mutex;
    std::string _module;
    std::string _name;
    int _window_second;
    std::map<std::string, PBvarReducerWindow> _bvar_map;
};

class BvarIntRecorder {
public:
    BvarIntRecorder(const std::string& module, const std::string& name, int32_t window_second = -1) {
        _recorder.reset(new bvar::IntRecorder());
        assert(_recorder != nullptr);
        if (window_second != -1) {
             _per_window_second.reset(new bvar::Window<bvar::IntRecorder> (module, name, _recorder.get(),  window_second));
            assert(_per_window_second != nullptr);
        }
        _window_second = window_second;
    }
    void put(int64_t value) {
        (*(_recorder.get())) << value;
    }
    int64_t get() {
        if (_window_second != -1) {
            return _per_window_second->get_value().get_average_int();
        }
        return _recorder->get_value().get_average_int();
    }
    void reset() {
        _recorder->reset();
    }
private:
    std::unique_ptr<bvar::IntRecorder> _recorder = nullptr;
    std::unique_ptr<bvar::Window<bvar::IntRecorder> > _per_window_second = nullptr;
    int _window_second;
};

class BvarIntRecorderWithTag {
public:
    typedef std::pair<std::shared_ptr<bvar::IntRecorder>, std::shared_ptr<bvar::Window<bvar::IntRecorder> > > PBvarIntRecorderWindow;
    BvarIntRecorderWithTag(const std::string& module, const std::string& name, int window_second = -1) {
        _module = module;
        _name = name;
        _window_second = window_second;
    }
    void put(const std::string& tag, int64_t value) {
        std::shared_ptr<bvar::IntRecorder> bvar_instance = nullptr;
        std::shared_ptr<bvar::Window<bvar::IntRecorder> > bvar_reducer_window = nullptr;
        {
            common::ScopedMutexLock lock(_mutex);
            auto it = _bvar_map.find(tag);
            if (it == _bvar_map.end()) {
                if (_window_second != -1) {
                    bvar_instance =
                        std::make_shared<bvar::IntRecorder>();
                    assert(bvar_instance != nullptr);
                    bvar_reducer_window = std::make_shared<bvar::Window<bvar::IntRecorder> >(
                        _module, tag + "_" + _name, bvar_instance.get(), _window_second);
                    assert(bvar_reducer_window != nullptr);
                } else {
                    bvar_instance =
                        std::make_shared<bvar::IntRecorder>(_module, tag + "_" + _name);
                    assert(bvar_instance != nullptr);
                }
                _bvar_map[tag] = PBvarIntRecorderWindow(bvar_instance, bvar_reducer_window);
            } else {
                bvar_instance = it->second.first;
            }
        }
        *(bvar_instance) << value;
    }
    int64_t get(const std::string& tag) {
        std::shared_ptr<bvar::IntRecorder> bvar_instance = nullptr;
        std::shared_ptr<bvar::Window<bvar::IntRecorder> > bvar_reducer_window = nullptr;
        {
            common::ScopedMutexLock lock(_mutex);
            auto it = _bvar_map.find(tag);
            if (it == _bvar_map.end()) {
               return 0;
            }
            bvar_instance = it->second.first;
            bvar_reducer_window = it->second.second;
        }
        
        if (_window_second != -1) {
            return bvar_reducer_window->get_value().get_average_int();
        }
        return bvar_instance->get_value().get_average_int();
        
    }
    void reset() {
        common::ScopedMutexLock lock(_mutex);
        for (auto & [_, window] : _bvar_map) {
            window.first->reset();
        }
    }
private:
    common::MutexLock _mutex;
    std::string _module;
    std::string _name;
    int _window_second;
    std::map<std::string, PBvarIntRecorderWindow> _bvar_map;
};

template <class T>
class BvarCounterInPerSecond {
public:
    BvarCounterInPerSecond(const std::string& module, const std::string& name) {
        _adder.reset(new bvar::Adder<T>());
        assert(_adder != nullptr);
        _per_second.reset(new bvar::PerSecond<bvar::Adder<T> > (module, name, _adder.get()));
        assert(_per_second != nullptr);
    }
    BvarCounterInPerSecond(const std::string& module, const std::string& name, uint32_t window_second) {
        _adder.reset(new bvar::Adder<T>());
        assert(_adder != nullptr);
        _per_second.reset(new bvar::PerSecond<bvar::Adder<T> > (module, name, _adder.get(),  window_second));
        assert(_per_second != nullptr);
    }
    void put(T value) {
        (*(_adder.get())) << value;
    }
    T get() {
        return _per_second->get_value(_per_second->window_size());
    }
private:
    std::unique_ptr<bvar::Adder<T> > _adder = nullptr;
    std::unique_ptr<bvar::PerSecond<bvar::Adder<T> > > _per_second = nullptr;
};

template <class T>
class BvarCounterInWindow {
public:
    BvarCounterInWindow(const std::string& module, const std::string& name, uint32_t window_second = 60) {
        _adder.reset(new bvar::Adder<T>());
        assert(_adder != nullptr);
        _window.reset(new bvar::Window<bvar::Adder<T> > (module, name, _adder.get(),  window_second));
        assert(_window != nullptr);
    }
    void put(T value) {
        (*(_adder.get())) << value;
    }
    T get() {
        return _window->get_value();
    }
private:
    std::unique_ptr<bvar::Adder<T> > _adder = nullptr;
    std::unique_ptr<bvar::Window<bvar::Adder<T> > > _window = nullptr;
};

class BvarLatency {
public:
    BvarLatency(std::string module, std::string name) {
        _latency.reset(new bvar::LatencyRecorder(module, name));
        assert(_latency != nullptr);
    }
    void put(int64_t value) {
        (*(_latency.get())) << value;
    }
    int64_t get_quantile(int32_t quantile) {
        return (*(_latency.get())).latency_percentile((double)quantile / 100);
    }
    int64_t qps() {
        return _latency->qps();
    }
private:
    std::unique_ptr<bvar::LatencyRecorder> _latency;
};

// record metrics for single operation.
class OperationMetricRecorder {
public:
    OperationMetricRecorder(std::string module, std::string name) {
        _metric_prefix = module + std::string("_") + name;
        _query_adder.reset(new bvar::Adder<float>());
        assert(_query_adder != nullptr);
        _bytes_adder.reset(new bvar::Adder<uint64_t>());
        assert(_bytes_adder != nullptr);
        _http_4xx_fail_adder.reset(new bvar::Adder<uint64_t>());
        assert(_http_4xx_fail_adder != nullptr);
        _http_5xx_fail_adder.reset(new bvar::Adder<uint64_t>());
        assert(_http_5xx_fail_adder != nullptr);
        _internal_fail_adder.reset(new bvar::Adder<uint64_t>());
        assert(_internal_fail_adder != nullptr);
        _timeout_adder.reset(new bvar::Adder<uint64_t>());
        assert(_timeout_adder != nullptr);
        _latency.reset(new bvar::LatencyRecorder(module, name, 60));
        assert(_latency != nullptr);
        _query_per_second.reset(new bvar::PerSecond<bvar::Adder<float> > (module, name, _query_adder.get(), 60));
        assert(_query_per_second != nullptr);
        _byte_per_second.reset(new bvar::PerSecond<bvar::Adder<uint64_t> > (module, name, _bytes_adder.get(), 60));
        assert(_byte_per_second != nullptr);
    }

    // record
    void record_query(size_t query_count = 1) {
        (*(_query_adder.get())) << query_count;
    }

    void record_fail(int status_code, size_t query_count = 1) {
        if (status_code < 400 || status_code >= 600) {
            return;
        }
        if (status_code < 500) {
            (*(_http_4xx_fail_adder.get())) << query_count;
            if (status_code == static_cast<int>(proxy::HTTPErrorCode::REQUEST_TIMEOUT)) {
                record_timeout();
            }
        } else {
            (*(_http_5xx_fail_adder.get())) << query_count;
        }
    }

    void record_timeout(size_t timeout_count = 1) {
        (*(_timeout_adder.get())) << timeout_count;
    }

    void record_internal_fail(size_t query_count = 1) {
        (*(_internal_fail_adder.get())) << query_count;
    }

    void record_bytes(uint64_t value) {
        (*(_bytes_adder.get())) << value;
    }

    void record_latency(int64_t latency) {
        (*(_latency.get())) << latency;
    }

    // get
    int64_t latency_percentile(double ratio) {
        // ratio=0.99 means 99% percentile
        int64_t percentile = (*(_latency.get())).latency_percentile(ratio);
        if (std::isnan(percentile)) {
            percentile = 0;
        }
        return percentile;
    }

    int64_t latency() {
        int64_t latency = (*(_latency.get())).latency();
        if (std::isnan(latency)) {
            latency = 0;
        }
        return latency;
    }

    float qps() {
        float qps = _query_per_second->get_value(_query_per_second->window_size());
        if (std::isnan(qps)) {
            qps = 0;
        }
        return qps;
    }

    uint64_t http_4xx_fail_count() {
        return _http_4xx_fail_adder->get_value();
    }

    uint64_t http_5xx_fail_count() {
        return _http_5xx_fail_adder->get_value();
    }

    uint64_t internal_fail_count() {
        return _internal_fail_adder->get_value();
    }

    uint64_t timeout_count() {
        return _timeout_adder->get_value();
    }

    uint64_t throughput() {
        uint64_t throughput = _byte_per_second->get_value(_byte_per_second->window_size());
        if (std::isnan(throughput)) {
            throughput = 0;
        }
        return throughput;
    }

    double succ_rate() {
        double succ_rate = (double)(_latency->count()) / _query_adder->get_value();
        if (std::isnan(succ_rate)) {
            succ_rate = 1.0;
        }
        return succ_rate;
    }

    nlohmann::json to_json() {
        nlohmann::json json;
        json[_metric_prefix + "_qps"] = qps();
        json[_metric_prefix + "_4xx_fail_count"] = http_4xx_fail_count();
        json[_metric_prefix + "_5xx_fail_count"] = http_5xx_fail_count();
        json[_metric_prefix + "_internal_error_count"] = internal_fail_count();
        json[_metric_prefix + "_timeout_count"] = timeout_count();
        json[_metric_prefix + "_succ_rate"] = succ_rate();
        // make it milli-second
        json[_metric_prefix + "_latency_avg"] = (double)latency() / 1000;
        json[_metric_prefix + "_latency_P50"] = (double)latency_percentile(0.5) / 1000; 
        json[_metric_prefix + "_latency_P90"] = (double)latency_percentile(0.9) / 1000;
        json[_metric_prefix + "_latency_P99"] = (double)latency_percentile(0.99) / 1000;
        return json;
    }

    std::string to_string(int indent = -1) {
        return to_json().dump(indent);
    }

private:
    std::string _metric_prefix;

    std::unique_ptr<bvar::Adder<float>> _query_adder = nullptr;
    std::unique_ptr<bvar::Adder<uint64_t>> _bytes_adder = nullptr;
    std::unique_ptr<bvar::Adder<uint64_t>> _http_4xx_fail_adder = nullptr;
    std::unique_ptr<bvar::Adder<uint64_t>> _http_5xx_fail_adder = nullptr;
    std::unique_ptr<bvar::Adder<uint64_t>> _internal_fail_adder = nullptr;
    std::unique_ptr<bvar::Adder<uint64_t>> _timeout_adder = nullptr;

    // only record latency of successful queries.
    std::unique_ptr<bvar::LatencyRecorder> _latency = nullptr;
    // qps
    std::unique_ptr<bvar::PerSecond<bvar::Adder<float> > > _query_per_second = nullptr;
    // throughput
    std::unique_ptr<bvar::PerSecond<bvar::Adder<uint64_t> > > _byte_per_second = nullptr;
};

using OperationMetricRecorderPtr = std::unique_ptr<OperationMetricRecorder>;

}