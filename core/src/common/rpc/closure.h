/**
 * MOCHOW - A Vector Database System.
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON> (<EMAIL>)
 * Date: 2023/09/18
 * Desciption: Declarations for Closure
 *
 */

#pragma once

#include <base/status.h>
#include <bthread/countdown_event.h>
#include <google/protobuf/stubs/common.h>

// Put third-party headers which contains compile warnings into the following
// code block
#pragma GCC diagnostic push
// NOTE: ignore cerntain warnings by add macros like:
// #pragma GCC diagnostic ignored "-Wuninitialized"
#pragma GCC diagnostic pop

#if __cplusplus < 201103L
#define INF_MOCHOW_SCOPED_PTR(ptr)                    \
    std::unique_ptr<BAIDU_TYPEOF(*(ptr))>             \
    BAIDU_CONCAT(ptr_guard_at_line_, __LINE__)((ptr))
#else
// c++11 deduces additional reference to the type.
#define INF_MOCHOW_SCOPED_PTR(ptr)                    \
    std::unique_ptr<typename std::remove_reference<BAIDU_TYPEOF(*(ptr))>::type> \
    BAIDU_CONCAT(ptr_guard_at_line_, __LINE__)((ptr))
#endif

namespace mochow::common {

// Closure which encloses a base::Status to report if the operation was successful.
class Closure : public google::protobuf::Closure {
public:
    base::Status& status() { return _st; }
    const base::Status& status() const { return _st; }

private:
    base::Status _st;
};

// A special Closure which provides synchronization primitives
class SynchronizedClosure : public Closure {
public:
    SynchronizedClosure() {
    }

    SynchronizedClosure(int num_signal) {
        _event.reset(num_signal);
    }
    // Implements Closure
    void Run() override {
        _event.signal();
    }
    // Block the thread until Run() has been called
    void wait() {
        _event.wait();
    }
    // Reset the event
    void reset() {
        status().reset();
        _event.reset();
    }
private:
    bthread::CountdownEvent _event;
};

// Start a bthread to run closure
void run_closure_in_bthread(::google::protobuf::Closure* closure,
                           bool in_pthread = false);

struct RunClosureInBthread {
    void operator()(google::protobuf::Closure* done) {
        return run_closure_in_bthread(done);
    }
};

// Start a bthread to run closure without signal other worker thread to steal
// it. You should call bthread_flush() at last.
void run_closure_in_bthread_nosig(::google::protobuf::Closure* closure,
                                  bool in_pthread = false);

struct RunClosureInBthreadNoSig {
    void operator()(google::protobuf::Closure* done) {
        return run_closure_in_bthread_nosig(done);
    }
};

}
