/**
 * MOCHOW - A Vector Database System.
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON> (caoshi<PERSON>@baidu.com)
 * Date: 2023/10/11
 * Desciption: Declaration and definition of rpc stubs
 *
 */

#pragma once

#include "baidu/vdb/mochow/core/src/proto/master.pb.h"
#include "baidu/vdb/mochow/core/src/proto/datanode.pb.h"
#include "baidu/vdb/mochow/core/src/proto/data_rpc.pb.h"
#include "baidu/vdb/mochow/core/src/proto/proxy.pb.h"
#include "core/src/common/rpc/rpc_call.h"

namespace mochow::common {

#define REDIRECT_MASTER_RPC_CALL(RPC_NAME, MASTER_ADDR, REQUEST, RESPONSE, OPTIONS) \
    { \
        common::MasterStub _stub_; \
        common::SynchronizedClosure _closure_; \
        _stub_.RPC_NAME(MASTER_ADDR, REQUEST, RESPONSE, &_closure_, OPTIONS); \
        _closure_.wait(); \
        if ((RESPONSE)->status().code() == ERR_NOT_PRIMARY) { \
            auto _leader_ = common::node2endpoint((RESPONSE)->status().leader_hint()); \
            if (_leader_.ip != base::IP_ANY && _leader_.port != 0) { \
                common::SynchronizedClosure _redirect_closure_; \
                (RESPONSE)->Clear(); \
                _stub_.RPC_NAME(_leader_, REQUEST, RESPONSE, &_redirect_closure_, OPTIONS); \
                _redirect_closure_.wait(); \
            } \
        } \
    }

#define DEFINE_RPC_STUB(RPC_NAME, REQUEST, RESPONSE, SERVICE) \
    virtual void RPC_NAME(const base::EndPoint& remote_addr, \
                        mochow::pb::REQUEST* request, \
                        mochow::pb::RESPONSE* response, \
                        ::google::protobuf::Closure* done, \
                        const RpcCallOptions* options = NULL) { \
        issue_rpc(_local_addr, remote_addr, \
                        &mochow::pb::SERVICE##_Stub::RPC_NAME, \
                        request, response, done, #RPC_NAME, options); \
    }

#define DEFINE_RPC_STUB_IN_SUB_NAMESPACE(RPC_NAME, NAMESPACE, REQUEST, RESPONSE, SERVICE) \
    virtual void RPC_NAME(const base::EndPoint& remote_addr, \
                        mochow::NAMESPACE::pb::REQUEST* request, \
                        mochow::NAMESPACE::pb::RESPONSE* response, \
                        ::google::protobuf::Closure* done, \
                        const RpcCallOptions* options = NULL) { \
        issue_rpc(_local_addr, remote_addr, \
                        &mochow::NAMESPACE::pb::SERVICE##_Stub::RPC_NAME, \
                        request, response, done, #RPC_NAME, options); \
    }

#define DEFINE_RPC_STUB_WITH_CHANNEL(RPC_NAME, REQUEST, RESPONSE, SERVICE) \
    virtual void RPC_NAME(const base::EndPoint& remote_addr, \
                        mochow::pb::REQUEST* request, \
                        mochow::pb::RESPONSE* response, \
                        ::google::protobuf::Closure* done, \
                        const RpcCallOptions* options = NULL, \
                        const baidu::rpc::Channel* channel = NULL) { \
        issue_rpc(_local_addr, remote_addr, \
                        &mochow::pb::SERVICE##_Stub::RPC_NAME, \
                        request, response, done, #RPC_NAME, options, \
                        NULL, NULL, channel); \
    }

#define DEFINE_RPC_STUB_WITH_ATTACHMENT(RPC_NAME, REQUEST, RESPONSE, SERVICE) \
    virtual void RPC_NAME(const base::EndPoint& remote_addr, \
                        mochow::pb::REQUEST* request, \
                        mochow::pb::RESPONSE* response, \
                        ::google::protobuf::Closure* done, \
                        const RpcCallOptions* options = NULL, \
                        base::IOBuf* request_attachment = NULL, \
                        base::IOBuf* response_attachment = NULL) { \
        issue_rpc(_local_addr, remote_addr, \
                        &mochow::pb::SERVICE##_Stub::RPC_NAME, \
                        request, response, done, #RPC_NAME, options, \
                        request_attachment, response_attachment); \
    }

#define DEFINE_RPC_STUB_WITH_ATTACHMENT_AND_CHANNEL(RPC_NAME, REQUEST, RESPONSE, SERVICE) \
    virtual void RPC_NAME(const base::EndPoint& remote_addr, \
                        mochow::pb::REQUEST* request, \
                        mochow::pb::RESPONSE* response, \
                        ::google::protobuf::Closure* done, \
                        const RpcCallOptions* options = NULL, \
                        base::IOBuf* request_attachment = NULL, \
                        base::IOBuf* response_attachment = NULL, \
                        const baidu::rpc::Channel* channel = NULL) { \
        issue_rpc(_local_addr, remote_addr, \
                        &mochow::pb::SERVICE##_Stub::RPC_NAME, \
                        request, response, done, #RPC_NAME, options, \
                        request_attachment, response_attachment, channel); \
    }

/**
 * Master Stub
 */
class MasterStub {
public:
    MasterStub(const base::EndPoint& local_addr)
        : _local_addr(local_addr) {}
    MasterStub() {}
    virtual ~MasterStub() {}

    // MasterDatabaseService
    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(create_database, master, \
            CreateDatabaseRequest, AckResponse, MasterDatabaseService);

    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(drop_database, master, \
            DropDatabaseRequest, AckResponse, MasterDatabaseService);
            
    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(update_database, master, \
            UpdateDatabaseRequest, AckResponse, MasterDatabaseService);

    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(show_database, master, \
            ShowDatabaseRequest, ShowDatabaseResponse, MasterDatabaseService);

    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(list_database, master, \
            ListDatabaseRequest, ListDatabaseResponse, MasterDatabaseService);

    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(create_table, master, \
            CreateTableRequest, AckResponse, MasterDatabaseService);

    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(drop_table, master, \
            DropTableRequest, AckResponse, MasterDatabaseService);

    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(update_table, master, \
            UpdateTableRequest, AckResponse, MasterDatabaseService);

    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(alias_table, master, \
            AliasTableRequest, AckResponse, MasterDatabaseService);

    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(unalias_table, master, \
            UnaliasTableRequest, AckResponse, MasterDatabaseService);

    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(add_column, master, \
            AddColumnRequest, AckResponse, MasterDatabaseService);

    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(show_table, master, \
            ShowTableRequest, ShowTableResponse, MasterDatabaseService);

    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(list_table, master, \
            ListTableRequest, ListTableResponse, MasterDatabaseService);

    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(create_index, master, \
            CreateIndexRequest, AckResponse, MasterDatabaseService);

    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(drop_index, master, \
            DropIndexRequest, AckResponse, MasterDatabaseService);

    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(rebuild_index, master, \
            RebuildIndexRequest, AckResponse, MasterDatabaseService);

    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(show_index, master, \
            ShowIndexRequest, ShowIndexResponse, MasterDatabaseService);

    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(list_index, master, \
            ListIndexRequest, ListIndexResponse, MasterDatabaseService);

    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(modify_index, master, \
            ModifyIndexRequest, AckResponse, MasterDatabaseService);

    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(create_user, master, \
            CreateUserRequest, AckResponse, MasterDatabaseService);

    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(drop_user, master, \
            DropUserRequest, AckResponse, MasterDatabaseService);

    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(change_password, master, \
            ChangePasswordRequest, AckResponse, MasterDatabaseService);

    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(create_role, master, \
            CreateRoleRequest, AckResponse, MasterDatabaseService);

    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(drop_role, master, \
            DropRoleRequest, AckResponse, MasterDatabaseService);

    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(grant_user_roles, master, \
            GrantUserRolesRequest, AckResponse, MasterDatabaseService);

    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(revoke_user_roles, master, \
            RevokeUserRolesRequest, AckResponse, MasterDatabaseService);

    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(grant_user_privileges, master, \
            GrantUserPrivilegesRequest, AckResponse, MasterDatabaseService);

    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(revoke_user_privileges, master, \
            RevokeUserPrivilegesRequest, AckResponse, MasterDatabaseService);

    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(grant_role_privileges, master, \
            GrantRolePrivilegesRequest, AckResponse, MasterDatabaseService);

    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(revoke_role_privileges, master, \
            RevokeRolePrivilegesRequest, AckResponse, MasterDatabaseService);

    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(show_user_privileges, master, \
            ShowUserPrivilegesRequest, ShowUserPrivilegesResponse, MasterDatabaseService);

    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(show_role_privileges, master, \
            ShowRolePrivilegesRequest, ShowRolePrivilegesResponse, MasterDatabaseService);

    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(select_user, master, \
            SelectUserRequest, SelectUserResponse, MasterDatabaseService);

    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(select_role, master, \
            SelectRoleRequest, SelectRoleResponse, MasterDatabaseService);

    // MasterControlService
    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(add_datanode, master, \
            AddDataNodeRequest, AckResponse, MasterControlService);

    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(drop_datanode, master, \
            DropDataNodeRequest, AckResponse, MasterControlService);
            
    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(update_datanode, master, \
            UpdateDataNodeRequest, AckResponse, MasterControlService);

    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(relocate_datanode_addr, master, \
            RelocateDatanodeAddrRequest, AckResponse, MasterControlService);

    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(add_master_peer, master, \
            AddMasterPeerRequest, AckResponse, MasterControlService);

    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(drop_master_peer, master, \
            DropMasterPeerRequest, AckResponse, MasterControlService);

    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(set_master_peers, master, \
            SetMasterPeersRequest, AckResponse, MasterControlService);

    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(transfer_leader, master, \
            TransferLeaderRequest, AckResponse, MasterControlService);

    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(transfer_tablet_leader, master, \
            TransferTabletLeaderRequest, AckResponse, MasterControlService);

    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(balance_tablet, master, \
            BalanceTabletRequest, AckResponse, MasterControlService);

    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(drop_tablet_replica, master, \
            DropTabletReplicaRequest, AckResponse, MasterControlService);

    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(create_tablet_replica, master, \
            CreateTabletReplicaRequest, AckResponse, MasterControlService);

    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(snapshot, master, \
            SnapshotRequest, AckResponse, MasterControlService);
    
    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(pause_tablet_balancer, master, \
            TabletBalancerRequest, AckResponse, MasterControlService);

    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(control_balance, master, \
            ControlBalanceRequest, AckResponse, MasterControlService);

    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(resume_tablet_balancer, master, \
            TabletBalancerRequest, AckResponse, MasterControlService);
    
    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(drop_az, master, \
            DropAzRequest, AckResponse, MasterControlService);
    
    // MasterReportService
    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(datanode_heartbeat, master, \
            DataNodeHeartbeatRequest, DataNodeHeartbeatResponse, MasterReportService);

    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(proxy_heartbeat, master, \
            ProxyHeartbeatRequest, ProxyHeartbeatResponse, MasterReportService);

    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(create_tablet_replica_finish, master, \
            UpdateTabletReplicaRequest, AckResponse, MasterReportService);

    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(rebuild_index_finish, master, \
            RebuildIndexFinishRequest, AckResponse, MasterReportService);

    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(report_tablet_leader, master, \
            ReportTabletLeaderRequest, AckResponse, MasterReportService);

    // MasterQueryService
    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(check_leader, master, \
            CheckLeaderRequest, CheckLeaderResponse, MasterQueryService);

    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(fetch_schema, master, \
            FetchSchemaRequest, FetchSchemaResponse, MasterQueryService);

    // list_user would removed in version 2.0
    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(list_user, master, \
            ListUserRequest, ListUserResponse, MasterQueryService);

    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(list_user_internal, master, \
            ListUserInternalRequest, ListUserInternalResponse, MasterQueryService);

    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(show_master, master, \
            ShowMasterRequest, ShowMasterResponse, MasterQueryService);

    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(show_datanode, master, \
            ShowDataNodeRequest, ShowDataNodeResponse, MasterQueryService);

    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(list_datanode, master, \
            ListDataNodeRequest, ListDataNodeResponse, MasterQueryService);

    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(check_drop_datanode, master, \
            CheckDropDataNodeRequest, AckResponse, MasterQueryService);

    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(check_datanode_tablet_perfection, master, \
            CheckDataNodeTabletPerfectionRequest, AckResponse, MasterQueryService);

    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(show_proxy, master, \
            ShowProxyRequest, ShowProxyResponse, MasterQueryService);

    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(list_proxy, master, \
            ListProxyRequest, ListProxyResponse, MasterQueryService);

    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(show_tablet, master, \
            ShowTabletRequest, ShowTabletResponse, MasterQueryService);

    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(list_tablet, master, \
            ListTabletRequest, ListTabletResponse, MasterQueryService);

    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(list_datanode_tablet, master, \
            ListDataNodeTabletRequest, ListDataNodeTabletResponse, MasterQueryService);

    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(list_datanode_replica, master, \
            ListDataNodeReplicaRequest, ListDataNodeReplicaResponse, MasterQueryService);

    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(list_joining_replica, master, \
            ListJoiningReplicaRequest, ListJoiningReplicaResponse, MasterQueryService);

    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(show_table_stats, master, \
            ShowTableStatsRequest, ShowTableStatsResponse, MasterQueryService);

    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(show_table_internal, master, \
            ShowTableInternalRequest, ShowTableInternalResponse, MasterQueryService);

    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(list_table_internal, master, \
            ListTableInternalRequest, ListTableInternalResponse, MasterQueryService);

    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(list_database_internal, master, \
            ListDatabaseInternalRequest, ListDatabaseInternalResponse, MasterQueryService);

    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(list_balance_task, master, \
            ListBalanceTaskRequest, ListBalanceTaskResponse, MasterQueryService);
    
    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(fetch_table_config, master, \
            FetchTableConfigRequest, FetchTableConfigResponse, MasterQueryService);

    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(refresh_rbac_info, master, \
            RefreshRBACInfoRequest, RefreshRBACInfoResponse, MasterQueryService);

private:
    base::EndPoint _local_addr;
};

/**
 * DataNode Stub
 */
class DataNodeStub {
public:
    DataNodeStub(const base::EndPoint& local_addr)
        : _local_addr(local_addr) {}
    DataNodeStub() {}
    virtual ~DataNodeStub() {}

    // DataNodeControlService
    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(create_replica, datanode, \
            CreateReplicaRequest, AckResponse, DataNodeControlService);

    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(drop_replica, datanode, \
            DropReplicaRequest, AckResponse, DataNodeControlService);

    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(add_tablet_peer, datanode, \
            AddTabletPeerRequest, AckResponse, DataNodeControlService);

    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(remove_tablet_peer, datanode, \
            RemoveTabletPeerRequest, AckResponse, DataNodeControlService);

    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(set_tablet_peers, datanode, \
            SetTabletPeersRequest, AckResponse, DataNodeControlService);

    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(transfer_tablet_leader, datanode, \
            TransferTabletLeaderRequest, AckResponse, DataNodeControlService);

    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(query_tablet_leader, datanode, \
            QueryTabletLeaderRequest, QueryTabletLeaderResponse, DataNodeControlService);

    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(drop_index, datanode, \
            DropIndexRequest, AckResponse, DataNodeControlService);

    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(reload_vector_index, datanode, \
            ReloadVectorIndexRequest, AckResponse, DataNodeControlService);

    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(release_vector_index, datanode, \
            ReleaseVectorIndexRequest, AckResponse, DataNodeControlService);

    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(create_snapshot, datanode, \
            CreateSnapshotRequest, CreateSnapshotResponse, DataNodeControlService);

    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(drop_snapshot, datanode, \
            DropSnapshotRequest, DropSnapshotResponse, DataNodeControlService);

    DEFINE_RPC_STUB_IN_SUB_NAMESPACE(compact, datanode, \
            CompactRequest, CompactResponse, DataNodeControlService);

    // DataNodeDataService
    DEFINE_RPC_STUB(Insert, \
            InsertRequest, AckResponse, MochowDataService);

    DEFINE_RPC_STUB(BatchInsert, \
            BatchInsertRequest, BatchInsertResponse, MochowDataService);

    DEFINE_RPC_STUB(Upsert, \
            UpsertRequest, AckResponse, MochowDataService);

    DEFINE_RPC_STUB(BatchUpsert, \
            BatchUpsertRequest, BatchUpsertResponse, MochowDataService);

    DEFINE_RPC_STUB(Update, \
            UpdateRequest, AckResponse, MochowDataService);

    DEFINE_RPC_STUB(Delete, \
            DeleteRequest, AckResponse, MochowDataService);

    DEFINE_RPC_STUB(BatchWrite, \
            BatchWriteRequest, AckResponse, MochowDataService);

    DEFINE_RPC_STUB(Nop, \
           TabletNOPRequest, AckResponse, MochowDataService);

    // DataNodeQueryService
    DEFINE_RPC_STUB(Query, \
            QueryRequest, QueryResponse, MochowQueryService);

    DEFINE_RPC_STUB(BatchQuery, \
            BatchQueryRequest, BatchQueryResponse, MochowQueryService);

    DEFINE_RPC_STUB(Select, \
            SelectRequest, SelectResponse, MochowQueryService);

    DEFINE_RPC_STUB(Search, \
            SearchRequest, SearchResponse, MochowQueryService);

    DEFINE_RPC_STUB(HybridSearch, \
            HybridSearchRequest, SearchResponse, MochowQueryService);

    DEFINE_RPC_STUB(show_tablets, \
            ShowTabletRequest, ShowTabletResponse, MochowQueryService);

private:
    base::EndPoint _local_addr;
};

/**
 * Proxy Stub
 */
class ProxyStub {
public:
    ProxyStub(const base::EndPoint& local_addr)
        : _local_addr(local_addr) {}
    ProxyStub() {}
    virtual ~ProxyStub() {}

    // ProxyControlService
    DEFINE_RPC_STUB(trigger_refresh, \
            TriggerRefreshRequest, AckResponse, ProxyControlService);

private:
    base::EndPoint _local_addr;
};

/**
 * ConfigReloader Stub
 */
class ConfigStub {
public:
    ConfigStub(const base::EndPoint& local_addr)
        : _local_addr(local_addr) {}
    ConfigStub() {}
    virtual ~ConfigStub() {}

    DEFINE_RPC_STUB(reload_conf, \
            UpdateConfRequest, AckResponse, ConfigReloaderService);
    DEFINE_RPC_STUB(update_conf, \
            UpdateConfRequest, AckResponse, ConfigReloaderService);

private:
    base::EndPoint _local_addr;
};

/**
 * BvarMonitorStub
 */
/*
class BvarMonitorStub {
public:
    BvarMonitorStub(const base::EndPoint& local_addr)
        : _local_addr(local_addr) {}
    BvarMonitorStub() {}
    virtual ~BvarMonitorStub() {}

    DEFINE_RPC_STUB(get_bvar, \
            GetBvarRequest, GetBvarResponse, BvarMonitorService);
private:
    base::EndPoint _local_addr;
};
*/
}
