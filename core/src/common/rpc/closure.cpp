/**
 * MOCHOW - A Vector Database System.
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON> (<EMAIL>)
 * Date: 2023/10/17
 */

#include <base/logging.h>
#include <bthread.h>
#include "core/src/common/rpc/closure.h"

namespace mochow::common {

static void* run_closure(void* arg) {
    ::google::protobuf::Closure *closure = static_cast< ::google::protobuf::Closure*>(arg);
    if (closure) {
        closure->Run();
    }
    return NULL;
}

void run_closure_in_bthread(google::protobuf::Closure* closure,
                            bool in_pthread) {
    assert(closure != NULL);
    bthread_t tid;
    bthread_attr_t attr = (in_pthread) 
                          ? BTHREAD_ATTR_PTHREAD : BTHREAD_ATTR_NORMAL;
    int ret = bthread_start_background(&tid, &attr, run_closure, closure);
    if (0 != ret) {
        LOG(ERROR) << "Fail to start bthread to run closure";
        return closure->Run();
    }
}

void run_closure_in_bthread_nosig(google::protobuf::Closure* closure,
                                  bool in_pthread) {
    assert(closure != NULL);
    bthread_t tid;
    bthread_attr_t attr = (in_pthread) 
                          ? BTHREAD_ATTR_PTHREAD : BTHREAD_ATTR_NORMAL;
    attr =  attr | BTHREAD_NOSIGNAL;
    int ret = bthread_start_background(&tid, &attr, run_closure, closure);
    if (0 != ret) {
        LOG(ERROR) << "Fail to start bthread to run closure without signal";
        return closure->Run();
    }
}

}
