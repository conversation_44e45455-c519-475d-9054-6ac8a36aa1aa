/**
 * MOCHOW - A Vector Database System.
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON> (<EMAIL>)
 * Date: 2023/09/18
 * Desciption: Implementations for RPC Call
 *
 */

#include <base/iobuf.h>
#include <bvar/bvar.h>
#include <baidu/rpc/reloadable_flags.h>
#include <baidu/rpc/controller.h>
#include <baidu/rpc/channel.h>
#include <bthread_unstable.h>
#include "core/src/common/rpc/rpc_call.h"
#include "core/src/common/rpc/closure.h"

namespace baidu {
namespace rpc {
DECLARE_uint64(max_body_size);
}
}

static __attribute__((constructor)) void set_max_body_size() {
    baidu::rpc::FLAGS_max_body_size = 100 * 1024 * 1024;
}

namespace mochow::common {

DEFINE_int64(call_timeout_ms, 15000, "rpc call timeout in ms");
BAIDU_RPC_VALIDATE_GFLAG(call_timeout_ms, baidu::rpc::PositiveInteger);
DEFINE_int64(connect_timeout_ms, 3000, "rpc call connect timeout in ms");
BAIDU_RPC_VALIDATE_GFLAG(connect_timeout_ms, baidu::rpc::PositiveInteger);
DEFINE_int64(retry_interval_ms, 1500, "rpc call retry interval in ms");
BAIDU_RPC_VALIDATE_GFLAG(retry_interval_ms, baidu::rpc::PositiveInteger);

RpcCallBase::RpcCallBase(const base::EndPoint local_addr,
                         const base::EndPoint& remote_addr,
                         const gpb::Message* request,
                         gpb::Message* response,
                         gpb::Closure* done,
                         const std::string& desc,
                         const RpcCallOptions* options,
                         base::IOBuf* request_attachment,
                         base::IOBuf* response_attachment,
                         const baidu::rpc::Channel *channel)
    : _local_addr(local_addr)
    , _remote_addr(remote_addr)
    , _request(request)
    , _response(response)
    , _request_attachment(request_attachment)
    , _response_attachment(response_attachment)
    , _channel(const_cast<baidu::rpc::Channel*>(channel))
    , _done(done)
    , _call_begin_us(base::monotonic_time_us())
    , _retry_times(0)
    , _should_reset_controller(false)  // _cntl of the first call is not required to reset
    , _desc(desc)
    , _is_timeout(false)
    , _last_send_timestamp(0)
{
    if (options) {
        _options = *options;
    }
    if (_options.log_id == LOG_ID_NOT_SET) {
        _options.log_id = base::fast_rand();
    }
}

RpcCallBase::~RpcCallBase() {}

#define MOCHOW_RPC_CALL_TRACE_REQUEST() \
    LOG(TRACE) << "[log_id:" << _options.log_id << "]" \
            << " [rpc:" << _desc << "]" \
            << " send " << _request->GetTypeName()    \
            << " to " << _remote_addr   \
            << " retry:" << _retry_times

#define MOCHOW_RPC_CALL_TRACE_RESPONSE(SUCC_OR_FAILED, STATUS) \
    LOG(TRACE) << "[log_id:" << _options.log_id << "]" \
            << " [rpc:" << _desc << "]" \
            << " recv " << _response->GetTypeName()   \
            << " from " << _remote_addr \
            << " retry:" << _retry_times   \
            << " this_time_us:" << this_duration_us \
            << " total_time_us:" << duration_us  \
            << " " << SUCC_OR_FAILED << ":"    \
            << (STATUS).error_code() << " (" << (STATUS).error_str() << ")"

#define MOCHOW_RPC_CALL_TRACE_RESPONSE_WITH_RETRY(SUCC_OR_FAILED, STATUS) \
    LOG(TRACE) << "[log_id:" << _options.log_id << "]" \
            << " [rpc:" << _desc << "]" \
            << " recv " << _response->GetTypeName()   \
            << " from " << _remote_addr \
            << " retry:" << _retry_times   \
            << " this_time_us:" << this_duration_us \
            << " total_time_us:" << duration_us  \
            << " need_retry:" << _options.need_retry \
            << " retry_ms:" << delay_ms    \
            << " " << SUCC_OR_FAILED << ":"    \
            << (STATUS).error_code() << " (" << (STATUS).error_str() << ")"

#define MOCHOW_RPC_CALL_PRINT_FINAL_STATUS(level, SUCC_OR_FAILED_OR_TIMEOUT) \
    LOG(level) << "[log_id:" << _options.log_id << "]" \
            << " [rpc:" << _desc << "]" \
            << " rpc " << SUCC_OR_FAILED_OR_TIMEOUT \
            << ": " << _request->GetTypeName() \
            << " to " << _remote_addr \
            << " retry:" << _retry_times \
            << " total_time_us:" << duration_us

void RpcCallBase::call() {
    if (_should_reset_controller) {
        _cntl.Reset();
    } else {
        // Retires should reset _cntl
        _should_reset_controller = true;
    }
    _cntl.set_log_id(log_id());
    if (_request_attachment != NULL) {
        _cntl.request_attachment() = *_request_attachment;
    }
    _last_send_timestamp = base::monotonic_time_us();
    const int64_t duration_us = base::monotonic_time_us() - _call_begin_us;
    if (duration_us >= _options.call_timeout_ms * 1000L) {
        _is_timeout = true;
        _cntl.SetFailed(ETIMEDOUT, "Rpc call timeout");
        return run_closure_in_bthread(this);
    }
    baidu::rpc::ChannelOptions options;
    options.connection_type = baidu::rpc::CONNECTION_TYPE_SINGLE;
    options.max_retry = 0;
    options.connect_timeout_ms = _options.connect_timeout_ms;
    options.timeout_ms = _options.call_timeout_ms - duration_us / 1000;
    baidu::rpc::Channel *channel = _channel;
    std::unique_ptr<baidu::rpc::Channel> channel_guard;
    if (_channel == NULL) {
        channel = new baidu::rpc::Channel;
        channel_guard.reset(channel);
        if (0 != channel->Init(_remote_addr, &options)) {
            _cntl.SetFailed(EAGAIN, "Channel init failed");
            return run_closure_in_bthread(this);
        }
    }

    MOCHOW_RPC_CALL_TRACE_REQUEST();
    call_method(channel, &_cntl, _request, _response, this);
}

int64_t RpcCallBase::calc_delay_ms(int64_t duration_us) {
    int64_t duration_ms = duration_us / 1000;
    int64_t delay_ms = std::min(
            (int64_t)_retry_times * _options.retry_interval_ms,
            ((int64_t)_options.call_timeout_ms / 2));
    delay_ms = std::min(delay_ms, (int64_t)_options.call_timeout_ms - duration_ms);

    // retry interval should not be less than 100ms
    if (delay_ms <= 100) {
        delay_ms = 100;
    }

    return delay_ms;
}

void RpcCallBase::Run() {
    if (_channel != NULL) {
        _remote_addr = _cntl.remote_side();
    }
    bool is_log_printed = false;
    int64_t delay_ms = 0;
    const int64_t duration_us = base::monotonic_time_us() - _call_begin_us;
    const int64_t this_duration_us = base::monotonic_time_us() - _last_send_timestamp;
    base::Status st;
    if (_cntl.Failed()) {
        st.set_error(_cntl.ErrorCode(), _cntl.ErrorText());
    } else {
        get_status(_response, &st);
    }

    // Success
    if (st.ok()) {
        if (_response_attachment != NULL) {
            _response_attachment->swap(_cntl.response_attachment());
        } else {
            LOG_IF(ERROR, !_cntl.response_attachment().empty()) 
                << "recv unexpected response_attachment through "
                << _response->GetTypeName() << " from " << _remote_addr
                << " log_id:" << log_id();
        }
        MOCHOW_RPC_CALL_TRACE_RESPONSE("succeeded", st);
        if (_options.print_trace_log_on_final_success) {
            MOCHOW_RPC_CALL_PRINT_FINAL_STATUS(TRACE, "succeeded");
        } else {
            MOCHOW_RPC_CALL_PRINT_FINAL_STATUS(NOTICE, "succeeded");
        }
        goto END_RPC;
    }

    // Timedout
    if (duration_us >= _options.call_timeout_ms * 1000 || _is_timeout) {
        // Overwrite errors
        _response->Clear();
        fill_error(_response, ERR_TIMEOUT, _cntl.Failed() ? _cntl.ErrorText() : "Rpc ack timeout");
        base::Status st2;
        get_status(_response, &st2);
        MOCHOW_RPC_CALL_PRINT_FINAL_STATUS(NOTICE, "timeout");
        goto END_RPC;
    }

    // The socket is overcrowded
    if (st.error_code() == baidu::rpc::EOVERCROWDED) {
        ++_retry_times;
        delay_ms = calc_delay_ms(duration_us);
        MOCHOW_RPC_CALL_TRACE_RESPONSE_WITH_RETRY("failed", st);
        is_log_printed = true;
        goto RETRY_RPC;
    }

    if (!_options.need_retry || !_options.will_retry(st.error_code())) {
        base::Status st2;
        get_status(_response, &st2);
        if (st2.ok()) {
            _response->Clear();
            // handle hostunreach condition
            if (_options.is_remote_service_err(st.error_code())) {
                fill_error(_response, ERR_REMOTE_SERVICE_UNREACH, st.error_str());
            } else {
                fill_error(_response, ERR_FAIL, st.error_str());
            }
            get_status(_response, &st2);
        }
        MOCHOW_RPC_CALL_TRACE_RESPONSE("failed", st2) << ""
                << " need_retry:" << _options.need_retry;
        MOCHOW_RPC_CALL_PRINT_FINAL_STATUS(NOTICE, "failed") << ""
                << " error:"
                << st2.error_code() << " (" << st2.error_str() << ")";
        goto END_RPC;
    }

    // Normal retry
    ++_retry_times;
    delay_ms = calc_delay_ms(duration_us);
    if (!is_log_printed) {
        MOCHOW_RPC_CALL_TRACE_RESPONSE_WITH_RETRY("failed", st);
    }

RETRY_RPC:
    _response->Clear();
    if (delay_ms == 0) {
        return call();
    }
    bthread_timer_t timer;
    if (bthread_timer_add(&timer,
                          base::milliseconds_from_now(delay_ms),
                          on_timer, this) != 0) {
        LOG(ERROR) << "Fail to add bthread timer for rpc call";
        return on_timer(this);
    }
    return;

END_RPC:
    _done->Run();
    _done = NULL;
    delete this;
}

void RpcCallBase::on_timer(void* arg) {
    bthread_t tid;
    int ret = bthread_start_background(&tid, NULL, handle_timer, arg);
    if (0 != ret) {
        LOG(ERROR) << "Fail to start bthread for rpc call timer";
        handle_timer(arg);
    }
}

void* RpcCallBase::handle_timer(void* arg) {
    RpcCallBase* rpc_call = static_cast<RpcCallBase*>(arg);
    rpc_call->call();
    return NULL;
}

}
