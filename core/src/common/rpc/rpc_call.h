/**
 * MOCHOW - A Vector Database System.
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON> (<EMAIL>)
 * Date: 2023/09/18
 * Desciption: Definitions for RPC Call
 *
 */

#pragma once

#include <vector>
#include <gflags/gflags.h>
#include <base/macros.h>
#include <base/fast_rand.h>
#include <baidu/rpc/errno.pb.h>
#include <baidu/rpc/channel.h>
#include <baidu/rpc/controller.h>
#include "core/src/common/flags.h"
#include "core/src/common/endpoint.h"
#include "core/src/common/rpc/closure.h"
#include "baidu/vdb/mochow/core/src/proto/error_code.pb.h"

namespace gpb = ::google::protobuf;

namespace google {
namespace protobuf {
class RpcChannel;
class RpcController;
}  // namespace protobuf
}  // namespace google

namespace base {
class IOBuf;
};

namespace baidu {
namespace rpc {
DECLARE_int64(socket_max_unwritten_bytes);
}  // namespace rpc
}  // namespace baidu

namespace mochow::common {

DECLARE_int64(call_timeout_ms);
DECLARE_int64(connect_timeout_ms);
DECLARE_int64(retry_interval_ms);

constexpr uint64_t LOG_ID_NOT_SET = UINT64_MAX;

struct RpcCallOptions {
    uint64_t log_id;
    int64_t call_timeout_ms;
    int64_t connect_timeout_ms;
    int64_t retry_interval_ms;
    bool    need_retry;
    bool    hostdown_retry;
    bool    redirect;
    bool    print_trace_log_on_final_success;

    RpcCallOptions(uint64_t log_id_ = LOG_ID_NOT_SET)
        : log_id(log_id_)
        , call_timeout_ms(FLAGS_call_timeout_ms)
        , connect_timeout_ms(FLAGS_connect_timeout_ms)
        , retry_interval_ms(FLAGS_retry_interval_ms)
        , need_retry(true)
        , hostdown_retry(true)
        , redirect(false)
        , print_trace_log_on_final_success(false) {}

    bool will_retry(int errcode) const {
        if (!need_retry) {
            return false;
        }
        // check hostdown not retry
        if (!hostdown_retry && (errcode == EHOSTDOWN || errcode == ECONNREFUSED)) {
            return false;
        }
        /*network err [EADDRINUSE, EHOSTUNREACH], include ETIMEDOUT, will retry*/
        return (errcode == baidu::rpc::EFAILEDSOCKET ||
                 errcode == baidu::rpc::ELIMIT ||
                 errcode == baidu::rpc::ELOGOFF ||
                 errcode == baidu::rpc::ERPCTIMEDOUT ||
                 errcode == EINPROGRESS ||
                 errcode == ETIMEDOUT ||
                 errcode == EAGAIN ||
                 errcode == EBUSY ||
                 (errcode >= baidu::rpc::SYS_EADDRINUSE &&
                  errcode <= baidu::rpc::SYS_EHOSTUNREACH) ||
                 errcode == ERR_INPROGRESS ||
                 errcode == ERR_BUSY ||
                 errcode == ERR_EXCEED_LIMIT ||
                 errcode == ERR_REDIRECT ||
                 errcode == ERR_TIMEOUT);
    }

    bool is_remote_service_err(int errcode) const {
        return errcode >= baidu::rpc::SYS_EADDRINUSE &&
                  errcode <= baidu::rpc::SYS_EHOSTUNREACH;
    }
};

class RpcCallBase : public gpb::Closure {
DISALLOW_COPY_AND_ASSIGN(RpcCallBase);
public:
    // Invoked when a single RPC returned
    void Run() override;
    // Issue the specific rpc call
    void call();
protected:

    // Issue a specific rpc
    virtual void call_method(::gpb::RpcChannel* channel,
                             ::gpb::RpcController* cntl,
                             const gpb::Message* request,
                             gpb::Message* response,
                             gpb::Closure* done) = 0;

    // Get status from the type specific response
    virtual void get_status(const gpb::Message* response, base::Status* st) = 0;

    //Fills error to response
    virtual void fill_error(gpb::Message* response,
                            int error_code, const std::string& error_text) = 0;

    int64_t calc_delay_ms(int64_t duration_us);

    base::EndPoint local_addr() const { return _local_addr; }
    uint64_t log_id() const { return _options.log_id; }

    RpcCallBase(const base::EndPoint local_addr,
                const base::EndPoint& remote_addr,
                const gpb::Message* request,
                gpb::Message* response,
                gpb::Closure* done,
                const std::string& desc,
                const RpcCallOptions* options,
                base::IOBuf* request_attachment,
                base::IOBuf* response_attachment,
                const baidu::rpc::Channel* channel);
    virtual ~RpcCallBase();

private:

    static void on_timer(void* arg);
    static void* handle_timer(void* arg);

    base::EndPoint  _local_addr;
    base::EndPoint  _remote_addr;
    const gpb::Message* _request; // user owned request
    gpb::Message*   _response;      // user owned response
    base::IOBuf*    _request_attachment; // user owned request attachment
    base::IOBuf*    _response_attachment; // user owned response attachment
    baidu::rpc::Channel* _channel; // user owned channel
    gpb::Closure*   _done; // user define done closure

    int64_t         _call_begin_us;
    int32_t         _retry_times;
    bool            _should_reset_controller;
    std::string     _desc;
    RpcCallOptions  _options;
    baidu::rpc::Controller _cntl;
    bool            _is_timeout;
    int64_t         _last_send_timestamp;
};

template <typename Req, typename Res, typename S>
void issue_rpc(const base::EndPoint local_addr, const base::EndPoint remote_addr,
               void (S::*call_method)(gpb::RpcController*, const Req*, Res*, gpb::Closure*),
               Req* request, Res* response, gpb::Closure* done,
               const std::string& desc, const RpcCallOptions* options,
               base::IOBuf* request_attachment = NULL,
               base::IOBuf* response_attachment = NULL,
               const baidu::rpc::Channel* channel = NULL);

template <typename Request, typename Response, typename Stub>
class RpcCall: public RpcCallBase {

    typedef void (Stub::*Call)(gpb::RpcController*, const Request*, Response*, gpb::Closure*);

friend void issue_rpc<Request, Response, Stub>(
        const base::EndPoint, const base::EndPoint, Call, Request*, Response*, gpb::Closure*,
        const std::string&, const RpcCallOptions*, base::IOBuf*, base::IOBuf*,
        const baidu::rpc::Channel*);
public:

protected:

    void call_method(gpb::RpcChannel* channel,
                     gpb::RpcController* cntl,
                     const gpb::Message* request,
                     gpb::Message* response,
                     gpb::Closure* done) override {
        Stub stub(channel);
        (stub.*_call_method)(cntl,
                           static_cast<const Request*>(request),
                           static_cast<Response*>(response),
                           done);
    }

    void get_status(const gpb::Message* response, base::Status* st) override {
        const Response* res = static_cast<const Response*>(response);
        if (res->status().code() == 0) {
            *st = base::Status::OK();
            return;
        }
        st->set_error(res->status().code(), res->status().msg());
    }

    void fill_error(gpb::Message* response,
                    int error_code, const std::string& error_text) override {
        Response* res = static_cast<Response*>(response);
        res->mutable_status()->set_code(error_code);
        res->mutable_status()->set_msg(error_text);
    }

    ~RpcCall() {}

private:
    RpcCall(const base::EndPoint local_addr,
            const base::EndPoint remote_addr,
            Call call_method,
            Request* request,
            Response* response,
            gpb::Closure* done,
            const std::string& desc,
            const RpcCallOptions* options,
            base::IOBuf* request_attachment = NULL,
            base::IOBuf* response_attachment = NULL,
            const baidu::rpc::Channel* channel = NULL)
        : RpcCallBase(local_addr, remote_addr, request, response,
                      done, desc, options, request_attachment, response_attachment,
                      channel)
        , _call_method(call_method) {
        //endpoint2node(request->mutable_req_id()->mutable_node(), local_addr);
        //request->mutable_req_id()->set_log_id(log_id());
    }

    Call _call_method;
};

// Function to help use RpcCall easier
template <typename Req, typename Res, typename S>
void issue_rpc(const base::EndPoint local_addr, const base::EndPoint remote_addr,
               void (S::*call_method)(gpb::RpcController*, const Req*, Res*, gpb::Closure*),
               Req* request, Res* response, gpb::Closure* done,
               const std::string& desc, const RpcCallOptions* options,
               base::IOBuf* request_attachment,
               base::IOBuf* response_attachment,
               const baidu::rpc::Channel* channel) {
    if (request->token().empty()) { request->set_token(FLAGS_token); }
    auto call = new RpcCall<Req, Res, S>(
            local_addr, remote_addr, call_method, request, response, done,
            desc, options, request_attachment, response_attachment,
            channel);
    return call->call();
}

}
