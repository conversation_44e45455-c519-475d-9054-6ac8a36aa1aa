/**
 * MOCHOW - A Vector Database System.
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON> (<PERSON><PERSON><PERSON><PERSON>@baidu.com)
 * Date: 2024/04/26
 * Desciption: Declarations for system variable reader.
 *
 */

#pragma once

#include <unistd.h>                        // getpagesize
#include <sys/types.h>
#include <sys/resource.h>                  // getrusage
#include <dirent.h>                        // dirent
#include <iomanip>                         // setw

#include <functional>
#include <memory>

#include <base/time.h>
#include <base/memory/singleton_on_pthread_once.h>
#include <base/scoped_lock.h>
#include <base/files/scoped_file.h>
#include <base/files/dir_reader_posix.h>
#include <base/file_util.h>

#include "core/src/common/flags.h"

namespace mochow {

// Reduce pressures to functions to get system metrics.
template <typename T>
class CachedReader {
public:
    CachedReader(const std::function<bool(T&)> fn, uint64_t interval_us)
    : _mtime_us(0), _interval_us(interval_us), _fn(std::move(fn)) {
        CHECK_EQ(0, pthread_mutex_init(&_mutex, NULL));
    }
    ~CachedReader() {
        pthread_mutex_destroy(&_mutex);
    }

    bool read(T& value) {
        uint64_t now = base::gettimeofday_us();
        if (now > this->_mtime_us + _interval_us) {
            pthread_mutex_lock(&this->_mutex);
            if (now > this->_mtime_us + _interval_us) {
                this->_mtime_us = now;
                pthread_mutex_unlock(&this->_mutex);
                // don't run fn inside lock otherwise a slow fn may
                // block all concurrent bvar dumppers. (e.g. /vars)
                T result;
                if (_fn(result)) {
                    pthread_mutex_lock(&this->_mutex);
                    this->_cached = result;
                    value = this->_cached;
                    pthread_mutex_unlock(&this->_mutex);
                    return true;
                } else {
                    return false;
                }
            } else {
                pthread_mutex_unlock(&this->_mutex);
            }
        }

        pthread_mutex_lock(&this->_mutex);
        value = this->_cached;
        pthread_mutex_unlock(&this->_mutex);

        return true;
    }

private:
    uint64_t _mtime_us;
    uint64_t _interval_us;
    pthread_mutex_t _mutex;
    T _cached;
    std::function<bool(T&)> _fn;
};

template <typename T>
using CachedReaderPtr = std::unique_ptr<CachedReader<T>>;
template <typename T>
using CachedReaderRef = std::shared_ptr<CachedReader<T>>;

template <typename T>
class SystemMetricReader {
public:
    SystemMetricReader(std::function<bool(T&)> fn, uint64_t interval_us) : _fn(fn) {
        _cached_reader = std::make_unique<CachedReader<T>>(fn, interval_us);
    }
    ~SystemMetricReader() = default;

    bool force_read(T& value) const {
        return _fn(value);
    }

    bool read(T& value) const {
        return _cached_reader->read(value);
    }

private:
    std::function<bool(T&)> _fn;
    CachedReaderPtr<T> _cached_reader;
};

template <typename T>
using SystemMetricReaderPtr = std::unique_ptr<SystemMetricReader<T>>;
template <typename T>
using SystemMetricReaderRef = std::shared_ptr<SystemMetricReader<T>>;

// ======================================

struct ProcStat {
    int pid;
    //std::string comm;
    char state;
    int ppid;
    int pgrp;
    int session;
    int tty_nr;
    int tpgid;
    uint32_t flags;
    uint64_t minflt;
    uint64_t cminflt;
    uint64_t majflt;
    uint64_t cmajflt;
    uint64_t utime;
    uint64_t stime;
    uint64_t cutime;
    uint64_t cstime;
    int64_t priority;
    int64_t nice;
    int64_t num_threads;
};

// Read status from /proc/self/stat. Information from `man proc' is out of date,
// see http://man7.org/linux/man-pages/man5/proc.5.html
inline static bool read_proc_stat(ProcStat& stat);
extern SystemMetricReaderPtr<ProcStat> g_proc_stat_reader;

// ==================================================

struct ProcMemory {
    int64_t size;      // total program size
    int64_t resident;  // resident set size
    int64_t share;     // shared pages
    int64_t trs;       // text (code)
    int64_t lrs;       // library
    int64_t drs;       // data/stack
    int64_t dt;        // dirty pages
};

inline static bool read_proc_memory(ProcMemory& m);
extern SystemMetricReaderPtr<ProcMemory> g_proc_memory_reader;

// ==================================================

struct LoadAverage {
    double loadavg_1m;
    double loadavg_5m;
    double loadavg_15m;
};

inline static bool read_load_average(LoadAverage& m);
extern SystemMetricReaderPtr<LoadAverage> g_load_average_reader;

// ==================================================

struct ProcIO {
    // number of bytes the process read, using any read-like system call (from
    // files, pipes, tty...).
    size_t rchar;

    // number of bytes the process wrote using any write-like system call.
    size_t wchar;

    // number of read-like system call invocations that the process performed.
    size_t syscr;

    // number of write-like system call invocations that the process performed.
    size_t syscw;

    // number of bytes the process directly read from disk.
    size_t read_bytes;

    // number of bytes the process originally dirtied in the page-cache
    // (assuming they will go to disk later).
    size_t write_bytes;

    // number of bytes the process "un-dirtied" - e.g. using an "ftruncate"
    // call that truncated pages from the page-cache.
    size_t cancelled_write_bytes;
};

inline static bool read_proc_io(ProcIO& s);
extern SystemMetricReaderPtr<ProcIO> g_proc_io_reader;

// ==================================================
// Refs:
//   https://www.kernel.org/doc/Documentation/ABI/testing/procfs-diskstats
//   https://www.kernel.org/doc/Documentation/iostats.txt
// 
// The /proc/diskstats file displays the I/O statistics of block devices.
// Each line contains the following 14 fields:
struct DiskStat {
    long long major_number;
    long long minor_mumber;
    char device_name[64];

    // The total number of reads completed successfully.
    long long reads_completed; // wMB/s wKB/s
    
    // Reads and writes which are adjacent to each other may be merged for
    // efficiency.  Thus two 4K reads may become one 8K read before it is
    // ultimately handed to the disk, and so it will be counted (and queued)
    // as only one I/O.  This field lets you know how often this was done.
    long long reads_merged;     // rrqm/s
    
    // The total number of sectors read successfully.
    long long sectors_read;     // rsec/s

    // The total number of milliseconds spent by all reads (as
    // measured from __make_request() to end_that_request_last()).
    long long time_spent_reading_ms;

    // The total number of writes completed successfully.
    long long writes_completed; // rKB/s rMB/s

    // See description of reads_merged
    long long writes_merged;    // wrqm/s

    // The total number of sectors written successfully.
    long long sectors_written;  // wsec/s

    // The total number of milliseconds spent by all writes (as
    // measured from __make_request() to end_that_request_last()).
    long long time_spent_writing_ms;

    // The only field that should go to zero. Incremented as requests are
    // given to appropriate struct request_queue and decremented as they finish.
    long long io_in_progress;

    // This field increases so long as `io_in_progress' is nonzero.
    long long time_spent_io_ms;

    // This field is incremented at each I/O start, I/O completion, I/O
    // merge, or read of these stats by the number of I/Os in progress
    // `io_in_progress' times the number of milliseconds spent doing
    // I/O since the last update of this field.  This can provide an easy
    // measure of both I/O completion time and the backlog that may be
    // accumulating.
    long long weighted_time_spent_io_ms;
};

inline static bool read_disk_stat(DiskStat& s);
extern SystemMetricReaderPtr<DiskStat> g_disk_stat_reader;

}   // namespace mochow
