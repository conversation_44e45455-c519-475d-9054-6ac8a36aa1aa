/**
 * MOCHOW - A Vector Database System.
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON> (ca<PERSON><PERSON>@baidu.com)
 *         <PERSON> (<EMAIL>)
 * Date: 2023/10/11
 */

#include "core/src/common/minmax.h"
#include "core/src/common/partition_type.h"
#include "core/src/schema/schema_cache.h"
#include "core/src/proxy/handler/utils.h"
#include "core/src/proxy/handler/handler.h"

namespace mochow::proxy {

base::Status ListDatabaseHandler::check_params(baidu::rpc::Controller* cntl, OpMsgRequest& request) {
    base::Status st = base::Status::OK();
    uint64_t log_id = cntl->log_id();

    const auto user = g_rbac_manager->get_user(request.username);
    if (user == nullptr) {
        HANDLER_RPC_LOG(WARNING) << "Fail to list database due to privilege denied";
        FILL_ERROR_RESPONSE(UNAUTHORIZED, PRIVILEGE_DENIED, "");
        st.set_error(ERR_PRIVILEGE_DENIED, "Privilege denied");
    }

    return st;
}

bool ListDatabaseHandler::execute(baidu::rpc::Controller* cntl, OpMsgRequest& request,
        ::google::protobuf::Closure* done, std::ostringstream& oss) {
    baidu::rpc::ClosureGuard done_guard(done);
    uint64_t log_id = cntl->log_id();
    master::pb::ListDatabaseRequest list_db_request;
    master::pb::ListDatabaseResponse list_db_response;
    list_db_request.mutable_auth_info()->set_username(request.username);
    list_db_request.mutable_auth_info()->set_password(request.password);

    base::EndPoint master_addr = g_master_tracker->get_master();
    common::RpcCallOptions options;
    options.log_id = log_id;
    REDIRECT_MASTER_RPC_CALL(list_database, master_addr, &list_db_request, &list_db_response, &options);
    int code = list_db_response.status().code();
    if (code != OK) {
        HANDLER_RPC_LOG(WARNING) << "List database failed,"
            << " rpc_status:" << common::pb2json(list_db_response.status());
        if (code == ERR_AUTHENTICATE_FAILED) {
            FILL_ERROR_RESPONSE_AND_OSS(UNAUTHORIZED, AUTHENTICATE_FAILED, "");
        } else if (code == ERR_PRIVILEGE_DENIED) {
            FILL_ERROR_RESPONSE_AND_OSS(UNAUTHORIZED, PRIVILEGE_DENIED, "");
        } else {
            FILL_ERROR_RESPONSE_AND_OSS(INTERNAL_ERROR, INTERNAL_ERROR, "");
        }
        return false;
    }

    // fill response
    JsonDoc j;
    j.SetObject();
    auto& alloc = j.GetAllocator();
    JsonNode db(sonic_json::kArray);
    for (int i = 0; i < list_db_response.db_infos_size(); ++i) {
        db.PushBack(JsonNode(list_db_response.db_infos(i).db_name(), alloc), alloc);
    }
    j.AddMember(S_PARAMETER_DATABASES, std::move(db), alloc);

    j.AddMember(S_PARAMETER_CODE, JsonNode(OK), alloc);
    j.AddMember(S_PARAMETER_MSG, JsonNode(S_ERROR_CODE_SUCCESS, alloc), alloc);

    sonic_json::WriteBuffer wb;
    j.Serialize(wb);
    cntl->response_attachment().append(wb.ToString());
    oss << "\tstatus=" << cntl->http_response().status_code();
    return true;
}

}
