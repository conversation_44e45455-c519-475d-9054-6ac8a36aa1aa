/**
 * MOCHOW - A Vector Database System.
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON> (ca<PERSON><PERSON>@baidu.com)
 *         <PERSON> (<EMAIL>)
 * Date: 2023/10/11
 */

#include "core/src/common/minmax.h"
#include "core/src/common/partition_type.h"
#include "core/src/schema/schema_cache.h"
#include "core/src/proxy/handler/utils.h"
#include "core/src/proxy/handler/handler.h"

namespace mochow::proxy {

base::Status ListTableHandler::check_params(baidu::rpc::Controller* cntl, OpMsgRequest& request) {
    base::Status st = base::Status::OK();
    uint64_t log_id = cntl->log_id();

    if (request.database.empty()) {
        HANDLER_RPC_LOG(WARNING) << "Fail to list table, database name is empty";
        FILL_ERROR_RESPONSE(INVALID_HTTP_REQUEST, INVALID_PARAMETER, "parameter 'database' required");
        st.set_error(ERR_INVALID_DB_NAME, "Invalid params 'database'");
        return st;
    }

    const auto user = g_rbac_manager->get_user(request.username);
    if (user == nullptr) {
        HANDLER_RPC_LOG(WARNING) << "Fail to list table due to privilege denied";
        FILL_ERROR_RESPONSE(UNAUTHORIZED, PRIVILEGE_DENIED, "");
        st.set_error(ERR_PRIVILEGE_DENIED, "Privilege denied");
        return st;
    }

    return st;
}

bool ListTableHandler::execute(baidu::rpc::Controller* cntl, OpMsgRequest& request,
        ::google::protobuf::Closure* done, std::ostringstream& oss) {
    baidu::rpc::ClosureGuard done_guard(done);
    uint64_t log_id = cntl->log_id();
    master::pb::ListTableRequest list_table_request;
    master::pb::ListTableResponse list_table_response;
    list_table_request.mutable_auth_info()->set_username(request.username);
    list_table_request.mutable_auth_info()->set_password(request.password);
    list_table_request.set_db_name(request.database);

    base::EndPoint master_addr = g_master_tracker->get_master();
    common::RpcCallOptions options;
    options.log_id = log_id;
    REDIRECT_MASTER_RPC_CALL(list_table, master_addr, &list_table_request, &list_table_response, &options);
    int code = list_table_response.status().code();
    if (code != OK) {
        HANDLER_RPC_LOG(WARNING) << "Fail to list table, database:" << request.database
            << " table:" << request.table
            << " request_id:" << request.id
            << " rpc_status:" << common::pb2json(list_table_response.status());
        if (code == ERR_AUTHENTICATE_FAILED) {
            FILL_ERROR_RESPONSE_AND_OSS(UNAUTHORIZED, AUTHENTICATE_FAILED, "");
        } else if (code == ERR_PRIVILEGE_DENIED) {
            FILL_ERROR_RESPONSE_AND_OSS(UNAUTHORIZED, PRIVILEGE_DENIED, "");
        } else if (code == ERR_DB_NOT_EXIST) {
            FILL_ERROR_RESPONSE_AND_OSS(RESOURCE_NOT_FOUND, DB_NOT_EXIST, "");
        } else {
            FILL_ERROR_RESPONSE_AND_OSS(INTERNAL_ERROR, INTERNAL_ERROR, "");
        }
        return false;
    }

    // fill response
    JsonDoc j;
    j.SetObject();
    auto& alloc = j.GetAllocator();
    JsonNode tbl;
    tbl.SetArray();
    for (int i = 0; i < list_table_response.table_infos_size(); ++i) {
        tbl.PushBack(JsonNode(list_table_response.table_infos(i).schema().table_name(), alloc), alloc);
    }
    j.AddMember(S_PARAMETER_TABLES, std::move(tbl), alloc);

    j.AddMember(S_PARAMETER_CODE, JsonNode(OK), alloc);
    j.AddMember(S_PARAMETER_MSG, JsonNode(S_ERROR_CODE_SUCCESS, alloc), alloc);

    sonic_json::WriteBuffer wb;
    j.Serialize(wb);
    cntl->response_attachment().append(wb.ToString());
    oss << "\tstatus=" << cntl->http_response().status_code();
    return true;
}

}
