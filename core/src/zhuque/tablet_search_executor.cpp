/**
 * MOCHOW - A Vector Database System.
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON> (<PERSON><PERSON><PERSON><PERSON>@baidu.com)
 * Date: 2024/06/06
 * Description: Implementations for Tablet Searches
 *
 */

#include "core/src/zhuque/tablet_search_executor.h"
#include "core/src/common/async_executor.h"
#include "core/src/zhuque/search_filter.h"

namespace mochow::zq {

// cherry-pick search is better than knn seach via iterator. set it to 0 to disable knn
// search for now and remove it from code repo later if we determine that we always have
// a better choice.
DEFINE_uint64(pre_filter_search_knn_threshold, 0,
              "Threshold of pre-filter knn search. for dataset that is smaller than 2000 "
              "row count with 768 dimension, knn is better.");

DEFINE_double(zhuque_intra_filter_selectivity_threashold, 0.05,
              "Threshold of intra-filter search. for dataset that selectivity is too "
              "low, pre-filter search is better even if intra-filter is available.");

DEFINE_bool(prefer_scalar_pre_filter_for_vector_search, false,
            "prefer to use scalar pre_filter compared to partial_bitmap pre_filter");

DEFINE_bool(enable_in_memory_pre_filter_search_executor, false,
            "enable in_memory pre_filter search executor");

DEFINE_uint64(max_concurrent_sub_task_num_for_scalar_pre_filter, 1,
              "The max concurrent sub task num which will be executed parallelly for scalar pre_filter");

TabletVectorSearchExecutorRef TabletSearchSelector::get_vector_search_executor(
        const TabletVectorSearchExecutorArgsRef args) {
    uint64_t log_id = args->log_id;
    TRACEID trace_id = args->trace_id;

    // no-filter search
    if (args->filter.is_pass_through()) {
        return std::make_shared<NoFilterVectorSearchExecutor>(args);
    }

    // post-filter search.
    if (args->filter_mode == common::FilterMode::POST) {
        RPC_TRACE_LOG(NOTICE) << "PostFilter is selected due to filter mode is set to POST,"
                              << " table_id:" << args->tablet->get_table_id()
                              << " tp_id:" << args->tablet->get_tp_id();

        if (is_in_memory_filter_available(*args)) {
            RPC_TRACE_LOG(NOTICE) << "[PostFilter] intra-filter is available and start checking BitMapPostFilter,"
                                  << " table_id:" << args->tablet->get_table_id()
                                  << " tp_id:" << args->tablet->get_tp_id();

            // try bitmap
            auto bitmap_indexes = args->tablet->get_available_filtering_bitmap_indexes();
            if (!bitmap_indexes.empty()) {
                // Check if this filter can use bitmap indexes since it should be faster than in-memory pre-filter
                auto filter_tree = args->filter.to_tree();
                LOG_AND_ASSERT(filter_tree != nullptr);
                bool all_column_match_bitmap = false;
                const bool use_bitmap_index = filter_tree->optimizable(bitmap_indexes, &all_column_match_bitmap);
                if (use_bitmap_index && all_column_match_bitmap) {
                    RPC_TRACE_LOG(NOTICE) << "[PostFilter] BitMapPostFilter is selected,"
                                          << " table_id:" << args->tablet->get_table_id()
                                          << " tp_id:" << args->tablet->get_tp_id();

                    // We can use FullMatchBitMapPreFilter in post-filter since it's fast.
                    auto filter = std::make_shared<FullMatchBitMapPreFilter>(args->tablet, std::move(bitmap_indexes), std::move(*filter_tree), args->filter);
                    return std::make_shared<PostFilterVectorSearchExecutor>(std::move(args), std::move(filter));
                }
            }

            RPC_TRACE_LOG(NOTICE) << "[PostFilter] BitMapPostFilter is not available and InMemoryPostFilter is selected,"
                                    << " table_id:" << args->tablet->get_table_id()
                                    << " tp_id:" << args->tablet->get_tp_id();

            auto filter = std::make_shared<InMemoryFilter>(args->tablet, args->schema_hierarchy, args->filter);
            return std::make_shared<PostFilterVectorSearchExecutor>(std::move(args), std::move(filter));
        }

        RPC_TRACE_LOG(NOTICE) << "[PostFilter] ScalarPostFilter is selected,"
                                << " table_id:" << args->tablet->get_table_id()
                                << " tp_id:" << args->tablet->get_tp_id();

        auto filter = std::make_shared<ScalarFilter>(args->tablet, args->schema_hierarchy, args->filter);
        return std::make_shared<PostFilterVectorSearchExecutor>(std::move(args), std::move(filter));
    }

    // intra-filter search
    do {
        if (!is_in_memory_filter_available(*args)) {
            RPC_TRACE_LOG(NOTICE) << "InMemoryFilter is NOT available and choose the fallback plan(ScalarPreFilter),"
                                  << " table_id:" << args->tablet->get_table_id()
                                  << " tp_id:" << args->tablet->get_tp_id();
            break;
        }

        auto vector_index_schema = args->schema_hierarchy->internal_schema->find_vector_index_by_column_name(
                                        args->vector_column_name);

        RPC_TRACE_LOG(NOTICE) << "IntraFilter is available and start checking other conditions,"
                              << " estimator_enabled:" << (FLAGS_zhuque_enable_tablet_search_estimator ? "true" : "false")
                              << " vector_index_exist:" << (vector_index_schema != nullptr ? "true" : "false")
                              << " table_id:" << args->tablet->get_table_id()
                              << " tp_id:" << args->tablet->get_tp_id();

        // Prefer to use intra-filter in following three cases:
        //   1. vector index does not exist and only KNN can be used.
	    //   2. search vector is sparse vector and sparse vector index is built.
        //   3. dense vector index had been created in schema and Filter ratio is acceptable on vector indexes(HNSW, etc)
        //      from the perspective of performance;
        if (vector_index_schema == nullptr ||
            vindex::IndexType::is_valid_sparse_vector_index_type(
                    vector_index_schema->get_index_param().index_type)) {
            RPC_TRACE_LOG(NOTICE) << "IntraFilter is selected";
            auto filter = std::make_shared<InMemoryFilter>(args->tablet, args->schema_hierarchy, args->filter);
            return std::make_shared<IntraFilterVectorSearchExecutor>(std::move(args), std::move(filter));
        }

        float estimated_filter_ratio = std::numeric_limits<float>::max(); // calculated below

        // We have three pre_filter executor now: SCALAR_PRE_FILTER/BITMAP_PRE_FILTER/IN_MEMORY_PRE_FILTER;
        //  1.BITMAP_PRE_FILTER should be faster than IN_MEMORY_PRE_FILTER under all cases;
        //  2.Full matched BITMAP_PRE_FILTER should be best when compared to INTRA_FILTER/SCALAR_PRE_FILTER/IN_MEMORY_PRE_FILTER;
        //  3.BITMAP_PRE_FILTER/IN_MEMORY_PRE_FILTER should be faster than SCALAR_PRE_FILTER at most time
        //    since they can save more IO and key/value decode cost from scalar_engine, but when primary-key range or secondary key range
        //    is provided, it will be better to use scalar pre-filter, that is why FLAGS_prefer_scalar_pre_filter_for_vector_search used;

        auto bitmap_indexes = args->tablet->get_available_filtering_bitmap_indexes();
        if (!bitmap_indexes.empty()) {
            // Check if this filter can use bitmap indexes since it should be faster than in-memory pre-filter
            auto filter_tree = args->filter.to_tree();
            LOG_AND_ASSERT(filter_tree != nullptr);
            bool all_column_match_bitmap = false;
            const bool use_bitmap_index = filter_tree->optimizable(bitmap_indexes, &all_column_match_bitmap);

            // Prefer full-matched BitmapPreFilter than IntraFilter.
            if (use_bitmap_index && all_column_match_bitmap) {
                RPC_TRACE_LOG(NOTICE) << "FullMatchBitmapPreFilter is selected,"
                                      << " table_id:" << args->tablet->get_table_id()
                                      << " tp_id:" << args->tablet->get_tp_id();

                auto filter = std::make_shared<FullMatchBitMapPreFilter>(args->tablet, std::move(bitmap_indexes), std::move(*filter_tree), args->filter);
                return std::make_shared<PreFilterVectorSearchExecutor>(std::move(args), std::move(filter));
            }

            // Partial-matched BitmapPreFilter involves iterating and checking each vid in the bitmap, which
            // is slow. So IntraFilter should be faster than partial-matched BitmapPreFilter.
            if (FLAGS_zhuque_enable_tablet_search_estimator && FLAGS_tablet_row_cache_max_num > 0) {
                estimated_filter_ratio = selectivity(*args);
            }
            if (estimated_filter_ratio > FLAGS_zhuque_intra_filter_selectivity_threashold) {
                RPC_TRACE_LOG(NOTICE) << "IntraFilter is selected, estimated_filter_ratio:" << estimated_filter_ratio;
                auto filter = std::make_shared<InMemoryFilter>(args->tablet, args->schema_hierarchy, args->filter);
                return std::make_shared<IntraFilterVectorSearchExecutor>(std::move(args), std::move(filter));
            }

            if (use_bitmap_index && !FLAGS_prefer_scalar_pre_filter_for_vector_search) {
                RPC_TRACE_LOG(NOTICE) << "PartialMatchBitMapPreFilter is selected,"
                                      << " use_bitmap_index:" << (use_bitmap_index ? "true" : "false")
                                      << " all_column_match_bitmap:" << (all_column_match_bitmap ? "true" : "false")
                                      << " prefer_scalar_pre_filter_flag:" << (FLAGS_prefer_scalar_pre_filter_for_vector_search ? "true" : "false")
                                      << " estimated_filter_ratio:" << estimated_filter_ratio
                                      << " table_id:" << args->tablet->get_table_id()
                                      << " tp_id:" << args->tablet->get_tp_id();

                auto filter = std::make_shared<PartialMatchBitMapPreFilter>(args->tablet, args->schema_hierarchy, std::move(bitmap_indexes), std::move(*filter_tree), args->filter);
                return std::make_shared<PreFilterVectorSearchExecutor>(std::move(args), std::move(filter));
            }
        }

        RPC_TRACE_LOG(NOTICE) << "BitmapPreFilter is not selected and start checking InMemoryPreFilter,"
                              << " table_id:" << args->tablet->get_table_id()
                              << " tp_id:" << args->tablet->get_tp_id()
                              << " estimated_filter_ratio:" << estimated_filter_ratio;

        // in-memory pre filter shows poor performance in online-environments. disable it by default until we found a better way to 
        // speed up.
        if (!FLAGS_enable_in_memory_pre_filter_search_executor) {
            RPC_TRACE_LOG(NOTICE) << "Skip InMemoryPreFilter and fallback to ScalarPreFilter,"
                                  << " FLAGS_enable_in_memory_pre_filter_search_executor:" << (FLAGS_enable_in_memory_pre_filter_search_executor ? "true" : "false")
                                  << " table_id:" << args->tablet->get_table_id()
                                  << " tp_id:" << args->tablet->get_tp_id();
            break;
        }

        RPC_TRACE_LOG(NOTICE) << "InMemoryPreFilter is selected,"
                              << " table_id:" << args->tablet->get_table_id()
                              << " tp_id:" << args->tablet->get_tp_id();

        auto filter = std::make_shared<InMemoryPreFilter>(args->tablet, args->schema_hierarchy, args->filter);
        return std::make_shared<PreFilterVectorSearchExecutor>(std::move(args), std::move(filter));

    } while (0);

    // fallback. scalar pre-filter search
    auto filter = std::make_shared<ScalarPreFilter>(args->tablet, args->schema_hierarchy, args->filter, args->timeout_info);
    return std::make_shared<PreFilterVectorSearchExecutor>(std::move(args), std::move(filter));
}

TabletBM25SearchExecutorRef TabletSearchSelector::get_bm25_search_executor(
        const TabletBM25SearchExecutorArgsRef args) {
    uint64_t log_id = args->log_id;
    TRACEID trace_id = args->trace_id;

    // no-filter search
    if (args->filter.is_pass_through()) {
        return std::make_shared<NoFilterBM25SearchExecutor>(std::move(args));
    }

    // intra-filter search
    do {
        if (!is_in_memory_filter_available(*args)) {
            RPC_TRACE_LOG(NOTICE) << "InMemoryFilter is NOT available for BM25 search and choose the fallback plan(ScalarPreFilter),"
                                  << " table_id:" << args->tablet->get_table_id()
                                  << " tp_id:" << args->tablet->get_tp_id();
            break;
        }

        RPC_TRACE_LOG(NOTICE) << "IntraFilter is available for BM25 search and start checking other conditions,"
                              << " estimator_enabled:" << (FLAGS_zhuque_enable_tablet_search_estimator ? "true" : "false")
                              << " table_id:" << args->tablet->get_table_id()
                              << " tp_id:" << args->tablet->get_tp_id();

        float estimated_filter_ratio = std::numeric_limits<float>::max(); // calculated below

        auto bitmap_indexes = args->tablet->get_available_filtering_bitmap_indexes();
        if (!bitmap_indexes.empty()) {
            // Check if this filter can use bitmap indexes since it should be faster than in-memory pre-filter
            auto filter_tree = args->filter.to_tree();
            LOG_AND_ASSERT(filter_tree != nullptr);
            bool all_column_match_bitmap = false;
            const bool use_bitmap_index = filter_tree->optimizable(bitmap_indexes, &all_column_match_bitmap);

            // Prefer full-matched BitmapPreFilter than IntraFilter.
            if (use_bitmap_index && all_column_match_bitmap) {
                RPC_TRACE_LOG(NOTICE) << "FullMatchBitMapPreFilter is selected for BM25 search,"
                                      << " table_id:" << args->tablet->get_table_id()
                                      << " tp_id:" << args->tablet->get_tp_id();

                auto filter = std::make_shared<FullMatchBitMapPreFilter>(args->tablet, std::move(bitmap_indexes), std::move(*filter_tree), args->filter);
                return std::make_shared<PreFilterBM25SearchExecutor>(std::move(args), std::move(filter));
            }

            // For whether to choose IntraFilter, following the same rule with vector search.
            if (FLAGS_zhuque_enable_tablet_search_estimator && FLAGS_tablet_row_cache_max_num > 0) {
                estimated_filter_ratio = selectivity(*args);
            }
            if (estimated_filter_ratio > FLAGS_zhuque_intra_filter_selectivity_threashold) {
                RPC_TRACE_LOG(NOTICE) << "IntraFilter is selected for BM25 search, estimated_filter_ratio:" << estimated_filter_ratio;
                auto filter = std::make_shared<InMemoryFilter>(args->tablet, args->schema_hierarchy, args->filter);
                return std::make_shared<IntraFilterBM25SearchExecutor>(std::move(args), std::move(filter));
            }

            if (use_bitmap_index && !FLAGS_prefer_scalar_pre_filter_for_vector_search) {
                RPC_TRACE_LOG(NOTICE) << "PartialMatchBitMapPreFilter is selected for BM25 search,"
                                      << " use_bitmap_index:" << (use_bitmap_index ? "true" : "false")
                                      << " all_column_match_bitmap:" << (all_column_match_bitmap ? "true" : "false")
                                      << " prefer_scalar_pre_filter_flag:" << (FLAGS_prefer_scalar_pre_filter_for_vector_search ? "true" : "false")
                                      << " estimated_filter_ratio:" << estimated_filter_ratio
                                      << " table_id:" << args->tablet->get_table_id()
                                      << " tp_id:" << args->tablet->get_tp_id();

                auto filter = std::make_shared<PartialMatchBitMapPreFilter>(args->tablet, args->schema_hierarchy, std::move(bitmap_indexes), std::move(*filter_tree), args->filter);
                return std::make_shared<PreFilterBM25SearchExecutor>(std::move(args), std::move(filter));
            }
        }

        RPC_TRACE_LOG(NOTICE) << "BitmapPreFilter is not selected for BM25 search and start checking InMemoryPreFilter,"
                              << " table_id:" << args->tablet->get_table_id()
                              << " tp_id:" << args->tablet->get_tp_id()
                              << " estimated_filter_ratio:" << estimated_filter_ratio;

        // in-memory pre filter shows poor performance in online-environments. disable it by default until we found a better way to 
        // speed up.
        if (!FLAGS_enable_in_memory_pre_filter_search_executor) {
            RPC_TRACE_LOG(NOTICE) << "Skip InMemoryPreFilter for BM25 search and fallback to ScalarPreFilter,"
                                  << " FLAGS_enable_in_memory_pre_filter_search_executor:" << (FLAGS_enable_in_memory_pre_filter_search_executor ? "true" : "false")
                                  << " table_id:" << args->tablet->get_table_id()
                                  << " tp_id:" << args->tablet->get_tp_id();
            break;
        }

        RPC_TRACE_LOG(NOTICE) << "InMemoryPreFilter is selected for BM25 search,"
                              << " table_id:" << args->tablet->get_table_id()
                              << " tp_id:" << args->tablet->get_tp_id();

        auto filter = std::make_shared<InMemoryPreFilter>(args->tablet, args->schema_hierarchy, args->filter);
        return std::make_shared<PreFilterBM25SearchExecutor>(std::move(args), std::move(filter));
    } while (0);

    // fallback. scalar pre-filter search
    auto filter = std::make_shared<ScalarPreFilter>(args->tablet, args->schema_hierarchy, args->filter, args->timeout_info);
    return std::make_shared<PreFilterBM25SearchExecutor>(std::move(args), std::move(filter));
}

std::pair<TabletVectorSearchExecutorRef, TabletBM25SearchExecutorRef>
TabletSearchSelector::get_hybrid_search_executor(const TabletHybridSearchExecutorArgsRef args) {
    auto vector_search_args = std::make_shared<TabletVectorSearchExecutorArgs>(
            args->tablet,
            args->schema_hierarchy,
            args->filter,
            args->vector_column_name,
            args->retrieve_vector,
            args->is_system_assigned_key,
            args->filter_mode,
            args->timeout_info,
            args->log_id,
            args->trace_id);

    auto bm25_search_args = std::make_shared<TabletBM25SearchExecutorArgs>(
            args->tablet,
            args->schema_hierarchy,
            args->filter,
            args->timeout_info,
            args->log_id,
            args->trace_id);

    // Step1: get executor for vector search.
    TabletVectorSearchExecutorRef vector_search_executor = get_vector_search_executor(vector_search_args);
    auto vector_filter = vector_search_executor->search_filter();

    // Step2: get executor for BM25 search.
    TabletBM25SearchExecutorRef bm25_search_executor;
    switch (vector_search_executor->type()) {
    case SearchExecutorType::NO_FILTER:
        bm25_search_executor = std::make_shared<NoFilterBM25SearchExecutor>(std::move(bm25_search_args));
        break;
    case SearchExecutorType::PRE_FILTER:
        // BM25_search and vector_search share the same filter.
        bm25_search_executor =
                std::make_shared<PreFilterBM25SearchExecutor>(std::move(bm25_search_args), vector_filter);
        MOCHOW_TRACE_LOG(NOTICE) << "BM25 PRE_FILTER search shares filter with vector search, filter_type:"
                                 << vector_filter->type();
        break;
    case SearchExecutorType::INTRA_FILTER:
        // BM25_search and vector_search share the same filter.
        bm25_search_executor =
                std::make_shared<IntraFilterBM25SearchExecutor>(std::move(bm25_search_args), vector_filter);
        MOCHOW_TRACE_LOG(NOTICE) << "BM25 INTRA_FILTER search shares filter with vector search, filter_type:"
                                 << vector_filter->type();
        break;
    case SearchExecutorType::POST_FILTER:
        // Currently POST_FILTER is not useful for BM25 search.
        bm25_search_executor = get_bm25_search_executor(std::move(bm25_search_args));
        break;
    default:
        LOG_AND_ASSERT_EXPR(false, "Unhandled SearchExecutorType");
    }


    return {std::move(vector_search_executor), std::move(bm25_search_executor)};

}

bool TabletSearchSelector::is_in_memory_filter_available(const TabletSearchExecutorArgs& args) {
    uint64_t log_id = args.log_id;
    TRACEID trace_id = args.trace_id;

    std::map<IDXID, FilteringIndexRef> filtering_indexes = args.tablet->get_available_filtering_indexes();
    std::unordered_set<COLID> indexed_columns{};

    for (const auto& [_, index] : filtering_indexes) {
        for (const auto& colid : index->get_index_key_ids(args.schema_hierarchy)) {
            indexed_columns.insert(colid);
        }
    }

    if (args.tablet->enable_vid_to_allkey_map()) {
        for (const auto& [colid, _] : args.schema_hierarchy->internal_schema->get_primary_keys()) {
            indexed_columns.insert(colid);
        }
    }

    std::unordered_set<COLID> columns = args.filter.columns();
    for (const auto& colid : columns) {
        if (indexed_columns.find(colid) == indexed_columns.end()) {

            RPC_TRACE_LOG(NOTICE) << "IntraFilter not available due to column in filter is not indexed,"
                                  << " table_id:" << args.tablet->get_table_id()
                                  << " tp_id:" << args.tablet->get_tp_id()
                                  << " colid:" << colid;
            // affected column is not indexed
            return false;
        }
    }

    RPC_TRACE_LOG(NOTICE) << "IntraFilter is available,"
                          << " table_id:" << args.tablet->get_table_id()
                          << " tp_id:" << args.tablet->get_tp_id();

    return true;
}

float TabletSearchSelector::selectivity(const TabletSearchExecutorArgs& args) {
    SampleResult result = g_tablet_search_estimator->sample(args.tablet, args.filter);
    return result.rate();
}

void ITabletVectorSearchExecutor::segment_search(
        const SegmentRef segment,
        const schema::SchemaHierarchyRef schema_hierarchy, 
        const IDXID index_id,
        const std::string& vector_column_name,
        const VariantArray& fvec_batch,
        const moss::VectorSearchParam& search_params,
        const zq::FilterRef filter,
        const moss::BitSetFilterRef search_filter,
        bool retrieve_vector,   
        bool is_system_assigned_key,
        bool knn_flag,
        std::vector<zq::VectorSearchResultVector>* results,
        std::shared_ptr<Status> status,
        const zq::TimeoutInfo& timeout_info,
        uint64_t log_id,
        TRACEID trace_id) {
    base::Timer timer;
    timer.start();

    RPC_TRACE_LOG(NOTICE) << "Start searching segment,"
                          << " knn_flag:" << (knn_flag ? "true" : "false")
                          << " index_id:" << index_id
                          << " table_id:" << segment->get_table_id()
                          << " tp_id:" << segment->get_tp_id()
                          << " seg_id:" << segment->get_seg_id();
    if (knn_flag) {
        std::string metric_type = search_params.metric_type;
        if (index_id != IDXID_MAX) {
            auto index_schema = schema_hierarchy->internal_schema->find_vector_index_by_index_id(index_id);
            LOG_AND_ASSERT(index_schema != nullptr);
            metric_type = index_schema->get_index_param().metric_type;
        }

        *status = segment->search_without_vindex(
                    schema_hierarchy,
                    vector_column_name,
                    fvec_batch,
                    search_params,
                    filter,
                    search_filter,
                    metric_type,
                    timeout_info,
                    results);
    } else {
        *status = segment->search(
                    schema_hierarchy,
                    index_id,
                    fvec_batch,
                    search_params,
                    filter,
                    search_filter,
                    retrieve_vector,
                    is_system_assigned_key,
                    timeout_info,
                    results);
    }

    timer.stop();
    RPC_TRACE_LOG(NOTICE) << "Finish searching segment,"
                          << " status:" << status->to_string()
                          << " knn_flag:" << (knn_flag ? "true" : "false")
                          << " index_id:" << index_id
                          << " table_id:" << segment->get_table_id()
                          << " tp_id:" << segment->get_tp_id()
                          << " seg_id:" << segment->get_seg_id()
                          << " cost_us:" << timer.u_elapsed();
}

Status ITabletVectorSearchExecutor::merge_segment_results(
        const size_t fvec_batch_size,
        const uint32_t limit,   
        const std::map<SEGID, std::vector<zq::VectorSearchResultVector>>& segment_results,
        std::vector<zq::VectorSearchResultVector>* results) {

    // merge vector rows for every segment. could be quicker. rewrite when we have time.
    // this is the result for every float vector. priority_queue.top() is the one with largest distance.
    std::vector<VectorSearchResultPriorityHeap> result_queue_vec;
    result_queue_vec.resize(fvec_batch_size);

    for (auto& [segid, segment_result] : segment_results) {
        if (segment_result.size() != fvec_batch_size) {
            return Status(ERR_INVALID_ARGUMENT, "Result batch size does not match input batch size");
        }

        // results for every float vector
        for (size_t i = 0; i < fvec_batch_size; i++) {
            const zq::VectorSearchResultVector& result_vec = segment_result[i];
            // result_vec are from small to large
            for (size_t j = 0; j < result_vec.size(); j++) {
                const zq::VectorSearchResult& result = result_vec[j];
                if (result_queue_vec[i].size() < static_cast<size_t>(limit)) {
                    result_queue_vec[i].emplace(result);
                } else {
                    if (!(result < result_queue_vec[i].top())) {
                        break;
                    }
                    result_queue_vec[i].emplace(result);
                    result_queue_vec[i].pop();
                }
            }
        }
    }

    results->resize(fvec_batch_size);
    for (size_t i = 0; i < fvec_batch_size; i++) {
        size_t result_size = result_queue_vec[i].size();
        (*results)[i].resize(result_size);
        for (size_t j = 0; j < result_size; j++) {
            (*results)[i][result_size-1-j] = std::move(result_queue_vec[i].top());
            result_queue_vec[i].pop();
        }
    }

    return Status();
}

// -----------------------------------------------------------------------------
// executors
// -----------------------------------------------------------------------------

/**
 * Description: ann search without filter.
 * Condition:   if filter is pass through.
 */
Status NoFilterVectorSearchExecutor::search(
        const VariantArray& fvec_batch,
        const moss::VectorSearchParam& search_params,
        std::vector<zq::VectorSearchResultVector>* results) {
    Status st;

    const auto& args = search_args();

    uint64_t log_id = args.log_id;
    TRACEID trace_id = args.trace_id;

    LOG_AND_ASSERT(args.filter.is_pass_through());

    // get arguments for search
    auto filter_ref = std::make_shared<zq::Filter>(args.filter);
    std::map<SEGID, std::shared_ptr<Status>> status_map;
    std::map<SEGID, std::vector<zq::VectorSearchResultVector>> result_map;

    bool knn_flag = true;
    IDXID index_id = IDXID_MAX;
    auto vector_index = args.schema_hierarchy->internal_schema->find_vector_index_by_column_name(args.vector_column_name);
    if (vector_index != nullptr) {
        index_id = vector_index->get_index_id();
        knn_flag = false;
    }

    const int64_t remain_time_us = args.timeout_info.remaining_time_before_timeout_us();
    if (remain_time_us <= 0) {
        RPC_TRACE_LOG(WARNING) << "Fail to search due to timeout";
        return Status(ERR_TIMEOUT);
    }

    // execute func() in parallel
    auto segments = args.tablet->get_segments();
    bthread::CountdownEvent cond(segments.size());
    std::vector<common::AsyncExecutor::ID> search_thread_ids;
    for (const auto& [seg_id, _] : segments) {
        status_map.emplace(seg_id, std::make_shared<Status>());
        result_map.emplace(seg_id, std::vector<zq::VectorSearchResultVector>());
    }

    for (const auto& [seg_id, segment] : segments) {
        auto segment_search_task = [&, seg_id]() {
            ITabletVectorSearchExecutor::segment_search(
                    segment,
                    args.schema_hierarchy,
                    index_id,
                    args.vector_column_name,
                    fvec_batch,
                    search_params,
                    filter_ref,
                    nullptr,
                    args.retrieve_vector,
                    args.is_system_assigned_key,
                    knn_flag,
                    &result_map[seg_id],
                    status_map[seg_id],
                    args.timeout_info,
                    log_id,
                    trace_id);
            cond.signal();
        };

        auto tid = common::AsyncExecutor::add_task(segment_search_task);
        if (!tid.has_value()) {
            RPC_TRACE_LOG(WARNING)
                    << "Use current bthread to run segment_search due to start bthread failed,"
                    << " table_id:" << args.tablet->get_table_id()
                    << " tp_id:" << args.tablet->get_tp_id()
                    << " timeout_ms:" << args.timeout_info.get_timeout_ms()
                    << " search_executor_type:" << type();
            segment_search_task();
        } else {
            search_thread_ids.push_back(*tid);
        }
    }

    int rc = cond.timed_wait(base::microseconds_from_now(remain_time_us));
    if (rc != 0) {
        RPC_TRACE_LOG(WARNING) << "Fail to search due to executor timeout,"
                               << " table_id:" << args.tablet->get_table_id()
                               << " tp_id:" << args.tablet->get_tp_id()
                               << " timeout_ms:" << args.timeout_info.get_timeout_ms()
                               << " search_executor_type:" << type();
        if (rc == ETIMEDOUT) {
            for (auto tid : search_thread_ids) {
                common::AsyncExecutor::stop(tid);
            }
            for (auto tid : search_thread_ids) {
                common::AsyncExecutor::join(tid);
            }
            return Status(ERR_TIMEOUT, "bthread countdown executor timeout");
        }
        return Status(ERR_DB_INTERNAL_ERROR, "bthread countdown executor error");
    }

    for (const auto& [seg_id, seg_st] : status_map) {
        if (!seg_st->ok()) {
            RPC_TRACE_LOG(WARNING) << "Fail to search due to segment search failed,"
                                   << " segment_id:" << seg_id
                                   << " search_result:" << seg_st->to_string()
                                   << " table_id:" << args.tablet->get_table_id()
                                   << " tp_id:" << args.tablet->get_tp_id()
                                   << " search_executor_type:" << type();
            return Status(seg_st->code(), "Segment search error");
        }
    }

    st = merge_segment_results(fvec_batch.size(), search_params.topk, result_map, results);
    if (!st.ok()) {
        RPC_TRACE_LOG(WARNING) << "Fail to search due to merge result failed,"
                               << " table_id:" << args.tablet->get_table_id()
                               << " tp_id:" << args.tablet->get_tp_id()
                               << " search_executor_type:" << type();
        return st;
    }

    return Status();
}

/**
 * Description: ann search with pre-filter.
 * Condition:   most common one. fallback to this in most scenario.
 */

Status PreFilterVectorSearchExecutor::search(const VariantArray& fvec_batch,
                                             const moss::VectorSearchParam& search_params,
                                             std::vector<zq::VectorSearchResultVector>* results) {

    const auto& args = search_args();

    uint64_t log_id = args.log_id;
    TRACEID trace_id = args.trace_id;

    common::BitSetFilterRef search_filter;
    Status status = init_bitset_filter(&search_filter);
    if (!status.ok()) {
        RPC_TRACE_LOG(WARNING) << "Fail to do pre_filter on tablet for vector search,"
                               << " status:" << status.to_string()
                               << " table_id:" << args.tablet->get_table_id()
                               << " tp_id:" << args.tablet->get_tp_id()
                               << " filter_type:" << filter_type()
                               << " search_executor_type:" << type()
                               << " status:" << status;
        return status;
    }

    // if nothing in this filter, like (id > 2 and id < 1), return nothing.
    auto basic_search_filter = std::dynamic_pointer_cast<moss::BasicBitSetFilter>(search_filter);
    if (basic_search_filter != nullptr && basic_search_filter->is_whitelist() && basic_search_filter->count() == 0) {
        RPC_TRACE_LOG(NOTICE) << "Succeed to search since all rows are filtered out by pre_filter,"
                              << " table_id:" << args.tablet->get_table_id()
                              << " tp_id:" << args.tablet->get_tp_id()
                              << " search_executor_type:" << type();
        results->resize(fvec_batch.size());
        return Status();
    }

    // get arguments for search
    auto filter_ref = std::make_shared<zq::Filter>(args.filter);
    bool knn_flag = true;
    IDXID index_id = IDXID_MAX;
    auto vector_index = args.schema_hierarchy->internal_schema->find_vector_index_by_column_name(args.vector_column_name);
    if (vector_index != nullptr) {
        index_id = vector_index->get_index_id();
        knn_flag = false;

        // force knn
        if (vector_index->get_index_param().index_type != vindex::IndexType::INDEX_SPARSE
                && basic_search_filter != nullptr
                && basic_search_filter->is_whitelist()
                && (basic_search_filter->count() * vector_index->get_vector_column()->get_dimension() <
                FLAGS_pre_filter_search_knn_threshold)) {
                knn_flag = true;
        }
    }

    const int64_t remain_time_us = args.timeout_info.remaining_time_before_timeout_us();
    if (remain_time_us <= 0) {
        RPC_TRACE_LOG(WARNING) << "Fail to search due to timeout";
        return Status(ERR_TIMEOUT);
    }

    std::map<SEGID, std::shared_ptr<Status>> status_map;
    std::map<SEGID, std::vector<zq::VectorSearchResultVector>> result_map;
    // execute func() in parallel
    auto segments = args.tablet->get_segments();
    bthread::CountdownEvent cond(segments.size());
    std::vector<common::AsyncExecutor::ID> search_thread_ids;
    for (const auto& [seg_id, _] : segments) {
        status_map.emplace(seg_id, std::make_shared<Status>());
        result_map.emplace(seg_id, std::vector<zq::VectorSearchResultVector>());
    }

    for (const auto& [seg_id, segment] : segments) {
        auto segment_search_task = [&, seg_id]() {
            ITabletVectorSearchExecutor::segment_search(
                segment,
                args.schema_hierarchy,
                index_id,
                args.vector_column_name,
                fvec_batch,
                search_params,
                filter_ref,
                search_filter,
                args.retrieve_vector,
                args.is_system_assigned_key,
                knn_flag,
                &result_map[seg_id],
                status_map[seg_id],
                args.timeout_info,
                log_id,
                trace_id);
            cond.signal();
        };

        auto tid = common::AsyncExecutor::add_task(segment_search_task);
        if (!tid.has_value()) {
            RPC_TRACE_LOG(WARNING)
                    << "Use current bthread to run segment_search due to start bthread failed,"
                    << " table_id:" << args.tablet->get_table_id()
                    << " tp_id:" << args.tablet->get_tp_id() << " timeout_ms:" << args.timeout_info.get_timeout_ms()
                    << " search_executor_type:" << type();
            segment_search_task();
        } else {
            search_thread_ids.push_back(*tid);
        }
    }

    int rc = cond.timed_wait(base::microseconds_from_now(remain_time_us));
    if (rc != 0) {
        RPC_TRACE_LOG(WARNING) << "Fail to search due to executor timeout,"
                               << " rc:" << rc
                               << " table_id:" << args.tablet->get_table_id()
                               << " tp_id:" << args.tablet->get_tp_id()
                               << " timeout_ms:" << args.timeout_info.get_timeout_ms()
                               << " search_executor_type:" << type();
        if (rc == ETIMEDOUT) {
            for (auto tid : search_thread_ids) {
                common::AsyncExecutor::stop(tid);
            }
            for (auto tid : search_thread_ids) {
                common::AsyncExecutor::join(tid);
            }
            return Status(ERR_TIMEOUT, "bthread countdown executor timeout");
        }
        return Status(ERR_DB_INTERNAL_ERROR, "bthread countdown executor error");
    }

    for (const auto& [seg_id, seg_st] : status_map) {
        if (!seg_st->ok()) {
            RPC_TRACE_LOG(WARNING) << "Fail to search due to segment search failed,"
                                   << " segment_id:" << seg_id
                                   << " search_result:" << seg_st->to_string()
                                   << " table_id:" << args.tablet->get_table_id()
                                   << " tp_id:" << args.tablet->get_tp_id()
                                   << " search_executor_type:" << type();
            return Status(seg_st->code(), "Segment search error");
        }
    }

    status = merge_segment_results(fvec_batch.size(), search_params.topk, result_map, results);    
    if (!status.ok()) {
        RPC_TRACE_LOG(WARNING) << "Fail to search due to merge result failed,"
                               << " table_id:" << args.tablet->get_table_id()
                               << " tp_id:" << args.tablet->get_tp_id()
                               << " search_executor_type:" << type();
        return status;
    }

    return Status();        
}

/**
 * Description: ann search with intra-filter, that is, check validity while searching.
 * Condition:   if it's all affected column in filter is indexed by vid-index or filtering index.
 */
Status IntraFilterVectorSearchExecutor::search(
        const VariantArray& fvec_batch,
        const moss::VectorSearchParam& search_params,
        std::vector<zq::VectorSearchResultVector>* results) {
    const auto& args = search_args();

    uint64_t log_id = args.log_id;
    TRACEID trace_id = args.trace_id;

    // Step1: initialize the intra_filter
    common::BitSetFilterRef search_filter;
    Status status = init_bitset_filter(&search_filter);
    if (!status.ok()) {
        RPC_TRACE_LOG(WARNING) << "Fail to init intra filter, type:" << filter_type()
                                  << " status:" << status;
        return status;
    }

    // step 2: get arguments for search
    auto filter_ref = std::make_shared<zq::Filter>(args.filter);
    bool knn_flag = true;
    IDXID index_id = IDXID_MAX;
    auto vector_index_schema = args.schema_hierarchy->internal_schema->find_vector_index_by_column_name(args.vector_column_name);
    if (vector_index_schema != nullptr) {
        index_id = vector_index_schema->get_index_id();
        knn_flag = false;
    }

    const int64_t remain_time_us = args.timeout_info.remaining_time_before_timeout_us();
    if (remain_time_us <= 0) {
        RPC_TRACE_LOG(WARNING) << "Fail to search due to timeout";
        return Status(ERR_TIMEOUT);
    }

    std::map<SEGID, std::shared_ptr<Status>> status_map;
    std::map<SEGID, std::vector<zq::VectorSearchResultVector>> result_map;
    // step 3: search in parallel
    auto segments = args.tablet->get_segments();
    bthread::CountdownEvent cond(segments.size());
    std::vector<common::AsyncExecutor::ID> search_thread_ids;
    for (const auto& [seg_id, _] : segments) {
        status_map.emplace(seg_id, std::make_shared<Status>());
        result_map.emplace(seg_id, std::vector<zq::VectorSearchResultVector>());
    }

    for (const auto& [seg_id, segment] : segments) {
        auto segment_search_task = [&, seg_id]() {
            ITabletVectorSearchExecutor::segment_search(
                    segment,
                    args.schema_hierarchy,
                    index_id,
                    args.vector_column_name,
                    fvec_batch,
                    search_params,
                    filter_ref,
                    search_filter,
                    args.retrieve_vector,
                    args.is_system_assigned_key,
                    knn_flag,
                    &result_map[seg_id],
                    status_map[seg_id],
                    args.timeout_info,
                    args.log_id,
                    args.trace_id);
            cond.signal();
        };

        auto tid = common::AsyncExecutor::add_task(segment_search_task);
        if (!tid.has_value()) {
            RPC_TRACE_LOG(WARNING)
                    << "Use current bthread to run segment_search due to start bthread failed,"
                    << " table_id:" << args.tablet->get_table_id()
                    << " tp_id:" << args.tablet->get_tp_id() << " timeout_ms:" << args.timeout_info.get_timeout_ms()
                    << " search_executor_type:" << type();
            segment_search_task();
        } else {
            search_thread_ids.push_back(*tid);
        }
    }

    int ret = cond.timed_wait(base::microseconds_from_now(remain_time_us));
    if (ret != 0) {
        RPC_TRACE_LOG(WARNING) << "Fail to search due to executor timeout,"
                               << " table_id:" << args.tablet->get_table_id()
                               << " tp_id:" << args.tablet->get_tp_id()
                               << " timeout_ms:" << args.timeout_info.get_timeout_ms()
                               << " search_executor_type:" << type();
        if (ret == ETIMEDOUT) {
            for (auto tid : search_thread_ids) {
                common::AsyncExecutor::stop(tid);
            }
            for (auto tid : search_thread_ids) {
                common::AsyncExecutor::join(tid);
            }
            return Status(ERR_TIMEOUT, "bthread countdown executor timeout");
        }
        return Status(ERR_DB_INTERNAL_ERROR, "bthread countdown executor error");
    }

    for (const auto& [seg_id, seg_st] : status_map) {
        if (!seg_st->ok()) {
            RPC_TRACE_LOG(WARNING) << "Fail to search due to segment search failed,"
                                   << " segment_id:" << seg_id
                                   << " search_result:" << seg_st->to_string()
                                   << " table_id:" << args.tablet->get_table_id()
                                   << " tp_id:" << args.tablet->get_tp_id()
                                   << " search_executor_type:" << type();
            return Status(seg_st->code(), "Segment search error");
        }
    }

    // step 4: merge results
    status = merge_segment_results(fvec_batch.size(), search_params.topk, result_map, results);
    if (!status.ok()) {
        RPC_TRACE_LOG(WARNING) << "Fail to search due to merge result failed,"
                               << " table_id:" << args.tablet->get_table_id()
                               << " tp_id:" << args.tablet->get_tp_id()
                               << " search_executor_type:" << type();
        return status;
    }

    return Status();
}

/**
 * Description: ann search with post-filter, that is, check validity while searching.
 * Condition:   if it's all affected column in filter is indexed by vid-index or filtering index.
 */
Status PostFilterVectorSearchExecutor::search(
        const VariantArray& fvec_batch,
        const moss::VectorSearchParam& search_params,
        std::vector<zq::VectorSearchResultVector>* results) {
    const auto& args = search_args();

    uint64_t log_id = args.log_id;
    TRACEID trace_id = args.trace_id;

    RPC_TRACE_LOG(NOTICE) << "Start to PostFilter vector search,"
                          << " table_id:" << args.tablet->get_table_id()
                          << " tp_id:" << args.tablet->get_tp_id()
                          << " filter_type:" << filter_type();

    LOG_AND_ASSERT(!args.filter.is_pass_through());
    auto columns = args.filter.columns();
    LOG_AND_ASSERT(!columns.empty());

    // step 2: get arguments for search
    zq::Filter pass_through_filter = zq::pass_through_filter();
    auto filter_ref = std::make_shared<zq::Filter>(pass_through_filter);
    bool knn_flag = true;
    IDXID index_id = IDXID_MAX;
    auto vector_index_schema = args.schema_hierarchy->internal_schema->find_vector_index_by_column_name(args.vector_column_name);
    if (vector_index_schema != nullptr) {
        index_id = vector_index_schema->get_index_id();
        knn_flag = false;
    }

    // step 3: search in parallel
    const int64_t remain_time_us = args.timeout_info.remaining_time_before_timeout_us();
    if (remain_time_us <= 0) {
        RPC_TRACE_LOG(WARNING) << "Fail to search due to timeout";
        return Status(ERR_TIMEOUT);
    }

    std::map<SEGID, std::shared_ptr<Status>> status_map;
    std::map<SEGID, std::vector<zq::VectorSearchResultVector>> result_map;
    auto segments = args.tablet->get_segments();

    bthread::CountdownEvent cond(segments.size());
    std::vector<common::AsyncExecutor::ID> search_thread_ids;

    // post filter specific: amplify topk
    moss::VectorSearchParam amplified_search_param = search_params;
    amplified_search_param.topk = uint32_t(amplified_search_param.topk * amplified_search_param.post_filter_amplification_factor);
    for (const auto& [seg_id, _] : segments) {
        status_map.emplace(seg_id, std::make_shared<Status>());
        result_map.emplace(seg_id, std::vector<zq::VectorSearchResultVector>());
    }
    
    for (const auto& [seg_id, segment] : segments) {
        auto segment_search_task = [&, seg_id]() {
            ITabletVectorSearchExecutor::segment_search(
                segment,
                args.schema_hierarchy,
                index_id,
                args.vector_column_name,
                fvec_batch,
                amplified_search_param,
                filter_ref,
                nullptr,
                args.retrieve_vector,
                args.is_system_assigned_key,
                knn_flag,
                &result_map[seg_id],
                status_map[seg_id],
                args.timeout_info,
                args.log_id,
                args.trace_id);
            cond.signal();
        };

        auto tid = common::AsyncExecutor::add_task(segment_search_task);
        if (!tid.has_value()) {
            RPC_TRACE_LOG(WARNING)
                    << "Use current bthread to run segment_search due to start bthread failed,"
                    << " table_id:" << args.tablet->get_table_id()
                    << " tp_id:" << args.tablet->get_tp_id() << " timeout_ms:" << args.timeout_info.get_timeout_ms()
                    << " search_executor_type:" << type();
            segment_search_task();
        } else {
            search_thread_ids.push_back(*tid);
        }
    }

    int ret = cond.timed_wait(base::microseconds_from_now(remain_time_us));
    if (ret != 0) {
        RPC_TRACE_LOG(WARNING) << "Fail to search due to executor timeout,"
                               << " table_id:" << args.tablet->get_table_id()
                               << " tp_id:" << args.tablet->get_tp_id()
                               << " timeout_ms:" << args.timeout_info.get_timeout_ms()
                               << " search_executor_type:" << type();
        if (ret == ETIMEDOUT) {
            for (auto tid : search_thread_ids) {
                common::AsyncExecutor::stop(tid);
            }
            for (auto tid : search_thread_ids) {
                common::AsyncExecutor::join(tid);
            }
            return Status(ERR_TIMEOUT, "bthread countdown executor timeout");
        }
        return Status(ERR_DB_INTERNAL_ERROR, "bthread countdown executor error");
    }

    for (const auto& [seg_id, seg_st] : status_map) {
        if (!seg_st->ok()) {
            RPC_TRACE_LOG(WARNING) << "Fail to search due to segment search failed,"
                                   << " status:" << seg_st->to_string()
                                   << " segment_id:" << seg_id
                                   << " search_result:" << seg_st->to_string()
                                   << " table_id:" << args.tablet->get_table_id()
                                   << " tp_id:" << args.tablet->get_tp_id()
                                   << " search_executor_type:" << type();
            return Status(seg_st->code(), "Segment search error");
        }
    }

    RPC_TRACE_LOG(NOTICE) << "Succeed to search without filter and start post filtering,"
                          << " table_id:" << args.tablet->get_table_id()
                          << " tp_id:" << args.tablet->get_tp_id()
                          << " search_executor_type:" << type();

    // step 4: do post filter, remove all invalid result
    base::Timer do_post_filter_timer;
    do_post_filter_timer.start();

    common::BitSetFilterRef search_filter;
    Status status = init_bitset_filter(&search_filter);
    if (!status.ok()) {
        RPC_TRACE_LOG(WARNING) << "Fail to search due to init post filter failed,"
                               << " status:" << status.to_string()
                               << " table_id:" << args.tablet->get_table_id()
                               << " tp_id:" << args.tablet->get_tp_id()
                               << " timeout_ms:" << args.timeout_info.get_timeout_ms()
                               << " filter_type:" << filter_type();
        return status;
    }

    for (auto& [seg_id, segment_result] : result_map) {
        // result_vec is results for one query vec in one segment
        for (auto& result_vec : segment_result) {
            auto iter = result_vec.begin();
            while (iter != result_vec.end()) {
                const INCID vid = iter->get_incid();
                // skip results that is invalid
                if (vid == INVALID_INCID) {
                    iter++;
                    continue;
                }

                if (search_filter->is_available(vid)) {
                    iter++;
                } else {
                    // erase invalid result
                    iter = result_vec.erase(iter);
                }
            }
        }
    }

    do_post_filter_timer.stop();
    RPC_TRACE_LOG(NOTICE) << "Succeed to do post_filter on tablet for vector search,"
                          << " table_id:" << args.tablet->get_table_id()
                          << " tp_id:" << args.tablet->get_tp_id()
                          << " filter_type:" << filter_type()
                          << " cost_us:" << do_post_filter_timer.u_elapsed();

    // step 5: merge results
    status = merge_segment_results(fvec_batch.size(), search_params.topk, result_map, results);
    if (!status.ok()) {
        RPC_TRACE_LOG(WARNING) << "Fail to search due to merge result failed,"
                               << " status:" << status.to_string()
                               << " table_id:" << args.tablet->get_table_id()
                               << " tp_id:" << args.tablet->get_tp_id()
                               << " filter_type:" << filter_type();
        return status;
    }

    return Status();
}

/****** BM25 search executors ******/

Status NoFilterBM25SearchExecutor::search(
        const zq::BM25SearchParam& search_param,
        size_t limit,
        zq::BM25SearchResultVector* results) {
    const auto& args = search_args();

    LOG_AND_ASSERT(args.filter.is_pass_through());
    Status status = args.tablet->do_bm25_search_from_inverted_indexes(
            args.schema_hierarchy, nullptr, limit, search_param, args.timeout_info, results);
    if (!status.ok()) {
        MOCHOW_TRACE_LOG(WARNING) << "Fail to do BM25 search from inverted indexes on tablet,"
                                  << " table_id:" << args.tablet->get_table_id()
                                  << " tp_id:" << args.tablet->get_tp_id() << " filter_type:" << filter_type()
                                  << " search_executor_type:" << type() << " status:" << status;
        return status;
    }

    return status;
}

SearchExecutorType NoFilterBM25SearchExecutor::type() const {
    return SearchExecutorType::NO_FILTER;
}

Status PreFilterBM25SearchExecutor::search(
        const zq::BM25SearchParam& search_param,
        size_t limit,
        zq::BM25SearchResultVector* results) {
    const auto& args = search_args();
    common::BitSetFilterRef search_filter;
    Status status = init_bitset_filter(&search_filter);
    if (!status.ok()) {
        MOCHOW_TRACE_LOG(WARNING) << "Fail to init filter for BM25 pre_filter search, filter_type:"
                                  << filter_type() << " status:" << status;
        return status;
    }

    // if nothing in this filter, like (id > 2 and id < 1), return nothing.
    auto basic_search_filter = std::dynamic_pointer_cast<moss::BasicBitSetFilter>(search_filter);
    if (basic_search_filter != nullptr && basic_search_filter->is_whitelist() &&
        basic_search_filter->count() == 0) {
        MOCHOW_TRACE_LOG(NOTICE) << "Succeed to search since all rows are filtered out by pre_filter,"
                                 << " table_id:" << args.tablet->get_table_id()
                                 << " tp_id:" << args.tablet->get_tp_id() << " filter_type:" << filter_type()
                                 << " search_executor_type:" << type();
        return Status();
    }

    status = args.tablet->do_bm25_search_from_inverted_indexes(
            args.schema_hierarchy, search_filter, limit, search_param, args.timeout_info, results);
    if (!status.ok()) {
        MOCHOW_TRACE_LOG(WARNING) << "Fail to do BM25 search from inverted indexes on tablet,"
                                  << " table_id:" << args.tablet->get_table_id()
                                  << " tp_id:" << args.tablet->get_tp_id() << " filter_type:" << filter_type()
                                  << " search_executor_type:" << type() << " status:" << status;
        return status;
    }

    return status;
}

SearchExecutorType PreFilterBM25SearchExecutor::type() const {
    return SearchExecutorType::PRE_FILTER;
}

Status IntraFilterBM25SearchExecutor::search(
        const zq::BM25SearchParam& search_param,
        size_t limit,
        zq::BM25SearchResultVector* results) {
    const auto& args = search_args();

    common::BitSetFilterRef search_filter;
    Status status = init_bitset_filter(&search_filter);
    if (!status.ok()) {
        MOCHOW_TRACE_LOG(WARNING) << "Fail to init filter for BM25 intra_filter search, filter_type:"
                                  << filter_type() << " status:" << status;
        return status;
    }

    status = args.tablet->do_bm25_search_from_inverted_indexes(
            args.schema_hierarchy, search_filter, limit, search_param, args.timeout_info, results);
    if (!status.ok()) {
        MOCHOW_TRACE_LOG(WARNING) << "Fail to do BM25 search from inverted indexes on tablet,"
                                  << " table_id:" << args.tablet->get_table_id()
                                  << " tp_id:" << args.tablet->get_tp_id() << " filter_type:" << filter_type()
                                  << " search_executor_type:" << type() << " status:" << status;
        return status;
    }

    return status;
}

SearchExecutorType IntraFilterBM25SearchExecutor::type() const {
    return SearchExecutorType::INTRA_FILTER;
}

} // namespace mochow::zq
