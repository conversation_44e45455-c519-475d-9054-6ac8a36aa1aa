// Copyright (c) 2017, Baidu.com, Inc. All Rights Reserved

// Licensed to the Apache Software Foundation (ASF) under one
// or more contributor license agreements.  See the NOTICE file
// distributed with this work for additional information
// regarding copyright ownership.  The ASF licenses this file
// to you under the Apache License, Version 2.0 (the
// "License"); you may not use this file except in compliance
// with the License.  You may obtain a copy of the License at
//
//   http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing,
// software distributed under the License is distributed on an
// "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
// KIND, either express or implied.  See the License for the
// specific language governing permissions and limitations
// under the License.

#pragma once

#include <string>
#include <sstream>
#include <cstdint>
#include <limits>
#include <tuple>
#include <memory>
#include <type_traits>
#include <functional>

#include "core/src/common/using.h"
#include "core/src/types/data_type.h"
#include "core/src/common/slice.h"

namespace mochow::scalar {
namespace codec {

// All of the integer types are different, i.e. you can safely overload
// functions for all of them and you won't get any conflict. However, some times
// use the same number of bits for their representation. Even if they use the
// same number of bits signed and unsigned types always have a different range.
// Except for char, using any integer type without signed is equivalent to using
// it with signed, i.e. signed int and int are equivalent. char is a different
// type as signed char and unsigned char but char has the same representation
// and range of either signed char or unsigned char. You can use
// std::numeric_limits<char>::is_signed to find out which it uses.
typedef unsigned char byte;


// ============================================================================
//                    sorting encoding, key encoding
// ============================================================================


/**
 * =========================================================
 *             Encoding tags
 * =========================================================
 * These tags classify the value types, and guarantee the order
 * of the encoded values are the same as the original value
 */
// ENCODING_INT_MAX is the maximum int tag value.
constexpr byte ENCODING_INT_MAX = 0xfd; // 0xfd, 253
constexpr byte ENCODING_INT_MIN = 0x80; // 0x80, 128
constexpr byte ENCODING_INT_MAX_WIDTH = 8; // max byte number of an integer
// 0x88, 136
constexpr byte ENCODING_INT_ZERO = ENCODING_INT_MIN + ENCODING_INT_MAX_WIDTH;
// 0x6d, 109
constexpr byte ENCODING_INT_SMALL =
    ENCODING_INT_MAX - ENCODING_INT_ZERO - ENCODING_INT_MAX_WIDTH;

constexpr byte ENCODING_BYTES_MARKER = 0x12;

constexpr byte ENCODING_DATE_MARKER = 0x14;
constexpr byte ENCODING_DATE_MARKER_DESC = 0x15;
constexpr byte ENCODING_DATETIME_MARKER = 0x16;
constexpr byte ENCODING_DATETIME_MARKER_DESC = 0x17;
constexpr byte ENCODING_NULL_MARKER = 0x00;

// these values won't be persisted
constexpr int ENCODING_ASCENDING_VARINT_TYPE   = 1;
constexpr int ENCODING_ASCENDING_UVARINT_TYPE  = 2;
constexpr int ENCODING_ASCENDING_BYTES_TYPE    = 3;
constexpr int ENCODING_ASCENDING_STRING_TYPE   = 4;
constexpr int ENCODING_ASCENDING_DATE_TYPE     = 5;
constexpr int ENCODING_ASCENDING_DATETIME_TYPE = 6;
constexpr int ENCODING_ASCENDING_NULL_TYPE     = 7;

template <int n>
struct ascending_type {
    typedef void type;
};

template<>
struct ascending_type<ENCODING_ASCENDING_VARINT_TYPE> {
    typedef int64_t type;
};

template<>
struct ascending_type<ENCODING_ASCENDING_UVARINT_TYPE> {
    typedef uint64_t type;
};

template<>
struct ascending_type<ENCODING_ASCENDING_BYTES_TYPE> {
    typedef std::string type;
};

template<>
struct ascending_type<ENCODING_ASCENDING_STRING_TYPE> {
    typedef std::string type;
};

template<>
struct ascending_type<ENCODING_ASCENDING_DATE_TYPE> {
    typedef uint64_t type;
};

template<>
struct ascending_type<ENCODING_ASCENDING_DATETIME_TYPE> {
    typedef uint64_t type;
};

template<>
struct ascending_type<ENCODING_ASCENDING_NULL_TYPE> {
    typedef bool type;
};

int gen_upper_endpoint(const std::string& origin, std::string* upper);

/**
 * =========================================================
 *             Encoding escaping value/chars
 * =========================================================
 * These constants are put into the encoding result for decoding purposes
 */
constexpr byte ENCODING_BYTE_ESCAPE = 0x00;
constexpr byte ENCODING_ESCAPED_00 = 0xff;
constexpr byte ENCODING_BYTES_ENDING = 0x01;

/**
 * Encodes the given value
 *
 * @param b output param, encoding result will be appended to b
 * @param v value to encode
 * @return 0 if succeeded, otherwise non-zero
 */
int encode_varint_ascending(const int64_t& v, std::string* b);

/**
 * Encodes the given value
 *
 * @param b output param, encoding result will be appended to b
 * @param v value to encode
 * @return 0 if succeeded, otherwise non-zero
 */
int encode_uvarint_ascending(const uint64_t& v, std::string* b);

/**
 * Decodes the given byte array, once decoded correctly, the bytes at the head
 * of the array, which are decoded, will be removed from the given array by
 * default
 *
 * @param b byte array to decode
 * @param v decoding result
 * @return 0 if succeeded, otherwise non-zero
 */
int decode_uvarint_ascending(std::string* b, uint64_t* v);

/**
 * Decodes the given byte array contained with Slice, once decoded correctly,
 * the bytes at the head of the array, which are decoded, will be removed from
 * the given array by default
 *
 * @param s slice contains byte array to decode
 * @param v decoding result
 * @return 0 if succeeded, otherwise non-zero
 */
int decode_uvarint_ascending(Slice* s, uint64_t* v);

/**
 * Decodes the given byte array, once decoded correctly, the bytes at the head
 * of the array, which are decoded, will be removed from the given array by
 * default
 *
 * @param b byte array to decode
 * @param v decoding result
 * @return 0 if succeeded, otherwise non-zero
 */
int decode_varint_ascending(std::string* b, int64_t* v);

/**
 * Decodes the given byte array contained with Slice, once decoded correctly,
 * the bytes at the head of the array, which are decoded, will be removed from
 * the given array by default
 *
 * @param s slice contains byte array to decode
 * @param v decoding result
 * @return 0 if succeeded, otherwise non-zero
 */
int decode_varint_ascending(Slice* s, int64_t* v);

/**
 * Encodes string
 *
 * @param str string to encode
 * @param b output param
 * @return 0 if succeeded otherwise non-zero
 */
int encode_string_ascending(const std::string& str, std::string* b);

/**
 * Decodes the given byte array, once decoded correctly, the bytes at the head
 * of the array, which are decoded, will be removed from the given array by
 * default
 *
 * @param b byte array to decode
 * @param str decoding result, if the input byte array is malformed, decoding
 *            will fail and partial decoded content may be appended to the
 *            output string
 * @return 0 if succeeded, otherwise non-zero
 */
int decode_string_ascending(std::string* b, std::string* str);

/**
 * Encodes byte array
 *
 * @param bytes byte array to encode, which is contained in a std::string
 * @param b output param
 * @return 0 if succeeded otherwise non-zero
 */
int encode_bytes_ascending(const std::string& bytes, std::string* b);

/**
 * Upper the encoded string value before the sequence "\x00\x01"
 */
int upper_bytes_ascending(std::string* upper);

/**
 * Decodes the given byte array, once decoded correctly, the bytes at the head
 * of the array, which are decoded, will be removed from the given array by
 * default
 *
 * @param b byte array to decode
 * @param bytes decoding result, if the input byte array is malformed, decoding
 *              will fail and partial decoded content may be appended to the
 *              output byte array
 * @return 0 if succeeded, otherwise non-zero
 */
int decode_bytes_ascending(std::string* b, std::string* bytes);

/**
 * Decodes the given byte array contained with Slice, once decoded correctly,
 * the bytes at the head of the array, which are decoded, will be removed from
 * the given array by default
 *
 * @param s slice which contains byte array to decode
 * @param bytes decoding result, if the input byte array is malformed, decoding
 *              will fail and partial decoded content may be appended to the
 *              output byte array
 * @return 0 if succeeded, otherwise non-zero
 */
int decode_bytes_ascending(Slice* s, std::string* bytes);

/**
 * Encodes date, the date value will be encoded as an uint64 and stored as
 * uvarint in ascending format, see `encode_uvarint_ascending()` for more
 * details.
 * Encoding example: date "2017-08-19" -> uint64 20170819 -> encode uint64
 *
 * @param v date to encode
 * @param b output param
 * @return 0 if succeeded otherwise non-zero
 */
int encode_date_ascending(const uint64_t& v, std::string* b);

/**
 * Decodes date, which is encoded with `encode_date_ascending()`
 *
 * @param b byte array to decode
 * @param v output param, datetime represented in int
 * @return 0 if succeeded otherwise non-zero
 */
int decode_date_ascending(std::string* b, uint64_t* v);

/**
 * Decodes date, which is encoded with `encode_date_ascending()`
 *
 * @param s byte array to decode
 * @param v output param, datetime represented in int
 * @return 0 if succeeded otherwise non-zero
 */
int decode_date_ascending(Slice* s, uint64_t* v);

/**
 * Encodes datetime, the datetime value will be encoded as an uint64 and stored
 * as uvarint in ascending format, see `encode_uvarint_ascending()` for more
 * details.
 * Encoding example:
 * datetime "2017-08-19 19:00:00" -> uint64 20170819190000 -> encode uint64
 *
 * @param v datetime represented in int to encode
 * @param b output param
 * @return 0 if succeeded otherwise non-zero
 */
int encode_datetime_ascending(const uint64_t& v, std::string* b);

/**
 * Decodes datetime, which is encoded with `encode_datetime_ascending()`
 *
 * @param b byte array to decode
 * @param v output param, datetime represented in int
 * @return 0 if succeeded otherwise non-zero
 */
int decode_datetime_ascending(std::string* b, uint64_t* v);

/**
 * Decodes datetime, which is encoded with `encode_datetime_ascending()`
 *
 * @param s byte array to decode
 * @param v output param, datetime represented in int
 * @return 0 if succeeded otherwise non-zero
 */
int decode_datetime_ascending(Slice* s, uint64_t* v);

/**
 * Encodes null
 *
 * @param b output param
 * @param null place holder param, no actual meaning
 * @return 0 for success, non-zero for failure
 */
int encode_null_ascending(const bool& null, std::string* b);

/**
 * Decodes encoded null, if the field is null the slice will be decoded,
 * otherwise, the slice remain unchanged. This function may fail when the
 * given slice is empty.
 *
 * @param s slice to decode
 * @param null output param, true for null decoded, false for not null
 * @return 0 for success, non-zero for failure
 */
int decode_null_ascending(Slice* s, bool* null);

/**
 * Decodes encoded null, if the field is null the slice will be decoded,
 * otherwise, the slice remain unchanged. This function may fail when the
 * given slice is empty.
 *
 * @param b binary to decode
 * @param null output param, true for null decoded, false for not null
 * @return 0 for success, non-zero for failure
 */
int decode_null_ascending(std::string* b, bool* null);

/*
 Encoding Format for Float-Pointing Values

 The encoding format relies on the property of IEEE754 (https://en.wikipedia.org/wiki/IEEE_754):
   > A property of the single- and double-precision formats is that their
   > encoding allows one to easily sort them without using floating-point
   > hardware, as if the bits represented sign-magnitude integers, although
   > it is unclear whether this was a design consideration ....... an xor
   > to flip the sign bit for positive values and all bits for negative
   > values, all the values become sortable as unsigned integers (with −0 <
   > +0).

 The encoding of a float64 value ‘f’ involves the following steps:
 - Encode both ‘+0’ and ‘-0’ as ‘+0’
 - If ‘f’ is positive or +0, flip the sign bit
 - If ‘f’ is negative, flip all bits

 Subsequently, ‘f’ is casted to an uint64 and converted to a big-endian format.
*/

/**
 * Encodes the given value
 *
 * @param b output param, encoding result will be appended to b
 * @param v value to encode
 * @return 0 if succeeded, otherwise non-zero
 */
int encode_float_ascending(float v, std::string* b);
int encode_double_ascending(double v, std::string* b);

/**
 * Decodes the given byte array, once decoded correctly, the bytes at the head
 * of the array, which are decoded, will be removed from the given array by
 * default
 *
 * @param b byte array to decode
 * @param v decoding result
 * @return 0 if succeeded, otherwise non-zero
 */
int decode_float_ascending(Slice* b, float* v);
int decode_double_ascending(Slice* b, double* v);

// ============================================================================
//                    end of sorting encoding, key encoding
// ============================================================================


// ============================================================================
//                         nonsorting encoding, value encoding
// ============================================================================

constexpr byte CURRENT_VALUE_ENCODING_VERSION = 1;

constexpr byte ENCODING_NULL_VALUE = 0x00;

/**
 * Encodes an uint64_t value with in nonsorting form
 *
 * @param v
 * @param b output param
 * @return 0 if succeeded, otherwise non-zero
 */
int encode_uvarint_nonsorting(uint64_t v, std::string* b);

/**
 * Decodes an uint64_t value from a raw byte array which generated by
 * `encode_uvarint_nonsorting()`
 *
 * @param b
 * @param v output param
 * @return 0 if succeeded, otherwise non-zero
 */
int decode_uvarint_nonsorting(std::string* b, uint64_t* v);

/**
 * Decodes an uint64_t value from a raw byte array which generated by
 *
 * @param s slice which contains byte array to decode
 * @param v output param
 * @return 0 if succeeded, otherwise non-zero
 */
int decode_uvarint_nonsorting(Slice* s, uint64_t* v);

/**
 * Encodes an int64_t value with in nonsorting form
 *
 * @param b
 * @param v
 * @return 0 if succeeded, otherwise non-zero
 */
int encode_varint_nonsorting(int64_t v, std::string* b);

/**
 * Decodes an int64_t value from a raw byte array which generated by
 * `encode_varint_nonsorting()`
 *
 * @param b
 * @param v output param
 * @return 0 if succeeded, otherwise non-zero
 */
int decode_varint_nonsorting(std::string* b, int64_t* v);

/**
 * Decodes an int64_t value from a raw byte array which generated by
 * `encode_varint_nonsorting()`
 *
 * @param s
 * @param v output param
 * @return 0 if succeeded, otherwise non-zero
 */
int decode_varint_nonsorting(Slice* s, int64_t* v);

/**
 *
 * @param str string to encode
 * @param b output param
 * @param len output param, number of byte of encoded result, which has been
 *            appended to b
 * @return 0 if succeeded, otherwise non-zero
 */
int encode_string_nonsorting(const std::string& str, std::string* b,
                            std::size_t* len = nullptr);

/**
 *
 * @param b raw byte to decode
 * @param len number of byte for decoding
 * @param str output param, decode result
 * @return 0 if succeeded, otherwise non-zero
 */
int decode_string_nonsorting(std::string* b, std::size_t len, std::string* str);

/**
 *
 * @param s raw byte to decode
 * @param len number of byte for decoding
 * @param str output param, decode result
 * @return 0 if succeeded, otherwise non-zero
 */
int decode_string_nonsorting(Slice* s, std::size_t len, std::string* str);

/**
 *
 * @param str string to encode
 * @param b output param
 * @param len output param, number of byte of encoded result, which has been
 *            appended to b
 * @return 0 if succeeded, otherwise non-zero
 */
int encode_bytes_nonsorting(const std::string& str, std::string* b,
                            std::size_t* len = nullptr);

/**
 *
 * @param b raw byte to decode
 * @param len number of byte for decoding
 * @param str output param, decode result
 * @return 0 if succeeded, otherwise non-zero
 */
int decode_bytes_nonsorting(std::string* b, std::size_t len, std::string* str);

/**
 *
 * @param s raw byte to decode
 * @param len number of byte for decoding
 * @param str output param, decode result
 * @return 0 if succeeded, otherwise non-zero
 */
int decode_bytes_nonsorting(Slice* s, std::size_t len, std::string* str);

int encode_fixed_uint8_nonsorting(uint8_t v, std::string* b);

int decode_fixed_uint8_nonsorting(Slice* b, uint8_t* v);

/**
 * Encodes type int32_t with fixed length, the length of the encoded value is
 * the same as sizeof(int32_t), the output encoding is big endian.
 *
 * @param v value to encode
 * @param b output param
 * @return 0 if succeeded, otherwise non-zero
 */
int encode_fixed_int32_nonsorting(int32_t v, std::string* b);

/**
 * Decodes type int32_t which is encoded with `encode_fixed_int32_nonsorting()`
 *
 * @param b raw bytes to decode
 * @param v output param
 * @return 0 if succeeded, otherwise non-zero
 */
int decode_fixed_int32_nonsorting(std::string* b, int32_t* v);

/**
 * Decodes type int32_t which is encoded with `encode_fixed_int32_nonsorting()`
 *
 * @param s slice contains raw bytes to decode
 * @param v output param
 * @return 0 if succeeded, otherwise non-zero
 */
int decode_fixed_int32_nonsorting(Slice* s, int32_t* v);

/**
 * Encodes type uint32_t with fixed length, the length of the encoded value is
 * the same as sizeof(uint32_t), the output encoding is big endian.
 *
 * @param v value to encode
 * @param b output param
 * @return 0 if succeeded, otherwise non-zero
 */
int encode_fixed_uint32_nonsorting(uint32_t v, std::string* b);

/**
 * Decodes type uint32_t which is encoded with `encode_fixed_uint32_nonsorting`
 *
 * @param b raw bytes to decode
 * @param v output param
 * @return 0 if succeeded, otherwise non-zero
 */
int decode_fixed_uint32_nonsorting(std::string* b, uint32_t* v);

/**
 * Decodes type uint32_t which is encoded with `encode_fixed_uint32_nonsorting`
 *
 * @param s slice contains raw bytes to decode
 * @param v output param
 * @return 0 if succeeded, otherwise non-zero
 */
int decode_fixed_uint32_nonsorting(Slice* s, uint32_t* v);

/**
 * Encodes type int64_t with fixed length, the length of the encoded value is
 * the same as sizeof(int64_t), the output encoding is big endian.
 *
 * @param v value to encode
 * @param b output param
 * @return 0 if succeeded, otherwise non-zero
 */
int encode_fixed_int64_nonsorting(int64_t v, std::string* b);

/**
 * Decodes type int64_t which is encoded with `encode_fixed_int64_nonsorting()`
 *
 * @param b raw bytes to decode
 * @param v output param
 * @return 0 if succeeded, otherwise non-zero
 */
int decode_fixed_int64_nonsorting(std::string* b, int64_t* v);

/**
 * Decodes type int64_t which is encoded with `encode_fixed_int64_nonsorting()`
 *
 * @param s slice contains raw bytes to decode
 * @param v output param
 * @return 0 if succeeded, otherwise non-zero
 */
int decode_fixed_int64_nonsorting(Slice* s, int64_t* v);

/**
 * Encodes type float with fixed length, which is 4, encoded value is formated
 * in IEEE754, see <https://en.wikipedia.org/wiki/IEEE_754> for more detail
 *
 * @param v value to decode
 * @param b output param
 * @return 0 if succeeded, otherwise non-zero
 */
int encode_float_nonsorting(float v, std::string* b);

/**
 * Decodes type float which is encoded with `encode_float_nonsorting()`
 *
 * @param b raw bytes to decode
 * @param v output param
 * @return 0 if succeeded, otherwise non-zero
 */
int decode_float_nonsorting(std::string* b, float* v);

/**
 * Decodes type float which is encoded with `encode_float_nonsorting()`
 *
 * @param s slice contains raw bytes to decode
 * @param v output param
 * @return 0 if succeeded, otherwise non-zero
 */
int decode_float_nonsorting(Slice* s, float* v);

/**
 * Encodes type double with fixed length, which is 4, encoded value is formated
 * in IEEE754, see <https://en.wikipedia.org/wiki/IEEE_754> for more detail
 *
 * @param v value to decode
 * @param b output param
 * @return 0 if succeeded, otherwise non-zero
 */
int encode_double_nonsorting(double v, std::string* b);

/**
 * Decodes type double which is encoded with `encode_double_nonsorting()`
 *
 * @param b raw bytes to decode
 * @param v output param
 * @return 0 if succeeded, otherwise non-zero
 */
int decode_double_nonsorting(std::string* b, double* v);

/**
 * Decodes type double which is encoded with `encode_double_nonsorting()`
 *
 * @param s slice contains raw bytes to decode
 * @param v output param
 * @return 0 if succeeded, otherwise non-zero
 */
int decode_double_nonsorting(Slice* s, double* v);

/**
 * Encodes a bool value with 1 byte
 *
 * @param v value to encode
 * @param b output param
 * @return 0 if succeeded, otherwise non-zero
 */
int encode_bool_nonsorting(bool v, std::string* b);
int decode_bool_nonsorting(std::string* b, bool* v);
int decode_bool_nonsorting(Slice* b, bool* v);

/**
 * Encodes a date represented in int like: 20170819 with fixed length of 4
 *
 * @param v date to encode
 * @param b output param
 * @return 0 if succeeded, otherwise non-zero
 */
int encode_date_nonsorting(uint32_t v, std::string* b);
int decode_date_nonsorting(std::string* b, uint32_t* v);
int decode_date_nonsorting(Slice* b, uint32_t* v);

/**
 * Encodes a datetime represented in int like: 20170819190000 with fixed length
 * of 8
 *
 * @param v date to encode
 * @param b output param
 * @return 0 if succeeded, otherwise non-zero
 */
int encode_datetime_nonsorting(uint64_t v, std::string* b);
int decode_datetime_nonsorting(std::string* b, uint64_t* v);
int decode_datetime_nonsorting(Slice* b, uint64_t* v);


// ============================================================================
//                 end of nonsorting encoding, value encoding
// ============================================================================


// ============================================================================
//                   format associated value encoding
// ============================================================================

/**
 * Values of each row of the table will be encoded in the form as following:
 *
 * valEncVer/colId1/valType1/valEncLen1/encVal1/colId2/valType2/valEncLen2/encVal2/...
 *      ^       ^      ^         ^         ^
 *      |       |      |         |         |
 *      |       |      |         |         \------ encoded value
 *      |       |      |         \---------------- length of encoded valued
 *      |       |      |                           `encode_varint_nonsorting`
 *      |       |      \-------------------------- value type, fixed to 1 byte
 *      |       |                                  `encode_varint_nonsorting`
 *      |       \--------------------------------- column id
 *      |                                          `encode_varint_nonsorting`
 *      \----------------------------------------- encoding version of value
 *                                                 `encode_varint_nonsorting`
 *
 * in which, '/' does not actually exist, it is here only for convenience of
 * representing each part of the encoded result.
 *
 * The combination of each part is done by the caller of this codec, this codec
 * only provides interfaces to achieve that goal.
 *
 * It means that:
 * the caller needs to know the input raw byte array has the type header
 * `col_id/val_type` or version header `val_enc_ver` when decode;
 * the caller also needs to know when to prepend `col_id` and `val_type` to
 * output raw byte array
 *
 * To encode a row of the table with the above format:
 * 1. initiate a raw byte array, say `std::string* b`
 * 2. call `encode_rocks_value_version(b)` to encode encoding version
 * 3. call `encode_value_header(col_id, type, b)`, to encode the value header:
 *    column id and type
 * 4. call `encode_xxx_value(v, b)`, to encode the value
 * 5. repeat step 2-4 until all values of row in the table encoded into `b`
 *
 * To decode a row of the table with the above format:
 * 1. get a slice from rocksDB, say `Slice* s`
 * 2. call `decode_rocks_value_version(s, ver)`, to get encoding version
 * 3. call `decode_value_header(s, col_id, type)`, to decode the value header, to
 * 4. call `decode_xxx_value(s, v)`, to encode the value
 * 5. repeat step 2-4 until all values of row in the slice of rocksDB decoded
 *
 * for types float, double, date and datetime they are fixed-length encoded
 * length are, in bits, 32, 64, 32, 64. other integers are variant length.
 *
 */

/**
 * Encodes the encoding version, the default encoding version is hard coded
 *
 * @param b output param
 * @param ver version to encode, default encoding version is
 *            CURRENT_VALUE_ENCODING_VERSION
 */
int encode_rocks_value_version(std::string* b,
         int32_t ver = CURRENT_VALUE_ENCODING_VERSION);

/**
 * Decodes the encoding version from value stored in rocksDB
 *
 * @param s value of rocksDB
 * @param ver output param, version
 * @return 0 if succeeded, otherwise non-zero
 */
int decode_rocks_value_version(Slice* s, int32_t* ver);

/**
 * Encodes the column id to the raw byte array
 *
 * @param cid column id
 * @param b output param
 * @return 0 if succeeded, otherwise non-zero
 */
int encode_column_id(COLID cid, std::string* b);

/**
 * Decodes the column id from the slice
 *
 * @param s given slice
 * @param cid output param
 * @return 0 if succeeded, otherwise non-zero
 */
int decode_column_id(Slice* s, COLID* cid);

/**
 * Encodes the column type to the raw byte array
 *
 * @param val_type value type
 * @param b output param
 * @return 0 if succeeded, otherwise non-zero
 */
int encode_value_type(int32_t val_type, std::string* b);

/**
 * Decodes the column type from the slice
 *
 * @param s given slice
 * @param val_type output param
 * @return 0 if succeeded, otherwise non-zero
 */
int decode_value_type(Slice* s, int32_t* val_type);

/**
 * Encodes the length of the column to raw byte array
 *
 * @param val_length
 * @param b output param
 * @param val_length the length of the encoded column
 */
int encode_value_length(std::size_t val_length, std::string* b);

/**
 * Decodes the length of the column from slice of rocksDB
 *
 * @param s a value stored in rocksDB
 * @param val_length column length
 * @return 0 if succeeded, otherwise non-zero
 */
int decode_value_length(Slice* s, std::size_t* val_length);

/**
 * Encodes the header of a column stored in rocksDB, value header is consist of
 * column id and type
 *
 * @param cid column id
 * @param type column type
 * @param b ouput param, raw byte array
 * @return 0 if succeeded, otherwise non-zero
 */
int encode_value_header(COLID cid, int32_t type, std::string* b);

/**
 * Decodes the header of a column stored in rocksDB, decoded byte will be
 * removed from slice
 *
 * @param s a value stored in rocksDB
 *          the call needs to ensure the format of given value is decodable
 * @param cid ouput param, column id
 * @param type ouput param, column type
 * @return 0 if succeeded, otherwise non-zero
 */
int decode_value_header(Slice* s, COLID* cid, int32_t* type);

/**
 * Skips current value stored in the slice, head of the slice will point to next
 * value header if there are more values
 *
 * @param s given slice
 * @param type current type
 * @return 0 if succeeded, otherwise non-zero
 */
int skip_value(Slice* s, int32_t type);

/**
 * Skips the first field of the slice which is encoding result of
 * encode_xxx_ascending() function family, head of the slice will point to next
 * field
 *
 * @param s given slice
 * @return 0 if succeeded, otherwise non-zero
 */
int skip_key(Slice* s);

/**
 * Decodes the first field of the slice which is encoding result of
 * encode_xxx_ascending() function family, if call doesn't care about the
 * decoding result, just set the ouput param to nullptr. This function may
 * allocate memory for the decoded value, call has to free it when necessary.
 *
 * @param ascending_slice slice in which all the fields are encoded with the
 *                        encode_xxx_ascending() function family
 * @param type output param, type of the field successfully decoded,
 *             can be nullptr
 * @param val output param, 2nd order pointer to value decoded, use the type
 *            returned to cast void* to proper type after calling this function,
 *            can be nullptr
 *            IF val IS NOT nullptr, THIS FUNCTION WILL ALLOCATE MEMORY FOR THE
 *            VALUE DECODED, THE CALLER HAS TO FREE IT WHEN NECESSARY
 * @param encoded_len output param, indecate how many bytes are extracted from
 *                    ascending_slice, can be nullptr
 * @return 0 if succeeded, otherwise non-zero
 */
int decode_ascending_encoded_slice(Slice* ascending_slice,
                                    int* type,
                                    void** val,
                                    int* encoded_len);

/**
 * Encodes an int to raw byte array, which will be stored in rocksDB
 *
 * @param v value to encode into rocks row
 * @param b output param
 * @return 0 if succeeded, otherwise non-zero
 */
int encode_int_value(int64_t v, std::string* b);

/**
 * Decodes an int stored in the slice of rocksDB, decoded bytes will be removed
 * from slice
 *
 * @param s given slice
 * @param v output param
 * @return 0 if succeeded, otherwise non-zero
 */
int decode_int_value(Slice* s, int64_t* v);

/**
 * Encodes bool to raw byte array, which will be stored in rocksDB
 *
 * @param v value to encode into rocks row
 * @param b output param
 * @return 0 if succeeded, otherwise non-zero
 */
int encode_bool_value(bool v, std::string* s);

/**
 * Decodes a bool stored in the slice of rocksDB, decoded bytes will be removed
 * from slice
 *
 * @param s given slice
 * @param v output param
 * @return 0 if succeeded, otherwise non-zero
 */
int decode_bool_value(Slice* s, bool* v);

int encode_float_value(float v, std::string* s);
int decode_float_value(Slice* s, float* v);

int encode_double_value(double v, std::string* s);
int decode_double_value(Slice* s, double* v);

int encode_bytes_value(const std::string& v, std::string* s);
int decode_bytes_value(Slice* s, std::string* v);

int encode_string_value(const std::string& v, std::string* s);
int decode_string_value(Slice* s, std::string* v);
int decode_string_value(Slice* s, std::string_view* v);

int encode_date_value(uint32_t v, std::string* s);
int decode_date_value(Slice* s, uint32_t* v);

int encode_datetime_value(uint64_t v, std::string* s);
int decode_datetime_value(Slice* s, uint64_t* v);

/**
 * Deocdes all alues contained by the given slice which is produced
 * by function family of encode_xxx_value()
 *
 * Note that the slice must begin with encoding version
 *
 * typedef std::function<void(void*)> Deleter;
 *
 * @param nonsorting_slice
 * @param cid column id
 * @param type value type
 * @param val value controlled by unique_ptr with coustomized deleter,
 *            user doesn't need to care about how to recyle the memory
 * @param cid_len encoded column id length
 * @param type_len encoded type length
 * @param val_len encoded value length
 * @return
 */
int decode_nonsorting_encoded_slice_v1(Slice* nonsorting_slice,
                                    COLID* cid,
                                    int* type,
                                    std::unique_ptr<void, std::function<void(void*)>>* val,
                                    int* cid_len,
                                    int* type_len,
                                    int* val_len
                                    );

// TODO: all methods should be in Codec?
class Codec {
public:
    typedef ascending_type<ENCODING_ASCENDING_VARINT_TYPE>::type KEY_VARINT;
    typedef ascending_type<ENCODING_ASCENDING_UVARINT_TYPE>::type KEY_UVARINT;
    typedef ascending_type<ENCODING_ASCENDING_BYTES_TYPE>::type KEY_BYTES;
    typedef ascending_type<ENCODING_ASCENDING_STRING_TYPE>::type KEY_STRING;
    typedef ascending_type<ENCODING_ASCENDING_DATE_TYPE>::type KEY_DATE;
    typedef ascending_type<ENCODING_ASCENDING_DATETIME_TYPE>::type KEY_DATETIME;
    // <db_id, table_id, index_id>
    //typedef std::tuple<uint64_t, uint64_t, int32_t> KeyPrefixTuple;

    static int decode_key_prefix(Slice* key_slice, int64_t* prefix);
    static int decode_coloumn_family_id(Slice* key, int64_t* column_family_id);

    static std::string debug_key(const Slice& s);
    static std::string debug_key(const std::string& s);

private:
    static std::string debug_key(Slice* s);
    static int print_one_key_slot(Slice* key_slice, std::stringstream* steam);

};

// ============================================================================
//                   end of format associated value encoding
// ============================================================================

} // codec
} // mochow::scalar
