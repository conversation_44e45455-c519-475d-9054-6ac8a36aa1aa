// Copyright (c) 2017, Baidu.com, Inc. All Rights Reserved

// Licensed to the Apache Software Foundation (ASF) under one
// or more contributor license agreements.  See the NOTICE file
// distributed with this work for additional information
// regarding copyright ownership.  The ASF licenses this file
// to you under the Apache License, Version 2.0 (the
// "License"); you may not use this file except in compliance
// with the License.  You may obtain a copy of the License at
//
//   http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing,
// software distributed under the License is distributed on an
// "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
// KIND, either express or implied.  See the License for the
// specific language governing permissions and limitations
// under the License.

#include <endian.h>
#include "core/src/scalar/interface/codec.h"

namespace mochow::scalar {
namespace codec {

int gen_upper_endpoint(const std::string& origin, std::string* upper) {
    bool found = false;
    std::string bytes = origin;
    for (int i = origin.size() - 1; i >= 0; --i) {
        int value = origin[i] & 0xff;
        if (value != 0xff) {
            bytes[i] = value + 1;
            found = true;
            break;
        } else {
            bytes[i] = 0;
        }
    }
    if (found) {
        *upper = bytes;
    } else {
        bytes.push_back(0);
        *upper = bytes;
    }
    return 0;
}

template<typename T, typename F>
struct alias_cast_t {
    union {
        F raw;
        T data;
    };
};

template<typename T, typename F>
T alias_cast(F raw_data) {
    static_assert(sizeof(T) == sizeof(F), "Cannot cast types of different sizes");
    alias_cast_t<T, F> ac;
    ac.raw = raw_data;
    return ac.data;
}

// ============================================================================
//                    sorting encoding, key encoding
// ============================================================================

/**
 * Encodes the given value
 */
int encode_varint_ascending(const int64_t& v, std::string* b) {
    // max encoding result 9 bytes
    byte buf[9];
    int cnt = 0;
    if (v >= 0) {
        return encode_uvarint_ascending(static_cast<uint64_t>(v), b);
    }
    if (v >= -0xffL) {
        cnt = 2;
        buf[0] = ENCODING_INT_MIN + 7;
        buf[1] = static_cast<byte>(v);
    } else if (v >= -0xffffL) {
        cnt = 3;
        buf[0] = ENCODING_INT_MIN + 6;
        buf[1] = static_cast<byte>(v >> 8);
        buf[2] = static_cast<byte>(v);
    } else if (v >= -0xffffffL) {
        cnt = 4;
        buf[0] = ENCODING_INT_MIN + 5;
        buf[1] = static_cast<byte>(v >> 16);
        buf[2] = static_cast<byte>(v >> 8);
        buf[3] = static_cast<byte>(v);
    } else if (v >= -0xffffffffL) {
        cnt = 5;
        buf[0] = ENCODING_INT_MIN + 4;
        buf[1] = static_cast<byte>(v >> 24);
        buf[2] = static_cast<byte>(v >> 16);
        buf[3] = static_cast<byte>(v >> 8);
        buf[4] = static_cast<byte>(v);
    } else if (v >= -0xffffffffffL) {
        cnt = 6;
        buf[0] = ENCODING_INT_MIN + 3;
        buf[1] = static_cast<byte>(v >> 32);
        buf[2] = static_cast<byte>(v >> 24);
        buf[3] = static_cast<byte>(v >> 16);
        buf[4] = static_cast<byte>(v >> 8);
        buf[5] = static_cast<byte>(v);
    } else if (v >= -0xffffffffffffL) {
        cnt = 7;
        buf[0] = ENCODING_INT_MIN + 2;
        buf[1] = static_cast<byte>(v >> 40);
        buf[2] = static_cast<byte>(v >> 32);
        buf[3] = static_cast<byte>(v >> 24);
        buf[4] = static_cast<byte>(v >> 16);
        buf[5] = static_cast<byte>(v >> 8);
        buf[6] = static_cast<byte>(v);
    } else if (v >= -0xffffffffffffffL) {
        cnt = 8;
        buf[0] = ENCODING_INT_MIN + 1;
        buf[1] = static_cast<byte>(v >> 48);
        buf[2] = static_cast<byte>(v >> 40);
        buf[3] = static_cast<byte>(v >> 32);
        buf[4] = static_cast<byte>(v >> 24);
        buf[5] = static_cast<byte>(v >> 16);
        buf[6] = static_cast<byte>(v >> 8);
        buf[7] = static_cast<byte>(v);
    } else {
        cnt = 9;
        buf[0] = ENCODING_INT_MIN;
        buf[1] = static_cast<byte>(v >> 56);
        buf[2] = static_cast<byte>(v >> 48);
        buf[3] = static_cast<byte>(v >> 40);
        buf[4] = static_cast<byte>(v >> 32);
        buf[5] = static_cast<byte>(v >> 24);
        buf[6] = static_cast<byte>(v >> 16);
        buf[7] = static_cast<byte>(v >> 8);
        buf[8] = static_cast<byte>(v);
    }
    b->insert(b->end(), buf, buf + cnt);
    return 0;
}

/**
 * Encodes the given value
 *
 */
int encode_uvarint_ascending(const uint64_t& v, std::string* b) {
    byte buf[10];
    int cnt = 0;
    if (v <= ENCODING_INT_SMALL) {
        cnt = 1;
        buf[0] = ENCODING_INT_ZERO + static_cast<byte>(v);
    } else if (v <= 0xffL) {
        cnt = 2;
        buf[0] = ENCODING_INT_MAX - 7;
        buf[1] = static_cast<byte>(v);
    } else if (v <= 0xffffL) {
        cnt = 3;
        buf[0] = ENCODING_INT_MAX - 6;
        buf[1] = static_cast<byte>(v >> 8);
        buf[2] = static_cast<byte>(v);
    } else if (v <= 0xffffffL) {
        cnt = 4;
        buf[0] = ENCODING_INT_MAX - 5;
        buf[1] = static_cast<byte>(v >> 16);
        buf[2] = static_cast<byte>(v >> 8);
        buf[3] = static_cast<byte>(v);
    } else if (v <= 0xffffffffL) {
        cnt = 5;
        buf[0] = ENCODING_INT_MAX - 4;
        buf[1] = static_cast<byte>(v >> 24);
        buf[2] = static_cast<byte>(v >> 16);
        buf[3] = static_cast<byte>(v >> 8);
        buf[4] = static_cast<byte>(v);
    } else if (v <= 0xffffffffffL) {
        cnt = 6;
        buf[0] = ENCODING_INT_MAX - 3;
        buf[1] = static_cast<byte>(v >> 32);
        buf[2] = static_cast<byte>(v >> 24);
        buf[3] = static_cast<byte>(v >> 16);
        buf[4] = static_cast<byte>(v >> 8);
        buf[5] = static_cast<byte>(v);
    } else if (v <= 0xffffffffffffL) {
        cnt = 7;
        buf[0] = ENCODING_INT_MAX - 2;
        buf[1] = static_cast<byte>(v >> 40);
        buf[2] = static_cast<byte>(v >> 32);
        buf[3] = static_cast<byte>(v >> 24);
        buf[4] = static_cast<byte>(v >> 16);
        buf[5] = static_cast<byte>(v >> 8);
        buf[6] = static_cast<byte>(v);
    } else if (v <= 0xffffffffffffffL) {
        cnt = 8;
        buf[0] = ENCODING_INT_MAX - 1;
        buf[1] = static_cast<byte>(v >> 48);
        buf[2] = static_cast<byte>(v >> 40);
        buf[3] = static_cast<byte>(v >> 32);
        buf[4] = static_cast<byte>(v >> 24);
        buf[5] = static_cast<byte>(v >> 16);
        buf[6] = static_cast<byte>(v >> 8);
        buf[7] = static_cast<byte>(v);
    } else {
        cnt = 9;
        buf[0] = ENCODING_INT_MAX;
        buf[1] = static_cast<byte>(v >> 56);
        buf[2] = static_cast<byte>(v >> 48);
        buf[3] = static_cast<byte>(v >> 40);
        buf[4] = static_cast<byte>(v >> 32);
        buf[5] = static_cast<byte>(v >> 24);
        buf[6] = static_cast<byte>(v >> 16);
        buf[7] = static_cast<byte>(v >> 8);
        buf[8] = static_cast<byte>(v);
    }
    b->insert(b->end(), buf, buf + cnt);
    return 0;
}

/**
 * Decodes the given byte array, once decoded correctly, the bytes at the head
 * of the array, which are decoded, will be removed from the given array.
 * There is an unnecessary copy in the implementation which is mainly for
 * testing purpose of all varint codec functions
 */
int decode_varint_ascending(std::string* b, int64_t* v) {
    Slice s(*b);
    int ret = decode_varint_ascending(&s, v);
    b->assign(s.data(), s.size());
    return ret;
}

int decode_varint_ascending(Slice* s, int64_t* v) {
    if (s->empty()) {
        // Insufficient bytes to decode uvarint value
        return -1;
    }

    int length = static_cast<int>(*s->data() & 0xff) - ENCODING_INT_ZERO;

    // Negative number
    int64_t val = 0;
    if (length < 0) {
        length = -length;
        if (s->size() < static_cast<std::size_t>(length + 1)) {
            // insufficient bytes to decode uvarint value
            return -2;
        }
        // Build sign bit first, and shift-left to build negative number
        val = 0xffffffffffffffffL;
        for (int i = 1; i < length + 1; ++i) {
            val = (val << 8) | ((*s)[i] & 0xff);
        }
        s->remove_prefix(length + 1);
        *v = val;
        return 0;
    }

    // Positive number
    uint64_t uval = 0;
    int ret = decode_uvarint_ascending(s, &uval);
    if (ret != 0) {
        return ret;
    } else if (uval > std::numeric_limits<int64_t>::max()) {
        // Value exceeds max value of int64_t,
        // if the input byte array was encoded by encode_varint_ascending(),
        // it should never reach this branch
        return -3;
    }
    *v = static_cast<int64_t>(uval);
    return 0;
}

/**
 * Decodes the given byte array, once decoded correctly, the bytes at the head
 * of the array, which are decoded, will be removed from the given array
 * There is an unnecessary copy in the implementation which is mainly for
 * testing purpose
 * of all uvarint codec functions
 */
int decode_uvarint_ascending(std::string* b, uint64_t* v) {
    Slice s(*b);
    int ret = decode_uvarint_ascending(&s, v);
    b->assign(s.data(), s.size());
    return ret;
}

int decode_uvarint_ascending(Slice* s, uint64_t* v) {
    if (s->empty()) {
        // Insufficient bytes to decode uvarint value
        return -1;
    }
    int length = static_cast<int64_t>(*s->data() & 0xff) - ENCODING_INT_ZERO;
    if (length <= ENCODING_INT_SMALL) {
        s->remove_prefix(1);
        *v = static_cast<uint64_t>(length);
        return 0;
    }
    length -= ENCODING_INT_SMALL;
    if (length < 0 || length > 8) {
        // Invalid uvarint length of %d
        return -2;
    } else if (s->size() < static_cast<std::size_t>(length + 1)) {
        // Insufficient bytes to decode uvarint value
        return -3;
    }
    uint64_t val = 0;
    for (int i = 1; i < length + 1; ++i) {
        val = (val << 8) | ((*s)[i] & 0xff);
    }
    *v = val;
    s->remove_prefix(length + 1);
    return 0;
}

/**
 * Encodes the given string to a byte array, implemented with byte array encoder
 */
int encode_string_ascending(const std::string& str, std::string* b) {
    return encode_bytes_ascending(str, b);
}

/**
 * Decodes the given byte array to a string, implemented with byte array decoder
 */
int decode_string_ascending(std::string* b, std::string* str) {
    return decode_bytes_ascending(b, str);
}

/**
 * upper the encoded value, just upper "\x01" int the sequence "\x00\x01"
*/
int upper_bytes_ascending(std::string* upper) {
    if (upper == nullptr) {
        return -1;
    }
    std::size_t escape_pos = upper->rfind(ENCODING_BYTES_ENDING);
    (*upper)[escape_pos] += 1;
    return 0;
}

/**
 * Encodes the byte array using an escape-based
 * encoding. The encoded value is terminated with the sequence
 * "\x00\x01" which is guaranteed to not occur elsewhere in the
 * encoded value. The encoded bytes are append to the supplied buffer
 * and the resulting buffer is returned.
 */
int encode_bytes_ascending(const std::string& bytes, std::string* b) {
    // Add bytes tag
    b->push_back(static_cast<char>(ENCODING_BYTES_MARKER));
    // Escape 0x00 as 0x00ff
    std::size_t escape_pos = 0;
    std::size_t start_pos = 0;
    while (true) {
        escape_pos = bytes.find(ENCODING_BYTE_ESCAPE, escape_pos);
        if (escape_pos == std::string::npos) {
            break;
        }
        b->insert(b->end(), bytes.begin() + start_pos,
                    bytes.begin() + escape_pos);
        b->push_back(ENCODING_BYTE_ESCAPE);
        b->push_back(ENCODING_ESCAPED_00);
        ++escape_pos;
        start_pos = escape_pos;
    }
    b->insert(b->end(), bytes.begin() + start_pos, bytes.end());
    b->push_back(ENCODING_BYTE_ESCAPE);
    b->push_back(ENCODING_BYTES_ENDING);
    return 0;
}

/**
 * Decodes the given byte array
 * There is an unnecessary copy in the implementation which is mainly for
 * testing purpose of all bytes codec functions
 */
int decode_bytes_ascending(std::string* b, std::string* bytes) {
    Slice s(*b);
    int ret = decode_bytes_ascending(&s, bytes);
    b->assign(s.data(), s.size());
    return ret;
}

/**
 * Extracts the bytes encoded with encode_bytes_ascending(), if the output param
 * is nullptr, encoded bytes removed without output.
 *
 * @param b slice to extract
 * @param bytes output param, can be nullptr
 * @return 0 if succeeded, non-zero for failure
 */
static inline int decode_bytes_ascending_extract(Slice* b, std::string* bytes) {
    // remove marker
    b->remove_prefix(1);
    // skip the first byte of marker
    while (true) {
        size_t pos = b->find(ENCODING_BYTE_ESCAPE);
        if (pos == std::string::npos) {
            // no ENCODING_BYTE_ESCAPE found, array without ending
            return -2;
        }
        if ((pos + 1) >= b->size()) {
            // malformed bytes encoding
            return -3;
        }
        byte c = static_cast<byte>((*b)[pos + 1]);
        if (c == ENCODING_BYTES_ENDING) {
            if (bytes != nullptr) {
                bytes->append(b->data(), pos);
            }
            b->remove_prefix(pos + 2);
            break;
        } else if (c == ENCODING_ESCAPED_00) {
            if (bytes != nullptr) {
                bytes->append(b->data(), pos + 1);
            }
            b->remove_prefix(pos + 2);
        } else {
            // undefined escaping marker
            return -4;
        }
    }
    return 0;
}

int decode_bytes_ascending(Slice* b, std::string* bytes) {
    if (b->empty() || (*b)[0] != ENCODING_BYTES_MARKER) {
        // byte array marker/tag not found
        return -1;
    }
    return decode_bytes_ascending_extract(b, bytes);
}

int encode_date_ascending(const uint64_t& v, std::string* b) {
    b->push_back(ENCODING_DATE_MARKER);
    return encode_uvarint_ascending(v, b);
}

int decode_date_ascending(std::string* b, uint64_t* v) {
    Slice s(*b);
    int ret = decode_date_ascending(&s, v);
    b->assign(s.data(), s.size());
    return ret;
}

int decode_date_ascending(Slice* b, uint64_t* v) {
    if (b->empty()) {
        // not enough bytes to decode
        return -1;
    }
    if (b->data()[0] != ENCODING_DATE_MARKER) {
        // not type date
        return -2;
    }
    b->remove_prefix(1);
    return decode_uvarint_ascending(b, v);
}

int encode_datetime_ascending(const uint64_t& v, std::string* b) {
    b->push_back(ENCODING_DATETIME_MARKER);
    return encode_uvarint_ascending(v, b);
}

int decode_datetime_ascending(std::string* b, uint64_t* v) {
    Slice s(*b);
    int ret = decode_datetime_ascending(&s, v);
    b->assign(s.data(), s.size());
    return ret;
}

int decode_datetime_ascending(Slice* b, uint64_t* v) {
    if (b->empty()) {
        // not enough bytes to decode
        return -1;
    }
    if (b->data()[0] != ENCODING_DATETIME_MARKER) {
        // not type datetime
        return -2;
    }
    b->remove_prefix(1);
    return decode_uvarint_ascending(b, v);
}

int encode_null_ascending(const bool&, std::string* b) {
    b->append(1, ENCODING_NULL_MARKER);
    return 0;
}

int decode_null_ascending(Slice* s, bool* null) {
    if (s->empty()) {
        return -1;
    }
    if ((*s)[0] == ENCODING_NULL_MARKER) {
        s->remove_prefix(1);
        *null = true;
    } else {
        *null = false;
    }
    return 0;
}

int decode_null_ascending(std::string* b, bool* null) {
    Slice s(*b);
    int ret = decode_null_ascending(&s, null);
    b->assign(s.data(), s.size());
    return ret;
}

// Check the CPU uses IEEE754
static_assert(std::numeric_limits<float>::is_iec559);
static_assert(std::numeric_limits<double>::is_iec559);

int encode_float_ascending(float v, std::string* b) {
    uint32_t encoded;
    if (v > 0) {
        encoded = alias_cast<uint32_t, float>(v) ^ (uint32_t(1) << 31);
    } else if (v < 0) {
        encoded = ~alias_cast<uint32_t, float>(v);
    } else {
        encoded = (uint32_t(1) << 31);
    }

    const uint32_t encoded_big_endian = ::htobe32(encoded);
    b->insert(
            b->end(),
            reinterpret_cast<const char*>(&encoded_big_endian),
            reinterpret_cast<const char*>(&encoded_big_endian) + sizeof(uint32_t));
    return 0;
}

int encode_double_ascending(double v, std::string* b) {
    uint64_t encoded;
    if (v > 0) {
        encoded = alias_cast<uint64_t, double>(v) ^ (uint64_t(1) << 63);
    } else if (v < 0) {
        encoded = ~alias_cast<uint64_t, double>(v);
    } else {
        encoded = (uint64_t(1) << 63);
    }

    const uint64_t encoded_big_endian = ::htobe64(encoded);
    b->insert(
            b->end(),
            reinterpret_cast<const char*>(&encoded_big_endian),
            reinterpret_cast<const char*>(&encoded_big_endian) + sizeof(uint64_t));
    return 0;
}

int decode_float_ascending(Slice* b, float* v) {
    if (b->size() < sizeof(uint32_t)) {
        return -1;
    }

    uint32_t encoded_big_endian;
    ::memcpy(&encoded_big_endian, b->data(), sizeof(uint32_t));
    b->remove_prefix(sizeof(uint32_t));

    const uint32_t encoded = ::be32toh(encoded_big_endian);

    const uint32_t high_bit = encoded >> 31;
    if (high_bit == 0) { // v < 0
        *v = alias_cast<float, uint32_t>(~encoded); // flip all bits
    } else { // v >= 0
        *v = alias_cast<float, uint32_t>(encoded ^ (uint32_t(1) << 31)); // flip high bit
    }

    return 0;
}

int decode_double_ascending(Slice* b, double* v) {
    if (b->size() < sizeof(uint64_t)) {
        return -1;
    }

    uint64_t encoded_big_endian;
    ::memcpy(&encoded_big_endian, b->data(), sizeof(uint64_t));
    b->remove_prefix(sizeof(uint64_t));

    const uint64_t encoded = ::be64toh(encoded_big_endian);
    const uint64_t high_bit = encoded >> 63;
    if (high_bit == 0) { // v < 0
        *v = alias_cast<double, uint64_t>(~encoded);
    } else { // v >= 0
        *v = alias_cast<double, uint64_t>(encoded ^ (uint64_t(1) << 63));
    }

    return 0;
}

// ============================================================================
//                    end of sorting encoding, key encoding
// ============================================================================

// ============================================================================
//                         nonsorting encoding, value encoding
// ============================================================================

/**
 * Big endian encoding
 */
int encode_uvarint_nonsorting(uint64_t v, std::string* b) {
    constexpr uint64_t one = 1L;
    byte buf[11];
    // mark the length to be copied to b
    std::size_t cnt = 0;
    if (v < (one << 7)) {
        cnt = 1;
        buf[0] = static_cast<byte>(v);
    } else if (v < (one << 14)) {
        cnt = 2;
        buf[0] = static_cast<byte>(0x80 | (v >> 7));
        buf[1] = static_cast<byte>(0x7f & (v));
    } else if (v < (one << 21)) {
        cnt = 3;
        buf[0] = static_cast<byte>(0x80 | (v >> 14));
        buf[1] = static_cast<byte>(0x80 | (v >> 7));
        buf[2] = static_cast<byte>(0x7f & (v));
    } else if (v < (one << 28)) {
        cnt = 4;
        buf[0] = static_cast<byte>(0x80 | (v >> 21));
        buf[1] = static_cast<byte>(0x80 | (v >> 14));
        buf[2] = static_cast<byte>(0x80 | (v >> 7));
        buf[3] = static_cast<byte>(0x7f & (v));
    } else if (v < (one << 35)) {
        cnt = 5;
        buf[0] = static_cast<byte>(0x80 | (v >> 28));
        buf[1] = static_cast<byte>(0x80 | (v >> 21));
        buf[2] = static_cast<byte>(0x80 | (v >> 14));
        buf[3] = static_cast<byte>(0x80 | (v >> 7));
        buf[4] = static_cast<byte>(0x7f & (v));
    } else if (v < (one << 42)) {
        cnt = 6;
        buf[0] = static_cast<byte>(0x80 | (v >> 35));
        buf[1] = static_cast<byte>(0x80 | (v >> 28));
        buf[2] = static_cast<byte>(0x80 | (v >> 21));
        buf[3] = static_cast<byte>(0x80 | (v >> 14));
        buf[4] = static_cast<byte>(0x80 | (v >> 7));
        buf[5] = static_cast<byte>(0x7f & (v));
    } else if (v < (one << 49)) {
        cnt = 7;
        buf[0] = static_cast<byte>(0x80 | (v >> 42));
        buf[1] = static_cast<byte>(0x80 | (v >> 35));
        buf[2] = static_cast<byte>(0x80 | (v >> 28));
        buf[3] = static_cast<byte>(0x80 | (v >> 21));
        buf[4] = static_cast<byte>(0x80 | (v >> 14));
        buf[5] = static_cast<byte>(0x80 | (v >> 7));
        buf[6] = static_cast<byte>(0x7f & (v));
    } else if (v < (one << 56)) {
        cnt = 8;
        buf[0] = static_cast<byte>(0x80 | (v >> 49));
        buf[1] = static_cast<byte>(0x80 | (v >> 42));
        buf[2] = static_cast<byte>(0x80 | (v >> 35));
        buf[3] = static_cast<byte>(0x80 | (v >> 28));
        buf[4] = static_cast<byte>(0x80 | (v >> 21));
        buf[5] = static_cast<byte>(0x80 | (v >> 14));
        buf[6] = static_cast<byte>(0x80 | (v >> 7));
        buf[7] = static_cast<byte>(0x7f & (v));
    } else if (v <= (one << 63)) {
        cnt = 9;
        buf[0] = static_cast<byte>(0x80 | (v >> 56));
        buf[1] = static_cast<byte>(0x80 | (v >> 49));
        buf[2] = static_cast<byte>(0x80 | (v >> 42));
        buf[3] = static_cast<byte>(0x80 | (v >> 35));
        buf[4] = static_cast<byte>(0x80 | (v >> 28));
        buf[5] = static_cast<byte>(0x80 | (v >> 21));
        buf[6] = static_cast<byte>(0x80 | (v >> 14));
        buf[7] = static_cast<byte>(0x80 | (v >> 7));
        buf[8] = static_cast<byte>(0x7f & (v));
    } else if (v <= std::numeric_limits<uint64_t>::max()) {
        cnt = 10;
        buf[0] = static_cast<byte>(0x80 | (v >> 63));
        buf[1] = static_cast<byte>(0x80 | (v >> 56));
        buf[2] = static_cast<byte>(0x80 | (v >> 49));
        buf[3] = static_cast<byte>(0x80 | (v >> 42));
        buf[4] = static_cast<byte>(0x80 | (v >> 35));
        buf[5] = static_cast<byte>(0x80 | (v >> 28));
        buf[6] = static_cast<byte>(0x80 | (v >> 21));
        buf[7] = static_cast<byte>(0x80 | (v >> 14));
        buf[8] = static_cast<byte>(0x80 | (v >> 7));
        buf[9] = static_cast<byte>(0x7f & (v));
    } else {
        // should not be reachable
        return -2;
    }
    b->append(buf, buf + cnt);
    return 0;
}

int decode_uvarint_nonsorting(Slice* s, uint64_t* v) {
    std::size_t len = static_cast<std::size_t>(s->size());

    if (len == 0) {
        // not enough data to decode
        return -1;
    }

    uint64_t tmp = 0xff;
    uint64_t val = 0;
    std::size_t idx = 0;
    const char* slice_data = s->data();
    for (; idx < len && (tmp & 0x80); ++idx) {
        val <<= 7;
        tmp = slice_data[idx] & 0xff;
        val |= tmp & 0x7f;
    }
    if (idx == len && (tmp & 0x80)) {
        // malformed encoding, there are more bytes expected when the end of the
        // input raw bytes reached
        return -2;
    }

    *v = val;
    s->remove_prefix(idx);
    return 0 ;
}

/**
 * Alternative of `decode_uvarint_nonsorting`, for testing only
 *
 */
int decode_uvarint_nonsorting(std::string* b, uint64_t* v) {
    Slice s(*b);
    int ret = decode_uvarint_nonsorting(&s, v);
    b->assign(s.data(), s.size());
    return ret;
}

/**
 * Encoding for int used by protocol buffer, little endian
 */
int encode_varint_nonsorting(int64_t v, std::string* b) {
    // cast int to uint to avoid problem of platform-dependent right-shift:
    // for signed int, different platforms/compilers may not act the same,
    // some act as arithmetical right-shift, some act as logical right-shift
    // assert logical right-shift for unsigned int
    static_assert((static_cast<uint64_t>(0xffffffffffffffffL) >> 1)
                    == static_cast<uint64_t>(0x7fffffffffffffffL),
                    "logical right-shift for unsigned int should be supported");

    uint64_t val = static_cast<uint64_t>(v);
    std::size_t cnt = 0;
    byte buf[11];
    constexpr static const uint64_t mask = 0xffffffffffffff80L;
    while (true) {
        if ((val & mask) == 0) {
            buf[cnt++] = static_cast<byte>(val);
            break;
        } else {
            buf[cnt++] = static_cast<byte>((val & 0x7F) | 0x80);
            val >>= 7;
        }
    }
    b->append(buf, buf + cnt);
    return 0;
}

int decode_varint_nonsorting(Slice* s, int64_t* v) {
    std::size_t idx = 0;
    std::size_t len = s->size();
    int64_t tmp = 0xff;
    int64_t val = 0;
    for (; idx < len && tmp >= 0x80; ++idx) {
        tmp = static_cast<int64_t>(s->data()[idx] & 0xff);
        val += (tmp & 0x7f) << (7 * idx);
    }
    if (idx == len && tmp >= 0x80) {
        // malformed encoding
        return -1;
    }
    *v = val;
    s->remove_prefix(idx);
    return 0;
}

/**
 * for testing only
 */
int decode_varint_nonsorting(std::string* b, int64_t* v) {
    Slice s(*b);
    int ret = decode_varint_nonsorting(&s, v);
    b->assign(s.data(), s.size());
    return ret;
}

int encode_string_nonsorting(const std::string& str, std::string* b,
                            std::size_t* len) {
    return encode_bytes_nonsorting(str, b, len);
}

int decode_string_nonsorting(std::string* b, std::size_t len,
                            std::string* str) {
    Slice s(*b);
    int ret = decode_string_nonsorting(&s, len, str);
    b->assign(s.data(), s.size());
    return ret;
}

int decode_string_nonsorting(Slice* s, std::size_t len, std::string* str) {
    return decode_bytes_nonsorting(s, len, str);
}

int encode_bytes_nonsorting(const std::string& str, std::string* b,
                            std::size_t* len) {
    if (len != nullptr) {
        *len = str.size();
    }
    b->append(str.data(), str.size());
    return 0;
}

int decode_bytes_nonsorting(std::string* b, std::size_t len, std::string* str) {
    Slice s(*b);
    int ret = decode_bytes_nonsorting(&s, len, str);
    b->assign(s.data(), s.size());
    return ret;
}

int decode_bytes_nonsorting(Slice* s, std::size_t len, std::string* str) {
    if (s->size() < len) {
        // not enough data to decode
        return -1;
    }
    str->append(s->data(), len);
    s->remove_prefix(len);
    return 0;
}

/**
 * Encodes integral type with fixed length, the length of the encoded value is
 * the same as sizeof(T), the output encoding is big endian.
 *
 * @param v value to encode, supported type are: int32_t int64_t char, etc
 * @param b output param
 * @return 0 if succeeded, otherwise non-zero
 */
template<typename T>
static inline int encode_fixed_int_nonsorting(T v, std::string* b) {
    static_assert(std::is_integral<T>::value, "integral type required");
    byte buf[sizeof(T)];
    int base = sizeof(T) << 3; // * 8
    for (int i = 0; i < sizeof(T); ++i) {
        base -= 8;
        buf[i] = static_cast<byte>(v >> base);
    }
    b->append(buf, buf + sizeof(T));
    return 0;
}

/**
 * Decodes integral type which is encoded with `encode_fixed_int_nonsorting()`
 *
 * @param s slice contains raw bytes to decode
 * @param v output param, supported type are: int32_t int64_t char, etc
 * @return 0 if succeeded, otherwise non-zero
 */
template<typename T>
static inline int decode_fixed_int_nonsorting(Slice* s, T* v) {
    static_assert(std::is_integral<T>::value, "integral type required");
    if (s->size() < sizeof(*v)) {
        // not enough to decode
        return -1;
    }
    *v = 0;
    for (int i = 0; i < sizeof(*v); ++i) {
        *v <<= 8;
        *v |= static_cast<byte>(s->data()[i]);
    }
    s->remove_prefix(sizeof(*v));
    return 0;
}

int encode_fixed_uint8_nonsorting(uint8_t v, std::string* b) {
    return encode_fixed_int_nonsorting(v, b);
}

int decode_fixed_uint8_nonsorting(Slice* s, uint8_t* v) {
    return decode_fixed_int_nonsorting(s, v);
}

int encode_fixed_int32_nonsorting(int32_t v, std::string* b) {
    return encode_fixed_int_nonsorting(v, b);
}

int decode_fixed_int32_nonsorting(std::string* b, int32_t* v) {
    Slice s(*b);
    int ret = decode_fixed_int32_nonsorting(&s, v);
    b->assign(s.data(), s.size());
    return ret;
}

int decode_fixed_int32_nonsorting(Slice* s, int32_t* v) {
    return decode_fixed_int_nonsorting(s, v);
}

int encode_fixed_uint32_nonsorting(uint32_t v, std::string* b) {
    return encode_fixed_int_nonsorting(v, b);
}

int decode_fixed_uint32_nonsorting(Slice* s, uint32_t* v) {
    return decode_fixed_int_nonsorting(s, v);
}

int decode_fixed_uint32_nonsorting(std::string* b, uint32_t* v) {
    Slice s(*b);
    int ret = decode_fixed_uint32_nonsorting(&s, v);
    b->assign(s.data(), s.size());
    return ret;
}

int encode_fixed_int64_nonsorting(int64_t v, std::string* b) {
    return encode_fixed_int_nonsorting(v, b);
}

int decode_fixed_int64_nonsorting(std::string* b, int64_t* v) {
    Slice s(*b);
    int ret = decode_fixed_int64_nonsorting(&s, v);
    b->assign(s.data(), s.size());
    return ret;
}

int decode_fixed_int64_nonsorting(Slice* s, int64_t* v) {
    return decode_fixed_int_nonsorting(s, v);
}

int encode_float_nonsorting(float v, std::string* b) {
    int32_t fi = alias_cast<int32_t, float>(v);
    
    return encode_fixed_int_nonsorting(fi, b);
}

int decode_float_nonsorting(std::string* b, float* v) {
    Slice s(*b);
    int ret = decode_float_nonsorting(&s, v);
    b->assign(s.data(), s.size());
    return ret;
}

int decode_float_nonsorting(Slice* s, float* v) {
    int32_t tmp = 0;
    int ret = decode_fixed_int_nonsorting(s, &tmp);

    *v = alias_cast<float, int32_t>(tmp);
    return ret;
}

int encode_double_nonsorting(double v, std::string* b) {
    int64_t di = alias_cast<int64_t, double>(v);
    
    return encode_fixed_int_nonsorting(di, b);
}

int decode_double_nonsorting(std::string* b, double* v) {
    Slice s(*b);
    int ret = decode_double_nonsorting(&s, v);
    b->assign(s.data(), s.size());
    return ret;
}

int decode_double_nonsorting(Slice* s, double* v) {
    int64_t tmp = 0;
    int ret = decode_fixed_int_nonsorting(s, &tmp);
    *v = alias_cast<double, int64_t>(tmp);
    return ret;
}

int encode_bool_nonsorting(bool v, std::string* b) {
    byte bb = alias_cast<byte, bool>(v);

    return encode_fixed_int_nonsorting(bb, b);
}

int decode_bool_nonsorting(std::string* b, bool* v) {
    Slice s(*b);
    int ret = decode_bool_nonsorting(&s, v);
    b->assign(s.data(), s.size());
    return ret;
}

int decode_bool_nonsorting(Slice* s, bool* v) {
    byte tmp = 0;
    int ret = decode_fixed_int_nonsorting(s, &tmp);

    *v = alias_cast<bool, byte>(tmp);
    return ret;
}

int encode_date_nonsorting(uint32_t v, std::string* b) {
    return encode_fixed_int_nonsorting(v, b);
}

int decode_date_nonsorting(std::string* b, uint32_t* v) {
    Slice s(*b);
    int ret = decode_date_nonsorting(&s, v);
    b->assign(s.data(), s.size());
    return ret;
}

int decode_date_nonsorting(Slice* b, uint32_t* v) {
    return decode_fixed_int_nonsorting(b, v);
}

int encode_datetime_nonsorting(uint64_t v, std::string* b) {
    return encode_fixed_int_nonsorting(v, b);
}

int decode_datetime_nonsorting(std::string* b, uint64_t* v) {
    Slice s(*b);
    int ret = decode_datetime_nonsorting(&s, v);
    b->assign(s.data(), s.size());
    return ret;
}

int decode_datetime_nonsorting(Slice* b, uint64_t* v) {
    return decode_fixed_int_nonsorting(b, v);
}

// ============================================================================
//                 end of nonsorting encoding, value encoding
// ============================================================================

// ============================================================================
//                   format associated value encoding
// ============================================================================

int encode_rocks_value_version(std::string* b, int32_t ver) {
    return encode_uvarint_nonsorting(static_cast<uint64_t>(ver), b);
}

int decode_rocks_value_version(Slice* s, int32_t* ver) {
    if (s->size() < 1) {
        return -1;
    }
    uint64_t tmp_ver = 0;
    int ret = decode_uvarint_nonsorting(s, &tmp_ver);
    *ver = static_cast<int32_t>(tmp_ver);
    return ret;
}

int encode_column_id(COLID cid, std::string* b) {
    return encode_uvarint_nonsorting(static_cast<uint64_t>(cid), b);
}

int decode_column_id(Slice* s, COLID* cid) {
    uint64_t tmp = 0;
    int ret = decode_uvarint_nonsorting(s, &tmp);
    *cid = static_cast<COLID>(tmp);
    return ret;
}

int encode_value_type(int32_t val_type, std::string* b) {
    return encode_uvarint_nonsorting(static_cast<uint64_t>(val_type), b);
}

int decode_value_type(Slice* s, int32_t* val_type) {
    if (s->empty()) {
        return -1;
    }
    uint64_t tmp_type = 0;
    int ret = decode_uvarint_nonsorting(s, &tmp_type);
    *val_type = static_cast<int32_t>(tmp_type);
    return ret;
}

int encode_value_length(std::size_t val_length, std::string* b) {
    return encode_uvarint_nonsorting(static_cast<uint64_t>(val_length), b);
}

int decode_value_length(Slice* s, std::size_t* val_length) {
    uint64_t tmp = 0;
    int ret = decode_uvarint_nonsorting(s, &tmp);
    *val_length = static_cast<std::size_t>(tmp);
    return ret;
}

int encode_value_header(COLID cid, int32_t type, std::string* b) {
    int ret = encode_column_id(static_cast<uint64_t>(cid), b);
    if (ret != 0) {
        // failed to encode column id
        return ret;
    }
    ret = encode_value_type(static_cast<uint64_t>(type), b);
    if (ret != 0) {
        // failed to encode value type
        return ret;
    }
    return 0;
}

int decode_value_header(Slice* s, COLID* cid, int32_t* type) {
    int ret = 0;
    ret = decode_column_id(s, cid);
    if (ret != 0) {
        // failed to decode column id
        return ret;
    }
    ret = decode_value_type(s, type);
    if (ret != 0) {
        // failed to decode type
        return ret;
    }
    return 0;
}

int skip_value(Slice* s, int32_t type) {
    std::size_t clen = 0;
    int ret = -1;
    switch (DataType(type)) {
        //case DataType::NULL:
        case DataType::BOOL: {
            clen = sizeof(bool);
            break;
        }
        case DataType::INT8:
        case DataType::UINT8:
        case DataType::INT16:
        case DataType::UINT16:
        case DataType::INT32:
        case DataType::UINT32:
        case DataType::INT64:
        case DataType::UINT64: {
            ret = decode_value_length(s, &clen);
            if (ret != 0) {
                return ret;
            }
            break;
        }
        case DataType::FLOAT: {
            clen = sizeof(float); // must be 4
            break;
        }
        case DataType::DOUBLE: {
            clen = sizeof(double); // must be 8
            break;
        }
        case DataType::DATE: {
            clen = sizeof(uint32_t);
            break;
        }
        case DataType::DATETIME: {
            clen = sizeof(uint64_t);
            break;
        }
        case DataType::TIME: {
            clen = sizeof(uint32_t);
            break;
        }
        case DataType::HLC:
        case DataType::TIMESTAMP: {
            clen = sizeof(uint64_t);
            break;
        }
        case DataType::STRING:
        case DataType::TEXT:
        case DataType::TEXT_GBK:
        case DataType::TEXT_GB18030:
        case DataType::BINARY:
        case DataType::UUID: { // bytes, string
            ret = decode_value_length(s, &clen);
            if (ret != 0) {
                return ret;
            }
            break;
        }
        
        default: return -1;
    }
    // skip
    s->remove_prefix(clen);
    return 0;
}

int decode_ascending_encoded_slice(Slice* s,
                                    int* type,
                                    void** val,
                                    int* encoded_len) {
    size_t size_before_decoding = s->size();
    if (size_before_decoding < 1) {
        return -1;
    }
    byte type_marker = reinterpret_cast<const byte*>(s->data())[0];
    int cnt = -1;
    int ret = -1;

    // null type
    if (type_marker == ENCODING_NULL_MARKER) {
        cnt = 1;
        if (type != nullptr) {
            *type = ENCODING_ASCENDING_NULL_TYPE;
        }
        if (encoded_len != nullptr) {
            *encoded_len = cnt;
        }
        if (val != nullptr) {
            ascending_type<ENCODING_ASCENDING_NULL_TYPE>::type* pval =
                new ascending_type<ENCODING_ASCENDING_NULL_TYPE>::type;
            *pval = true;
            *val = pval;
        }
        s->remove_prefix(cnt);
        return 0;
    }

    // varint, positive
    // one byte, positive
    // ENCODING_INT_ZERO + ENCODING_INT_SMALL == 136 + 109 == 245 == 0xf5
    constexpr byte MAX_ENCODED_1_BYTE_INT = ENCODING_INT_ZERO + ENCODING_INT_SMALL;
    if (type_marker <= MAX_ENCODED_1_BYTE_INT
            && type_marker >= ENCODING_INT_ZERO) {
        cnt = 1;
        if (type != nullptr) {
            *type = ENCODING_ASCENDING_VARINT_TYPE;
        }
        if (encoded_len != nullptr) {
            *encoded_len = cnt;
        }
        if (val != nullptr) {
            ascending_type<ENCODING_ASCENDING_UVARINT_TYPE>::type* pval =
                new ascending_type<ENCODING_ASCENDING_UVARINT_TYPE>::type;
            ret = decode_uvarint_ascending(s, pval);
            if (ret != 0) {
                delete pval;
                return ret;
            }
            *val = pval;
        } else {
            s->remove_prefix(cnt);
        }
        return 0;
    }
    for (byte i = 0; i <= 7; ++i) {
        if (type_marker == ENCODING_INT_MAX - i) {
            cnt = 9 - i;
            if (type != nullptr) {
                *type = ENCODING_ASCENDING_VARINT_TYPE;
            }
            if (encoded_len != nullptr) {
                *encoded_len = cnt;
            }
            if (val != nullptr) {
                ascending_type<ENCODING_ASCENDING_UVARINT_TYPE>::type* pval =
                    new ascending_type<ENCODING_ASCENDING_UVARINT_TYPE>::type;
                ret = decode_uvarint_ascending(s, pval);
                if (ret != 0) {
                    delete pval;
                    return ret;
                }
                *val = pval;
            } else {
                s->remove_prefix(cnt);
            }
            return 0;
        }
    }
    // varint, negative
    for (byte i = 0; i <= 7; ++i) {
        if (type_marker == ENCODING_INT_MIN + i) {
            cnt = 9 - i;
            if (type != nullptr) {
                *type = ENCODING_ASCENDING_VARINT_TYPE;
            }
            if (encoded_len != nullptr) {
                *encoded_len = cnt;
            }
            if (val != nullptr) {
                ascending_type<ENCODING_ASCENDING_VARINT_TYPE>::type* pval =
                    new ascending_type<ENCODING_ASCENDING_VARINT_TYPE>::type;
                ret = decode_varint_ascending(s, pval);
                if (ret != 0) {
                    delete pval;
                    return ret;
                }
                *val = pval;
            } else {
                s->remove_prefix(cnt);
            }
            return 0;
        }
    }

    // string or bytes array
    if (type_marker == ENCODING_BYTES_MARKER) {
        if (type != nullptr) {
            *type = ENCODING_ASCENDING_BYTES_TYPE;
        }
        if (val != nullptr) {
            ascending_type<ENCODING_ASCENDING_BYTES_TYPE>::type* pval =
                new ascending_type<ENCODING_ASCENDING_BYTES_TYPE>::type;
            ret = decode_bytes_ascending(s, pval);
            if (ret != 0) {
                delete pval;
                return ret;
            }
            *val = pval;
        } else {
            ret = decode_bytes_ascending_extract(s, nullptr);
            if (ret != 0) {
                return ret;
            }
        }
        if (encoded_len != nullptr) {
            *encoded_len = size_before_decoding - s->size();
        }
        return 0;
    }

    // date and date time
    if (type_marker == ENCODING_DATE_MARKER) {
        if (type != nullptr) {
            *type = ENCODING_ASCENDING_DATE_TYPE;
        }
        if (val != nullptr) {
            ascending_type<ENCODING_ASCENDING_DATE_TYPE>::type* pval =
                new ascending_type<ENCODING_ASCENDING_DATE_TYPE>::type;
            ret = decode_date_ascending(s, pval);
            if (ret != 0) {
                delete pval;
                return ret;
            }
            *val = pval;
        } else {
            s->remove_prefix(1);
            ret = decode_ascending_encoded_slice(s, nullptr, nullptr, nullptr);
            if (ret != 0) {
                return ret;
            }
        }
        if (encoded_len != nullptr) {
            *encoded_len = size_before_decoding - s->size();
        }
        return 0;
    }
    if (type_marker == ENCODING_DATETIME_MARKER) {
        if (type != nullptr) {
            *type = ENCODING_ASCENDING_DATETIME_TYPE;
        }
        if (val != nullptr) {
            ascending_type<ENCODING_ASCENDING_DATETIME_TYPE>::type* pval =
                new ascending_type<ENCODING_ASCENDING_DATETIME_TYPE>::type;
            ret = decode_datetime_ascending(s, pval);
            if (ret != 0) {
                delete pval;
                return ret;
            }
            *val = pval;
        } else {
            s->remove_prefix(1);
            ret = decode_ascending_encoded_slice(s, nullptr, nullptr, nullptr);
            if (ret != 0) {
                return ret;
            }
        }
        if (encoded_len != nullptr) {
            *encoded_len = size_before_decoding - s->size();
        }
        return 0;
    }

    return -1;
}

int skip_key(Slice* s) {
    return decode_ascending_encoded_slice(s, nullptr, nullptr, nullptr);
}

int encode_int_value(int64_t v, std::string* b) {
    std::string bv;
    int ret = encode_varint_nonsorting(v, &bv);
    if (ret != 0) {
        return ret;
    }
    // encode length first
    ret = encode_uvarint_nonsorting(bv.size(), b);
    if (ret != 0) {
        return ret;
    }
    // append varint encoding result
    b->append(bv.data(), bv.size());
    return 0;
}

int decode_int_value(Slice* s, int64_t* v) {
    // clen is not used here
    std::size_t clen = 0;
    int ret = decode_value_length(s, &clen);
    if (ret != 0) {
        return ret;
    }
    return decode_varint_nonsorting(s, v);
}

int encode_bool_value(bool v, std::string* b) {
    return encode_bool_nonsorting(v, b);
}

int decode_bool_value(Slice* s, bool* v) {
    return decode_bool_nonsorting(s, v);
}

int encode_float_value(float v, std::string* b) {
    return encode_float_nonsorting(v, b);
}

int decode_float_value(Slice* s, float* v) {
    return decode_float_nonsorting(s, v);
}

int encode_double_value(double v, std::string* b) {
    return encode_double_nonsorting(v, b);
}

int decode_double_value(Slice* s, double* v) {
    return decode_double_nonsorting(s, v);
}

int encode_bytes_value(const std::string& v, std::string* b) {
    int ret = encode_uvarint_nonsorting(v.size(), b);
    if (ret != 0) {
        // failed to encode length
        return ret;
    }
    ret = encode_bytes_nonsorting(v, b);
    if (ret != 0) {
        // failed to encode bytes
        return ret;
    }
    return 0;
}

int decode_bytes_value(Slice* s, std::string* v) {
    uint64_t len = 0;
    int ret = decode_uvarint_nonsorting(s, &len);
    if (ret != 0) {
        // failed to encode length
        return ret;
    }
    ret = decode_bytes_nonsorting(s, len, v);
    if (ret != 0) {
        // failed to encode bytes
        return ret;
    }
    return 0;
}

int encode_string_value(const std::string& v, std::string* b) {
    int ret = encode_uvarint_nonsorting(v.size(), b);
    if (ret != 0) {
        // failed to encode length
        return ret;
    }
    ret = encode_string_nonsorting(v, b);
    if (ret != 0) {
        // failed to encode string
        return ret;
    }
    return 0;
}

int decode_string_value(Slice* s, std::string* v) {
    uint64_t len = 0;
    int ret = decode_uvarint_nonsorting(s, &len);
    if (ret != 0) {
        // failed to encode length
        return ret;
    }
    ret = decode_string_nonsorting(s, len, v);
    if (ret != 0) {
        // failed to encode string
        return ret;
    }
    return 0;
}

int decode_string_value(Slice* s, std::string_view* v) {
    uint64_t len = 0;
    int ret = decode_uvarint_nonsorting(s, &len);
    if (ret != 0) {
        // failed to encode length
        return ret;
    }

    *v = {s->data(), len};

    s->remove_prefix(len);

    return 0;
}

int encode_date_value(uint32_t v, std::string* b) {
    return encode_date_nonsorting(v, b);
}

int decode_date_value(Slice* s, uint32_t* v) {
    return decode_date_nonsorting(s, v);
}

int encode_datetime_value(uint64_t v, std::string* b) {
    return encode_datetime_nonsorting(v, b);
}

int decode_datetime_value(Slice* s, uint64_t* v) {
    return decode_datetime_nonsorting(s, v);
}

template <typename T>
static std::function<void(void*)> deleter() {
    return [](void* p) { delete reinterpret_cast<T*>(p); };
}

int decode_nonsorting_encoded_slice_v1(
        Slice* nonsorting_slice,
        COLID* cid,
        int* type,
        std::unique_ptr<void, std::function<void(void*)>>* val,
        int* cid_len,
        int* type_len,
        int* val_len) {
    auto& s = nonsorting_slice;

    int ret = -1;
    int type_val = -1;
    COLID cid_val = -1;
    void* pval = nullptr;

    size_t cid_len_val = 0;
    size_t type_len_val = 0;
    size_t val_len_val = 0;
    typedef std::function<void(void*)> Deleter;
    std::unique_ptr<void, Deleter> unique_val;

    size_t slice_len = s->size();

    ret = decode_column_id(s, &cid_val);
    if (ret != 0) {
        return ret;
    }
    cid_len_val = slice_len - s->size();
    slice_len = s->size();

    ret = decode_value_type(s, &type_val);
    if (ret != 0) {
        return ret;
    }
    type_len_val = slice_len - s->size();
    slice_len = s->size();

    std::shared_ptr<void> __defer(
        nullptr, [ret, cid, type, val, cid_len, type_len, val_len, &cid_val, &type_val,
                  &unique_val, &cid_len_val, &type_len_val, &val_len_val](...) {
            if (ret != 0) return;
            // assert that pval != nullptr
            cid != nullptr ? *cid = cid_val : 0;
            type != nullptr ? *type = type_val : 0;
            val != nullptr ? *val = std::move(unique_val) : unique_val;
            cid_len != nullptr ? *cid_len = cid_len_val : 0;
            type_len != nullptr ? *type_len = type_len_val : 0;
            val_len != nullptr ? *val_len = val_len_val : 0;
        });

    switch (DataType(type_val)) {
        //case DataType::NULL:
        case DataType::BOOL:
        case DataType::INT8:
        case DataType::UINT8:
        case DataType::INT16:
        case DataType::UINT16:
        case DataType::INT32:
        case DataType::UINT32:
        case DataType::INT64:
        case DataType::UINT64:
        case DataType::HLC:
            pval = new int64_t;
            unique_val = std::move(
                    std::unique_ptr<void, Deleter>(static_cast<void*>(pval),
                    deleter<int64_t>()));
            ret = decode_int_value(s, reinterpret_cast<int64_t*>(pval));
            break;
        case DataType::TIME:
        case DataType::DATE: {
            pval = new uint32_t;
            unique_val = std::move(
                    std::unique_ptr<void, Deleter>(static_cast<void*>(pval),
                    deleter<uint32_t>()));
            ret = decode_date_value(s, reinterpret_cast<uint32_t*>(pval));
            break;
        }
        case DataType::TIMESTAMP:
        case DataType::DATETIME: {
            pval = new uint64_t;
            unique_val = std::move(
                    std::unique_ptr<void, Deleter>(static_cast<void*>(pval),
                    deleter<uint64_t>()));
            ret = decode_datetime_value(s, reinterpret_cast<uint64_t*>(pval));
            break;
        }
        case DataType::FLOAT:
        case DataType::DOUBLE: {
            pval = new double;
            unique_val = std::move(
                    std::unique_ptr<void, Deleter>(static_cast<void*>(pval),
                    deleter<double>()));
            ret = decode_double_value(s, reinterpret_cast<double*>(pval));
            break;
        }
        case DataType::UUID:
        case DataType::STRING:
        case DataType::TEXT:
        case DataType::TEXT_GBK:
        case DataType::TEXT_GB18030:
        case DataType::BINARY: {
            pval = new std::string();
            unique_val = std::move(
                    std::unique_ptr<void, Deleter>(static_cast<void*>(pval),
                    [](void* p) { delete reinterpret_cast<std::string*>(p); }));
            ret = decode_string_value(s, reinterpret_cast<std::string*>(pval));
            break;
        }
        default: {
            ret = -1;
        };
    }
    val_len_val = slice_len - s->size();

    return ret;
}

// ============================================================================
//                   end of format associated value encoding
// ============================================================================

int Codec::decode_key_prefix(Slice* key, int64_t* prefix) {
    if (decode_varint_ascending(key, prefix) != 0) {
        return -1;
    }

    return 0;
}

int Codec::decode_coloumn_family_id(Slice* key, int64_t* column_family_id) {
    if (key->empty()) {
        return -1;
    }
    int64_t len = 0;
    Slice len_slice(key->data() + key->size() - 1, 1);
    if (decode_varint_ascending(&len_slice, &len) != 0) {
        return -1;
    }
    key->remove_suffix(1);
    if (len == 0) {
        *column_family_id = 0;
        return 0;
    }
    if (key->size() < len) {
        return -1;
    }
    Slice column_family_id_slice(key->data() + key->size() - len, len);
    if (decode_varint_ascending(&column_family_id_slice, column_family_id) != 0) {
        return -1;
    }
    key->remove_suffix(len);
    return 0;
}

int Codec::print_one_key_slot(Slice* key_slice, std::stringstream* stream) {
    int type = 0;
    void* val = nullptr;
    int ret = decode_ascending_encoded_slice(key_slice, &type, &val, nullptr);
    if (ret != 0) {
        (*stream) << "BAD-COLUMN-ENCODINE";
        return ret;
    }

    switch (type) {
    case ENCODING_ASCENDING_VARINT_TYPE:
        (*stream) << *(reinterpret_cast<KEY_VARINT*>(val));
        delete reinterpret_cast<KEY_VARINT*>(val);
        break;
    case ENCODING_ASCENDING_UVARINT_TYPE:
        (*stream) << *(reinterpret_cast<KEY_UVARINT*>(val));
        delete reinterpret_cast<KEY_UVARINT*>(val);
        break;
    case ENCODING_ASCENDING_BYTES_TYPE:
        (*stream) << "'" << *(reinterpret_cast<KEY_BYTES*>(val)) << "'";
        delete reinterpret_cast<KEY_BYTES*>(val);
        break;
    case ENCODING_ASCENDING_STRING_TYPE:
        (*stream) << "'" << *(reinterpret_cast<KEY_STRING*>(val)) << "'";
        delete reinterpret_cast<KEY_STRING*>(val);
        break;
    case ENCODING_ASCENDING_DATE_TYPE:
        (*stream) << "'" << *(reinterpret_cast<KEY_DATE*>(val)) << "'";
        delete reinterpret_cast<KEY_DATE*>(val);
        break;
    case ENCODING_ASCENDING_DATETIME_TYPE:
        (*stream) << "'" << *(reinterpret_cast<KEY_DATETIME*>(val)) << "'";
        delete reinterpret_cast<KEY_DATETIME*>(val);
        break;
    default:
        (*stream) << "BAD-COLUMN-ENCODING";
        return -1;
    }

    return ret;
}

std::string Codec::debug_key(Slice* key_slice) {
    int64_t prefix;
    if (decode_key_prefix(key_slice, &prefix) != 0) {
        return std::string("BAD-ENCODING.");
    }

    // remove column-family id
    int64_t cf_id = 0;
    if (decode_coloumn_family_id(key_slice, &cf_id)) {
        return std::string("BAD-ENCODING.");
    }

    std::stringstream stream;
    stream << "(";
    int err_num = 0;
    while (!err_num) {
        int err_num = print_one_key_slot(key_slice, &stream);
        if (err_num != 0) {
            break;
        }
        if (key_slice->empty()) {
            break;
        }
        stream << ",";
    }
    if (err_num != 0) {
        stream << "...";
    }
    stream << ")";
    return stream.str();
}

std::string Codec::debug_key(const Slice& key_slice) {
    Slice tmp_slice(key_slice);
    return debug_key(&tmp_slice);
}

std::string Codec::debug_key(const std::string& s ) {
    Slice tmp_slice(s);
    return debug_key(&tmp_slice);
}

}
}

// vim: et tw=80 ts=4 sw=4
