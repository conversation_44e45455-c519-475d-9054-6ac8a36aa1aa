/**
 * MOCHOW - A Vector Database System.
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: la<PERSON><PERSON><PERSON>(<EMAIL>)
 * Date: 2024/06/14
 * Desciption: implementations of compaction scheduler;
 *
 */

#include "core/src/datanode/scheduler/compaction_scheduler.h"
#include "core/src/common/common.h"
#include "core/src/datanode/flags.h"
#include "core/src/datanode/tablet_manager.h"
#include "core/src/datanode/meta_cache.h"

namespace mochow::datanode {

CompactionScheduler* g_compaction_scheduler = new CompactionScheduler();

void CompactionScheduler::run() {
    auto last_exec_compaction_ts = base::gettimeofday_s();
    while (!_is_stopped) {
        const auto delta =  base::gettimeofday_s() - last_exec_compaction_ts;
        if (delta < FLAGS_compaction_thread_work_interval_s) {
            std::this_thread::sleep_for(std::chrono::seconds(1));
            continue;
        }

        const auto tablet_controls = g_tablet_manager->list_tablet_controls();
        for (const auto& tablet_control : tablet_controls) {
            try_compact(tablet_control);
            if (_is_stopped) {
                break;
            }
   
        }

        last_exec_compaction_ts = base::gettimeofday_s();
    }
}

Status CompactionScheduler::try_compact(const TabletControlRef& tablet_control) {
    auto tablet = tablet_control->get_tablet();
    LOG_AND_ASSERT(tablet != nullptr);
    auto schema_hierarchy = g_schema_cache->get_schema_hierarchy(tablet->get_table_id());
    if (schema_hierarchy == nullptr) {
        //Only will happen when table of tablet had been dropped.
        LOG(NOTICE) << "Skip compact tablet since tablet has already been dropped,"
                    << " table_id:" << tablet->get_table_id()
                    << " tp_id:"<< tablet->get_tp_id();
        return Status();
    }

    bool vector_minor_compaction_enabled = true;
    bool vector_major_compaction_enabled = true;
    // Minor and major compaction will be triggered by build_vector_index if the vector schema has a dense vector index and vector index auto-build is enabled.
    auto moss_schema = schema_hierarchy->vector_schema;
    const auto& vector_indexes = moss_schema->get_vector_indexes();
    bool has_dense_vector_index = false;
    for (const auto& vector_index_schema : vector_indexes) {
        const auto& index_type = vector_index_schema->get_index_param().index_type;
        if (vindex::IndexType::is_valid_dense_vector_index_type(index_type)) {
            has_dense_vector_index = true;
            vector_major_compaction_enabled = false;
            break;
        }
    }
    
    bool auto_build_enable = false;
    auto table_config = g_dn_meta_cache->get_table_config(tablet->get_table_id());
    if (table_config != nullptr) {
        auto_build_enable = table_config->has_auto_build_strategy();
    }

    if (has_dense_vector_index && auto_build_enable) {
        LOG(NOTICE) << "Tablet has dense vector index in schema, moss compaction will be done in rebuild_vector_index,"
                    << " table_id:" << tablet->get_table_id()
                    << " tp_id:"<< tablet->get_tp_id();
        vector_minor_compaction_enabled = false;
    }

    auto status = tablet->try_compact(schema_hierarchy, vector_major_compaction_enabled, vector_minor_compaction_enabled);
    if (!status.ok()) {
        return status;
    }
    // Create a newer snapshot on tablet;
    status = tablet_control->create_state_machine_snapshot();
    if (!status.ok()) {
        LOG(WARNING) << "Fail to create tablet snapshot after compaction is done,"
                     << " table_id:" << tablet->get_table_id()
                     << " tp_id:" << tablet->get_tp_id()
                     << " status:" << status;
    } else {
        LOG(NOTICE) << "Succeed to create tablet snapshot after compaction is done,"
                     << " table_id:" << tablet->get_table_id()
                    << " tp_id:" << tablet->get_tp_id();
    }
   return status;
}
}

