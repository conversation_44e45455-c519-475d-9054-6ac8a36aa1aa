/***************************************************************************
 *
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/
#pragma once

#include <thread>
#include <atomic>
#include <vector>
#include <algorithm>

#include "core/src/zhuque/tablet.h"

namespace mochow::datanode {

class GCScheduler {
public:
    GCScheduler() = default;
    ~GCScheduler() = default;

    void start(base::EndPoint local_addr) {
        _local_addr = local_addr;
        _is_stopped = false;
        _thread = std::thread{std::bind(&GCScheduler::run, this)};
    }

    void stop() {
        _is_stopped = true;
    }
    void join() {
        if (_thread.joinable()) {
            _thread.join();
        }
    }

    void add_garbage_tablet(const zq::TabletRef& tablet) {
        common::ScopedButex guard(_mutex);
        _garbage_tablets.emplace(std::make_pair(tablet->get_tablet_id(), tablet));
    }

    NODISCARD int size() {
        common::ScopedButex guard(_mutex);
        return _garbage_tablets.size();
    }

    void clear() {
        common::ScopedButex guard(_mutex);
        _garbage_tablets.clear();
    }

private:
    void run();

    bool list_all_table_states(std::map<TBLID, std::string>* table_states);
    bool list_databases(std::set<DBID>* db_ids);

    void exec_gc();
    void exec_sstable_memory_pool_gc();
    void exec_table_dir_gc(const std::map<TBLID, std::string>& table_states);
    void exec_db_dir_gc(const std::set<DBID>& db_ids);

    bool sync_and_diff_tablets(const std::map<TBLID, std::string>& table_states);
    bool refresh_tablets(std::set<TabletID>& tablets);

    bool could_be_gc(const zq::TabletRef& tablet) const {
        return tablet.use_count() == 1 && tablet->could_be_gc();
    }

    bool is_digit(const std::string& str) {
        return !str.empty() && std::all_of(str.begin(), str.end(), ::isdigit);
    }
    
private:
    common::Butex _mutex;
    base::EndPoint _local_addr;
    std::thread _thread;
    std::atomic<bool> _is_stopped = true;
    std::map<TabletID, zq::TabletRef> _garbage_tablets;
};

extern GCScheduler* g_gc_scheduler;

}  // namespace mochow::datanode
