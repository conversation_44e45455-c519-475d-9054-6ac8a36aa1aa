/**
 * MOCHOW - A Vector Database System.
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON> (<PERSON><PERSON><PERSON><PERSON>@baidu.com)
 * Date: 2024/08/30
 * Desciption: Definitions for DataNode Healthy Check Scheduler
 *
 */

#pragma once

#include <atomic>
#include <thread>
#include <string>
#include <vector>
#include <functional>
#include <memory>

#include "core/src/common/using.h"
#include "core/src/common/logging.h"
#include "core/src/zhuque/row.h"
#include "core/src/datanode/flags.h"
#include "core/src/datanode/tablet_control.h"

namespace mochow::datanode {

class HealthyCheckScheduler final {
public:
    HealthyCheckScheduler() = default;

    ~HealthyCheckScheduler() = default;

    void start() {
        if (_is_stopped) {
            LOG(NOTICE) << "Start healthy-check scheduler";
            _is_stopped = false;
            _thread = std::thread{ std::bind(&HealthyCheckScheduler::run, this) };
        }
    }

    void stop() {
        LOG(NOTICE) << "Stop healthy-check scheduler";
        _is_stopped = true;
    }

    void join() {
        if (_thread.joinable()) {
            LOG(NOTICE) << "Join healthy-check scheduler";
            _thread.join();
        }
    }

private:
    void run();

    void healthy_check();

    void do_tablet_healthy_check(TabletControlRef tablet_control);

    void do_tablet_sub_range_healthy_check(TabletControlRef tablet_control,
                                           zq::RowKeyRef min_key,
                                           zq::RowKeyRef max_key);

    void update_tablet_row_cache(TabletControlRef tablet_control,
                                 const std::vector<zq::RowKey>& keys);

private:
    // print search pool info periodically
    void thread_pool_check();

private:
    std::atomic<bool> _is_stopped = true;

    std::thread _thread;
};

inline std::unique_ptr<HealthyCheckScheduler> g_healthy_check_scheduler = std::make_unique<HealthyCheckScheduler>();

} // namespace mochow::datanode
