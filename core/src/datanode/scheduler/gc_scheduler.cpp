#include "gc_scheduler.h"

#include <filesystem>

#include "core/src/common/common.h"
#include "core/src/common/table_state.h"
#include "core/src/common/master_tracker.h"
#include "core/src/common/rpc/rpc_stub.h"
#include "core/src/datanode/flags.h"
#include "core/src/datanode/tablet_manager.h"
#include "core/src/sstable/page_memory_pool.h"

#include "baidu/vdb/mochow/core/src/proto/common.pb.h"
#include "baidu/vdb/mochow/core/src/proto/master.pb.h"

namespace fs = std::filesystem;

namespace mochow::datanode {

GCScheduler* g_gc_scheduler = new GCScheduler();

const int64_t log_every_n = 10;

void GCScheduler::run() {
    while (!_is_stopped) {
        std::this_thread::sleep_for(std::chrono::seconds(FLAGS_tablet_gc_interval_s));

        std::map<TBLID, std::string> table_states;
        bool list_table_flag = list_all_table_states(&table_states);

        std::set<DBID> db_ids;
        bool list_db_flag = list_databases(&db_ids);

        // get diff tablets
        if (list_table_flag) {
            sync_and_diff_tablets(table_states);
        }

        // gc tablet first
        exec_gc();

        // gc table
        if (list_table_flag) {
            exec_table_dir_gc(table_states);
        }

        // gc database
        if (list_db_flag) {
            exec_db_dir_gc(db_ids);
        }

        // gc memory pool
        exec_sstable_memory_pool_gc();
    }
}

void GCScheduler::exec_gc() {
    std::vector<zq::TabletRef> candidates;

    {
        // Do not execute IOs within this mutex.
        common::ScopedButex guard(_mutex);
        std::map<TabletID, zq::TabletRef>::iterator iter = _garbage_tablets.begin();
        while (iter != _garbage_tablets.end()) {
            if (could_be_gc(iter->second)) {
                candidates.push_back(iter->second);
                iter = _garbage_tablets.erase(iter);
            } else {
                ++iter;
            }
        }
    }

    for (auto iter = candidates.begin(); iter != candidates.end(); ++iter) {
        const std::string tablet_dir = (*iter)->get_tablet_dir();
        //Force to release Tablet resources;
        (*iter).reset();
        BRESULT br = io::get_file_system()->remove(tablet_dir.c_str(), 0, 0, nullptr);
        if (baidu::sfl::is_failed(br)) {
            LOG(WARNING) << "Fail to remove tablet dir: " << tablet_dir
                         << " error_code:" << br.get_value();
        } else {
            LOG(WARNING) << "Success to remove tablet dir: " << tablet_dir;
        }
    }

    LOG_EVERY_N(NOTICE, log_every_n) << "Tablet gc queue size is " << _garbage_tablets.size();
}

bool GCScheduler::list_all_table_states(std::map<TBLID, std::string>* table_states) {
    master::pb::ListTableInternalRequest request;
    master::pb::ListTableInternalResponse response;
    request.set_token(FLAGS_token);

    common::RpcCallOptions options;
    options.need_retry = false;
    options.print_trace_log_on_final_success = true;
    base::EndPoint master_addr = g_master_tracker->get_master();
    REDIRECT_MASTER_RPC_CALL(list_table_internal, master_addr, &request, &response, &options);
    if (response.status().code() != OK) {
        LOG(WARNING) << "Fail to list table state due to rpc failed, will try next time,"
                     << " rpc_status:" << common::pb2json(response.status());
        return false;
    }

    for (int i = 0; i < response.table_infos_size(); ++i) {
        std::string table_state = response.table_infos(i).state();
        TBLID table_id = response.table_infos(i).schema().table_id();
        table_states->insert(std::make_pair(table_id, table_state));
    }

    return true;
}

bool GCScheduler::list_databases(std::set<DBID>* db_ids) {
    master::pb::ListDatabaseInternalRequest request;
    master::pb::ListDatabaseInternalResponse response;
    request.set_token(FLAGS_token);

    common::RpcCallOptions options;
    options.need_retry = false;
    options.print_trace_log_on_final_success = true;
    base::EndPoint master_addr = g_master_tracker->get_master();
    REDIRECT_MASTER_RPC_CALL(list_database_internal, master_addr, &request, &response, &options);
    if (response.status().code() != OK) {
        LOG(WARNING) << "Fail to list database state due to rpc failed, will try next time,"
                     << " rpc_status:" << common::pb2json(response.status());
        return false;
    }

    for (int i = 0; i < response.db_infos_size(); ++i) {
        bool ok = db_ids->insert(response.db_infos(i).db_id()).second;
        LOG_AND_ASSERT(ok);
    }

    return true;
}

bool GCScheduler::refresh_tablets(std::set<TabletID>& tablet_ids) {
    master::pb::ListDataNodeTabletRequest request;
    request.set_token(FLAGS_token);
    request.mutable_node_addr()->set_ip(base::ip2int(_local_addr.ip));
    request.mutable_node_addr()->set_port(_local_addr.port);
    master::pb::ListDataNodeTabletResponse response;

    common::RpcCallOptions options;
    options.need_retry = false;
    options.print_trace_log_on_final_success = true;
    base::EndPoint master_addr = g_master_tracker->get_master();
    REDIRECT_MASTER_RPC_CALL(list_datanode_tablet, master_addr, &request, &response, &options);

    if (response.status().code() != OK) {
        LOG(WARNING) << "Fail to list datanode tablets from master";
        return false;
    }

    for (int i = 0; i < response.tablet_ids_size(); ++i) {
        const mochow::pb::PTabletId& p_tablet_id = response.tablet_ids(i);
        auto tablet_id = make_tablet_id(p_tablet_id.table_id(), p_tablet_id.tp_id());
        bool ok = tablet_ids.insert(tablet_id).second;
        LOG_AND_ASSERT(ok);
    }

    return true;
}

bool GCScheduler::sync_and_diff_tablets(const std::map<TBLID, std::string>& table_states) {
    std::set<TabletID> master_tablet_ids;
    if (!refresh_tablets(master_tablet_ids)) {
        return false;
    }

    // Check whether every local tablet exists in master.
    for (const auto& tablet_id : master_tablet_ids) {
        if (!g_tablet_manager->is_tablet_exist(tablet_id)) {
            auto table_id = tblid(tablet_id);
            if (table_states.count(table_id) == 0) {
                LOG(ERROR) << "Find an unexpected status: master has tablet but has no its table info,"
                           << " please check it again!"
                           << " table_id:" << table_id << " tp_id:" << tpid(tablet_id);
                continue;
            }
            if (table_states.at(table_id) == common::TableState::NORMAL) {
                LOG(ERROR) << "Find a possibly lost tablet in a normal table:"
                           << " the tablet exists in master but not exists in datanode,"
                           << " please check it again!"
                           << " table_id:" << table_id << " tp_id:" << tpid(tablet_id);
            }
        }
    }

    std::vector<TabletID> datanode_tablet_ids = g_tablet_manager->list_tablet_ids();
    for (const auto& tablet_id : datanode_tablet_ids) {
        auto table_id = tblid(tablet_id);

        // Exsits in master
        if (master_tablet_ids.count(tablet_id) > 0) {
            continue;
        }

        // NOT exists in master:
        // Correspoding tablet already dropped or is NOT in CREATING state: garbage
        if (table_states.count(table_id) == 0
                || table_states.at(table_id) != common::TableState::CREATING) {
            auto tablet_control = g_tablet_manager->remove_tablet(tablet_id);
            if (tablet_control != nullptr) {
                tablet_control->destroy();
                add_garbage_tablet(tablet_control->get_tablet());
                LOG(WARNING) << "Destroy tablet and add into garbage candidates,"
                             << " table_id:" << table_id << " tp_id:" << tpid(tablet_id);
            }
        }
    }

    return true;
}

void GCScheduler::exec_sstable_memory_pool_gc() {
    if (sstable::g_block_memory_pool != nullptr) {
        sstable::g_block_memory_pool->gc(FLAGS_sstable_memory_pool_gc_interval_ms);
        LOG_EVERY_N(NOTICE, log_every_n)
            << "Succeed to execute sstable block memory pool gc, memory pool stats: "
            << sstable::g_block_memory_pool->get_stats().to_string();
    }

    if (sstable::g_row_memory_pool != nullptr) {
        sstable::g_row_memory_pool->gc(FLAGS_sstable_memory_pool_gc_interval_ms);
        LOG_EVERY_N(NOTICE, log_every_n)
            << "Succeed to execute sstable row memory pool gc, memory pool stats: "
            << sstable::g_row_memory_pool->get_stats().to_string();
    }
}

// only gc DELETED table dir, which means these TBLID will not appear in table_states.
void GCScheduler::exec_table_dir_gc(const std::map<TBLID, std::string>& table_states) {
    std::string data_dir = FLAGS_datanode_data_dir;
    if (!fs::exists(data_dir) || !fs::is_directory(data_dir)) {
        LOG(ERROR) << "Data dir does not exist or is not a directory, path:" << data_dir;
        return;
    }

    for (const auto& entry : fs::directory_iterator(data_dir)) {
        if (fs::is_directory(entry.path()) && is_digit(entry.path().filename())) {
            for (const auto& sub_entry : fs::directory_iterator(entry.path())) {
                // 1. dir is empty
                // 2. it's a deleted table
                if (fs::is_directory(sub_entry.path()) &&
                    is_digit(entry.path().filename()) &&
                    fs::is_empty(sub_entry.path()) &&
                    table_states.count(std::stoull(sub_entry.path().filename().string())) == 0) {
                    LOG(NOTICE) << "GC deleted table dir: " << sub_entry.path();
                    LOG_AND_ASSERT(fs::remove(sub_entry.path()));
                }
            }
        }
    }
}

// only gc DELETED db dir, which means these DBID will not appear in db_ids.
void GCScheduler::exec_db_dir_gc(const std::set<DBID>& db_ids) {
    std::string data_dir = FLAGS_datanode_data_dir;
    if (!fs::exists(data_dir) || !fs::is_directory(data_dir)) {
        LOG(ERROR) << "Data dir does not exist or is not a directory, path:" << data_dir;
        return;
    }

    for (const auto& entry : fs::directory_iterator(data_dir)) {
        if (fs::is_directory(entry.path()) &&
            is_digit(entry.path().filename()) &&
            fs::is_empty(entry.path()) &&
            (db_ids.find(std::stoull(entry.path().filename().string())) == db_ids.end())) {
            LOG(NOTICE) << "GC deleted database dir: " << entry.path();
            LOG_AND_ASSERT(fs::remove(entry.path()));
        }
    }
}

}  // namespace mochow::datanode
