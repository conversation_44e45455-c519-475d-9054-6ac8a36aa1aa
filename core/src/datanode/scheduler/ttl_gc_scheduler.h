/**
 * MOCHOW - A Vector Database System.
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: la<PERSON><PERSON><PERSON>(<EMAIL>)
 * Date: 2024/11/19
 *
 */

#pragma once

#include <atomic>
#include <thread>
#include <string>
#include <vector>
#include <functional>

#include "sfl/class_helper.h"

#include "core/src/common/using.h"
#include "core/src/common/logging.h"
#include "core/src/datanode/tablet_control.h"

namespace mochow::datanode {

class TTLGCScheduler final {
public:
    TTLGCScheduler() = default;

    ~TTLGCScheduler() = default;

    DISABLE_COPY_AND_MOVE(TTLGCScheduler);

    void start() {
        if (_is_stopped) {
            LOG(NOTICE) << "Start TTL gc scheduler";
            _is_stopped = false;
            _thread = std::thread{std::bind(&TTLGCScheduler::run, this)};
        }
    }

    void stop() {
        LOG(NOTICE) << "Stop TTL gc scheduler";
        _is_stopped = true;
    }

    void join() {
        if (_thread.joinable()) {
            LOG(NOTICE) << "Join TTL gc scheduler";
            _thread.join();
        }
    }

private:
    void run();

    void tablet_gc(TabletControlRef tablet_control);

    Status tablet_delete_expired_rows(const schema::SchemaHierarchyRef schema_hierarchy,
                                      TabletControlRef tablet_control,
                                      const std::vector<zq::RowRef>& expired_rows);    

private:
    std::atomic<bool> _is_stopped = true;
    std::thread _thread;
};

extern TTLGCScheduler* g_ttl_gc_scheduler;

}
