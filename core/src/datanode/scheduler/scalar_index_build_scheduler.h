/**
 * MOCHOW - A Vector Database System.
 * Copyright (c) 2024 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON> (caoshi<PERSON>@baidu.com)
 * Date: 2024/11/20
 */

#pragma once

#include <memory>
#include <atomic>
#include <thread>

#include "core/src/datanode/tablet_manager.h"
#include "core/src/datanode/flags.h"
#include "core/src/zhuque/scalar_index.h"

namespace mochow::datanode {

class ScalarIndexBuildScheduler final {
public:
    ScalarIndexBuildScheduler() = default;
    ~ScalarIndexBuildScheduler() = default;

    void start() {
        if (_is_stopped) {
            LOG(NOTICE) << "Start scalar index build scheduler";
            _is_stopped = false;
            _thread = std::thread{std::bind(&ScalarIndexBuildScheduler::run, this)};
        }
    }

    void stop() {
        LOG(NOTICE) << "Stop scalar index build scheduler";
        _is_stopped = true;
    }

    void join() {
        if (_thread.joinable()) {
            LOG(NOTICE) << "Join scalar index build scheduler";
            _thread.join();
        }
    }

private:
    void run();

private:
    static void build_scalar_index(const schema::SchemaHierarchyRef schema_hierarchy,
                                   TabletControlRef tablet_control,
                                   zq::ScalarIndexRef index);

    static Status insert_into_index(const schema::SchemaHierarchyRef schema_hierarchy,
                                    TabletControlRef tablet_control,
                                    zq::ScalarIndexRef index,
                                    const zq::RowRef row);

private:
    std::atomic<bool> _is_stopped = true;
    std::thread _thread;
};

inline std::unique_ptr<ScalarIndexBuildScheduler> g_scalar_index_build_scheduler =
        std::make_unique<ScalarIndexBuildScheduler>();
    
} // namespace mochow::datanode
