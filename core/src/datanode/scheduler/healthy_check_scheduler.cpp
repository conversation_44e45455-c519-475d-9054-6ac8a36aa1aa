/***************************************************************************
 *
 * Copyright (c) 2024 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

#include "core/src/datanode/scheduler/healthy_check_scheduler.h"

#include "core/src/datanode/tablet_manager.h"
#include "core/src/schema/schema_cache.h"
#include "core/src/common/common.h"
#include "core/src/common/flags.h"
#include "core/src/datanode/flags.h"
#include "core/src/zhuque/tablet_self_check_iterator.h"

#include "core/src/zhuque/search_thread_pool.h"

#include "baidu/vdb/mochow/core/src/proto/datanode.pb.h"

namespace mochow::datanode {

const int64_t log_every_n = 10;

void HealthyCheckScheduler::run() {
    auto target_time = base::gettimeofday_s();
    while (!_is_stopped) {
        std::this_thread::sleep_for(std::chrono::seconds(1));
        if (base::gettimeofday_s() < target_time) { continue; }
        target_time = base::gettimeofday_s() + FLAGS_self_check_thread_work_interval_s;

        if (FLAGS_enable_datanode_healthy_check_scheduler) {
            healthy_check();      
        } else {
            LOG(TRACE) << "No need to self check due to healthy check scheduler is disabled by gflag.";
        }

        thread_pool_check();
    }
}

void HealthyCheckScheduler::healthy_check() {
    auto tablet_ids = g_tablet_manager->list_tablet_ids();
    for (TabletID tablet_id : tablet_ids) {
        if (_is_stopped) {
            break;
        }

        auto tablet_control = g_tablet_manager->get_tablet_control(tablet_id);
        if (tablet_control != nullptr && tablet_control->is_leader()) {
            do_tablet_healthy_check(tablet_control);
        }
    }
}

void HealthyCheckScheduler::do_tablet_healthy_check(TabletControlRef tablet_control) {
    auto tablet = tablet_control->get_tablet();
    LOG_AND_ASSERT(tablet != nullptr);
    const auto table_id = tablet->get_table_id();
    const auto tp_id = tablet->get_tp_id();
    LOG(NOTICE) << "Begin to do healthy check for tablet, "
		<< " table_id:" << table_id
		<< " tp_id:" << tp_id;

    auto schema_hierarchy = g_schema_cache->get_schema_hierarchy(table_id);
    // table is deleted.
    if (schema_hierarchy == nullptr) {
        LOG(WARNING) << "No need to do healthy check for tablet when tablet is dropped,"
                     << " table_id:" << table_id
                     << " tp_id:" << tp_id;       
        return;
    }

    // We have not support snapshot read on tablet and it maybe inconsistent between tablet_total_row_count
    // and healthy check iterator, but it should be fine for sampling;
    size_t sample_step = 0;
    const auto tablet_total_row_count = tablet->get_row_count();
    if (zq::FLAGS_tablet_row_cache_max_num != 0 && tablet_total_row_count != 0) {
        sample_step = std::max(1UL, tablet_total_row_count / zq::FLAGS_tablet_row_cache_max_num);
    }

    size_t row_count = 0;
    std::vector<zq::RowKey> sample_row_keys;
    zq::RowKeyRef marker_min_key = nullptr;
    Status st;
    while (!_is_stopped) {
        // Table may be dropped during healthy check.
        if (!g_tablet_manager->is_tablet_exist(tablet->get_tablet_id())) {
            return;
        }
        zq::InitArgsRef args = std::make_shared<zq::InitArgs>();
        args->min_key = marker_min_key;
        args->max_key = nullptr;
        args->include_min_key = true;
        args->include_max_key = false;
        args->schema_hierarchy = schema_hierarchy;
        auto iter = tablet->create_self_check_iterator(args, schema_hierarchy);
        if (iter == nullptr) {
            LOG(WARNING) << "Fail to create tablet_self_check_iterator,"
                         << " table_id:" << table_id
                         << " tp_id:" << tp_id;
            st = Status(ERR_FAIL, "Fail to create self_check iterator");
            break;
        }

        st = iter->seek_to_first();
        if (!st.ok()) {
            if (st.is_iterator_end()) {
                st = Status();
                break;
            }

            LOG(WARNING) << "Fail to call tablet_iterator.seek_to_first(),"
                         << " table_id:" << table_id
                         << " tp_id:" << tp_id
                         << " status:" << st;
            break;
        }

        const uint64_t batch_size = FLAGS_self_check_max_batch_size;
        for (size_t index = 0; st.ok() && index < batch_size; ++index) {
            zq::RowKeyRef rowkey = nullptr;
            bool has_scalar = false;
            bool has_vector = false;
            st = iter->get(rowkey, &has_scalar, &has_vector);
            if (!st.ok()) {
                break;
            }
            LOG_AND_ASSERT(rowkey != nullptr);
            ++row_count;
            if (sample_step != 0 && row_count % sample_step == 0) {
                // Add this rowkey to samples;
                sample_row_keys.emplace_back(*rowkey);
            }

            st = iter->next();
        }

        zq::RowKeyRef next_key = nullptr;
        if (!st.ok()) {
            if (!st.is_iterator_end()) {
                LOG(WARNING) << "Fail to get upper bound for tablet healthy check,"
                             << " table_id:" << table_id
                             << " tp_id:" << tp_id;
                break;
            }

            next_key = nullptr;
            st = Status();
        } else {
            bool has_scalar = false;
            bool has_vector = false;
            st = iter->get(next_key, &has_scalar, &has_vector);
            if (!st.ok()) {
                LOG(WARNING) << "Fail to get next_key from tablet iterator for healthy check,"
                             << " table_id:" << table_id
                             << " tp_id:" << tp_id;
                break;
            }

            LOG_AND_ASSERT(next_key != nullptr);
        }

        // Add the sub range healthy check msg to replication queue;
        do_tablet_sub_range_healthy_check(tablet_control, marker_min_key, next_key);
        marker_min_key = next_key;
        if (marker_min_key == nullptr) {
            break;
        }

        for (auto index = 0; !_is_stopped && index < FLAGS_self_check_thread_work_interval_s;
             ++index) {
            if (!g_tablet_manager->is_tablet_exist(tablet->get_tablet_id())) {
                return;
            }
            std::this_thread::sleep_for(std::chrono::seconds(1));
        }
    }

    //Add sample rowkeys to replication queue for building tablet row cache;
    if (st.ok() && !_is_stopped && !sample_row_keys.empty()) {
        update_tablet_row_cache(tablet_control, sample_row_keys);
    }

    LOG(NOTICE) << "Finish to do healthy check on tablet,"
		<< " table_id:" << table_id
		<< " tp_id:" << tp_id;
}

void HealthyCheckScheduler::do_tablet_sub_range_healthy_check(TabletControlRef tablet_control,
							      zq::RowKeyRef minkey,
							      zq::RowKeyRef maxkey) {
    pb::SelfCheckRequest request;
    request.set_token(FLAGS_token);
    tablet_id_2pb(tablet_control->get_tablet()->get_tablet_id(), request.mutable_tablet_id());

    if (minkey != nullptr) {
        minkey->serialize_to_pb(request.mutable_minkey());
    }

    if (maxkey != nullptr) {
        maxkey->serialize_to_pb(request.mutable_maxkey());
    }

    ::mochow::pb::AckResponse response;
    tablet_control->self_check(nullptr, &request, &response);
}

void HealthyCheckScheduler::update_tablet_row_cache(TabletControlRef tablet_control,
					            const std::vector<zq::RowKey>& keys) {

    ::mochow::datanode::pb::UpdateTabletRowCacheRequest request;
    request.set_token(FLAGS_token);;
    tablet_id_2pb(tablet_control->get_tablet()->get_tablet_id(), request.mutable_tablet_id());
    for (const auto& row_key : keys) {
        auto pkey = request.add_row_keys();
	row_key.serialize_to_pb(pkey);
    }

    ::mochow::pb::AckResponse response;
    tablet_control->update_row_cache(nullptr, &request, &response);
    if (response.status().code() != 0) {
        LOG(WARNING) << "Fail to update tablet row cache,"
                     << " table_id:" << tablet_control->get_tablet()->get_table_id()
                     << " tp_id:" << tablet_control->get_tablet()->get_tp_id()
                     << " error_code:" << response.status().code();
    } else {
        LOG(NOTICE) << "Succeed to update tablet row cache,"
                    << " table_id:" << tablet_control->get_tablet()->get_table_id()
                    << " tp_id:" << tablet_control->get_tablet()->get_tp_id();
    }
}

void HealthyCheckScheduler::thread_pool_check() {
    {
        auto pool = zq::SearchThreadPool::get_global_build_thread_pool();
        LOG(NOTICE) << "Print global build pool info,"
                    << " pending_task_count:" << pool->get_pending_task_count()
                    << " max_capacity:" << pool->get_capacity()
                    << " is_full:" << (pool->full() ? "true" : "false");
    }
    {
        auto pool = zq::SearchThreadPool::get_global_search_thread_pool();
        LOG(NOTICE) << "Print global search pool info,"
                    << " pending_task_count:" << pool->get_pending_task_count()
                    << " max_capacity:" << pool->get_capacity()
                    << " is_full:" << (pool->full() ? "true" : "false");
    }
}

}
