/**
 * MOCHOW - A Vector Database System.
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: la<PERSON><PERSON><PERSON>(<EMAIL>)
 * Date: 2024/06/14
 * Desciption: definitions of compaction scheduler;
 *
 */

#pragma once

#include <atomic>
#include <thread>
#include <string>
#include <vector>
#include <functional>

#include "sfl/class_helper.h"

#include "core/src/datanode/tablet_control.h"
#include "core/src/common/using.h"
#include "core/src/common/status.h"
#include "core/src/common/logging.h"

namespace mochow::datanode {

class CompactionScheduler final {
public:
    CompactionScheduler() = default;

    ~CompactionScheduler() = default;

    DISABLE_COPY_AND_MOVE(CompactionScheduler);

    void start() {
        if (_is_stopped) {
            LOG(NOTICE) << "Start compaction scheduler";
            _is_stopped = false;
            _thread = std::thread{ std::bind(&CompactionScheduler::run, this) };
        }
    }

    void stop() {
        LOG(NOTICE) << "Stop compaction scheduler";
        _is_stopped = true;
    }

    void join() {
        if (_thread.joinable()) {
            LOG(NOTICE) << "Join compaction scheduler";
            _thread.join();
        }
    }

private:
    void run();
    Status try_compact(const TabletControlRef& tablet_control);
private:
    std::atomic<bool> _is_stopped = true;
    std::thread _thread;
};

extern CompactionScheduler*  g_compaction_scheduler;

}
