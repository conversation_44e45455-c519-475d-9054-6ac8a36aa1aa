/**
 * MOCHOW - A Vector Database System.
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON> (<PERSON><PERSON><PERSON><PERSON>@baidu.com)
 * Date: 2023/12/23
 * Desciption: Definitions for DataNode Metric Refresher
 *
 */

#pragma once

#include <atomic>
#include <thread>
#include <string>
#include <vector>
#include <functional>
#include "core/src/common/using.h"
#include "core/src/common/logging.h"
#include "core/src/datanode/flags.h"

namespace mochow::datanode {

class DataNodeMetricRefresher {
public:
    DataNodeMetricRefresher() = default;
    ~DataNodeMetricRefresher() = default;

    void start() {
        if (_is_stopped) {
            LOG(NOTICE) << "Start metric refresher";
            _is_stopped = false;
            _thread = std::thread{ std::bind(&DataNodeMetricRefresher::run, this) };
        }
    }

    void stop() {
        LOG(NOTICE) << "Stop metric refresher";
        _is_stopped = true;
    }

    void join() {
        if (_thread.joinable()) {
            LOG(NOTICE) << "Join metric refresher";
            _thread.join();
        }
    }

private:
    void run();

private:
    std::atomic<bool> _is_stopped = true;
    std::thread _thread;
};

extern DataNodeMetricRefresher* g_metric_refresher;

} // namespace mochow::datanode
