/**
 * MOCHOW - A Vector Database System.
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: la<PERSON><PERSON><PERSON>(<EMAIL>)
 * Date: 2024/11/19
 *
 */

#include "core/src/datanode/scheduler/compaction_scheduler.h"
#include "core/src/datanode/scheduler/ttl_gc_scheduler.h"
#include "core/src/common/common.h"
#include "core/src/datanode/flags.h"
#include "core/src/datanode/tablet_manager.h"

namespace mochow::datanode {

TTLGCScheduler* g_ttl_gc_scheduler = new TTLGCScheduler;

void TTLGCScheduler::run() {
    auto last_exec_gc_ts = base::gettimeofday_s();
    while (!_is_stopped) {
        const auto delta =  base::gettimeofday_s() - last_exec_gc_ts;
        if (delta < FLAGS_tablet_gc_thread_work_interval_s) {
            std::this_thread::sleep_for(std::chrono::seconds(1));
            continue;
        }

        auto tablet_controls = g_tablet_manager->list_tablet_controls();
        for (auto& tablet_control : tablet_controls) {
            const auto need_gc = tablet_control->get_tablet()->need_gc();
            if (!need_gc) {
                continue;
            }
           
            if (!tablet_control->is_leader()) {
                continue;
            }

            tablet_gc(tablet_control);
            if (_is_stopped) {
                break;
            }
        }
        last_exec_gc_ts = base::gettimeofday_s();
    }
}

void TTLGCScheduler::tablet_gc(TabletControlRef tablet_control) {
    auto tablet = tablet_control->get_tablet();
    const auto table_id = tablet->get_table_id();
    const auto tp_id = tablet->get_tp_id();
    auto schema_hierarchy = ::mochow::datanode::g_schema_cache->get_schema_hierarchy(table_id);
    if (schema_hierarchy == nullptr) {
        //Only will happen when table of tablet had been dropped.
        LOG(NOTICE) << "Skip to do gc on tablet due to table schema can not be found,"
                    << " table_id:" << table_id
                    << " tp_id:" << tp_id;
        return;
    }

    const auto ttl = tablet->get_ttl();
    zq::RowRef marker = nullptr;
    auto scalar_iter = tablet->create_scalar_iterator_from_filter(schema_hierarchy, zq::pass_through_filter(), nullptr);
    if (scalar_iter == nullptr) {
        LOG(WARNING) << "Fail to create scalar iterator from scalar engine for ttl gc,"
                     << " table_id:" << table_id
                     << " tp_id:"<< tp_id;
        return;
    }

    uint64_t deleted_expired_row_num = 0;
    std::vector<zq::RowRef> expired_rows;
    auto st = scalar_iter->seek_to_first();
    while (st.ok() && !_is_stopped) {
        zq::RowRef row;
        st = scalar_iter->get(row);
        if (!st.ok()) {
            LOG(WARNING) << "Fail to get row from scalar iterator for ttl gc due to iterator get failed,"
                         << " table_id:" << table_id
                         << " tp_id:"<< tp_id
                         << " status:" << st;
            break;
        }

        const bool expired = row->expired(schema_hierarchy, ttl);
        if (expired) {
            expired_rows.emplace_back(std::move(row));
            if (expired_rows.size() >= static_cast<size_t>(FLAGS_tablet_expired_keys_gc_batch_size)) {
                auto status = tablet_delete_expired_rows(schema_hierarchy, tablet_control, expired_rows);
                if (!status.ok()) {
                    LOG(WARNING) << "Fail to delete expired rows for ttl gc on tablet,"
                                 << " expired_rows_size:" << expired_rows.size()
                                 << " table_id:" << table_id
                                 << " tp_id:"<< tp_id
                                 << " status:" << status;
                } else {
                    deleted_expired_row_num += expired_rows.size();
                }
                expired_rows.clear();
            }
        }

        st = scalar_iter->next();
    }

    if (!expired_rows.empty()) {
        auto status = tablet_delete_expired_rows(schema_hierarchy, tablet_control, expired_rows);
        if (!status.ok()) {
             LOG(WARNING) << "Fail to delete expired rows for ttl gc on tablet,"
                          << " expired_rows_size:" << expired_rows.size()
                          << " table_id:" << table_id
                          << " tp_id:"<< tp_id
                          << " status:" << status;
        } else {
            deleted_expired_row_num += expired_rows.size();
        }
        expired_rows.clear();
    }

    if (!st.ok() && !st.is_iterator_end()) {
        LOG(WARNING) << "Fail to do ttl gc on tablet due to iterator error,"
                     << " table_id:" << table_id
                     << " tp_id:"<< tp_id
                     << " deleted_expired_row_num:" << deleted_expired_row_num
                     << " status:" << st;
        return;
    }

    LOG(NOTICE) << "Succeed to do ttl gc on tablet,"
                << " table_id:" << table_id
                << " tp_id:"<< tp_id
                << " deleted_expired_row_num:" << deleted_expired_row_num;
}

Status TTLGCScheduler::tablet_delete_expired_rows(const schema::SchemaHierarchyRef schema_hierarchy,
                                                  TabletControlRef tablet_control,
                                                  const std::vector<zq::RowRef>& expired_rows) {
    pb::GCExpiredRowsRequest request;
    request.set_token(FLAGS_token);
    tablet_id_2pb(tablet_control->get_tablet()->get_tablet_id(), request.mutable_tablet_id());
    for (const auto& row : expired_rows) {
        auto expired_row_pb = request.add_expired_rows();
        row->get_allkey()->serialize_to_pb(expired_row_pb->mutable_row_key());
        expired_row_pb->set_create_timestamp(row->get_create_timestamp(schema_hierarchy));
    }

    ::mochow::pb::AckResponse response;
    tablet_control->gc_expired_rows(nullptr, &request, &response);
    if (response.status().code() != 0) {
        LOG(WARNING) << "Fail to gc expired rows on tablet,"
                     << " table_id:" << tablet_control->get_tablet()->get_table_id()
                     << " tp_id:" << tablet_control->get_tablet()->get_tp_id()
                     << " expired_rows_num:" << expired_rows.size();
        return Status(ERR_FAIL, "Fail to gc expired rows");
    }
    return Status();
}

}
