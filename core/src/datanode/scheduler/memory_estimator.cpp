/***************************************************************************
 *
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

#include "core/src/datanode/scheduler/memory_estimator.h"

#include <sstream>

#include "sfl/constants.h"
#include "sfl/logging.h"

#include "core/src/common/logging.h"

#include "core/src/common/utils.h"
#include "core/src/common/system_metric_reader.h"

#include "core/src/datanode/flags.h"

namespace mochow::datanode {

std::unique_ptr<MemoryEstimator> g_memory_estimator = std::make_unique<MemoryEstimator>();

bool MemoryEstimator::try_refresh_system_memory() {
    // read from /proc/self/statm
    ProcMemory m;
    bool b = g_proc_memory_reader->read(m);
    if (!b) {
        LOG(WARNING) << "Fail to read proc memory from g_proc_memory_reader,"
                    << " return_value:" << b;
        return false;
    }

    uint64_t current_system_memory = m.resident * 4 * baidu::sfl::KIBI;

    common::ScopedButex lock(_memory_lock);
    if (_estimation_task_count == 0) {
        // scenario 1: if there's no task, update it directly.
        _system_memory = current_system_memory;
        return true;
    } else {
        // scenario 2: there're tasks running.
        if (_system_memory + _estimation_memory < current_system_memory) {
            // scenario 2.1: force update system memory if it is too high
            _system_memory = current_system_memory - _estimation_memory;
            return true;
        } else {
            // scenario 2.2: otherwise we do not update.
            return false;
        }
    }
}

bool MemoryEstimator::try_add(size_t memory_cost) {
    common::ScopedButex lock(_memory_lock);

    auto estimated_memory = _system_memory + _estimation_memory + memory_cost;
    MOCHOW_TRACE_LOG(NOTICE) << "Try to add estimated memory cost to memory estimator,"
                << " memory_cost(byte):" << memory_cost
                << " estimated_total_memory(byte):" << estimated_memory
                << " system_memory(byte):" << _system_memory
                << " previous_estimation_task_count:" << _estimation_task_count;

    if (reach_high_watermark(estimated_memory)) {
        MOCHOW_TRACE_LOG(NOTICE) << "Fail to add estimated memory cost to memory estimator due to "
                       "estimated memory reaches high watermark,"
                    << " total_memory(byte):" << total_memory()
                    << " estimated_total_memory(byte):" << estimated_memory
                    << " high_watermark(byte):" << high_watermark();
        return false;
    }

    if (!reserve_enough_memory(estimated_memory)) {
        MOCHOW_TRACE_LOG(NOTICE) << "Fail to add estimated memory cost to memory estimator due to "
                       "no enough memory reserving for OS,"
                    << " total_memory(byte):" << total_memory()
                    << " reserved_memory(byte):" << reserved_memory(estimated_memory)
                    << " mimimum_reserved_memory(byte):" << FLAGS_datanode_minimum_reserved_memory_in_byte;
        return false;
    }

    MOCHOW_TRACE_LOG(NOTICE) << "Succeed to add estimated memory cost to memory estimator,"
                << " total_memory(byte):" << total_memory()
                << " estimated_total_memory(byte):" << estimated_memory;

    _estimation_task_count++;
    _estimation_memory += memory_cost;

    return true;
}

void MemoryEstimator::remove(size_t memory_cost) {
    common::ScopedButex lock(_memory_lock);

    LOG_AND_ASSERT(_estimation_task_count > 0);
    LOG_AND_ASSERT(_estimation_memory >= memory_cost);

    _estimation_task_count--;
    _estimation_memory -= memory_cost;

    MOCHOW_TRACE_LOG(NOTICE) << "Succeed to remove estimated memory cost from memory estimator,"
                << " memory_cost(byte):" << memory_cost
                << " system_memory(byte):" << _system_memory
                << " estimated_total_memory_after_removing(byte):" << _system_memory + _estimation_memory
                << " estimation_task_count_after_removing:" << _estimation_task_count;
}

void MemoryEstimator::clear() {
    common::ScopedButex lock(_memory_lock);

    _estimation_task_count = 0;
    _estimation_memory = 0;
    _system_memory = 0;
}

std::string MemoryEstimator::to_string() const {
    common::ScopedButex lock(_memory_lock);

    std::stringstream ss;
    ss << "{\"MemoryEstimator\":{";
    ss << "\"estimation_task_count\":" << _estimation_task_count << ",";
    ss << "\"estimation_memory\":" << _estimation_memory << ",";
    ss << "\"system_memory\":" << _system_memory;
    ss << "}}";

    return ss.str();
}

size_t MemoryEstimator::total_memory() const {
    return get_total_mem_size_in_mb() * baidu::sfl::MEBI;
}

size_t MemoryEstimator::high_watermark() const {
    return total_memory() * FLAGS_datanode_memory_cost_water_mark_percentage;
}

size_t MemoryEstimator::reserved_memory(size_t estimated_memory) const {
    return total_memory() - estimated_memory;
}

bool MemoryEstimator::reach_high_watermark(size_t estimated_memory) const {
    return estimated_memory >= high_watermark();
}

bool MemoryEstimator::reserve_enough_memory(size_t estimated_memory) const {
    return reserved_memory(estimated_memory) >= FLAGS_datanode_minimum_reserved_memory_in_byte;
}

} // namespace mochow::datanode
