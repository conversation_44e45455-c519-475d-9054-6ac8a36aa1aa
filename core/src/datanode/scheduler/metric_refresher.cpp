/***************************************************************************
 *
 * Copyright (c) 2024 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

#include "core/src/datanode/rpc_metric_recorder.h"
#include "core/src/datanode/tablet_manager.h"
#include "core/src/datanode/scheduler/metric_refresher.h"
#include "core/src/zhuque/bvar_metrics.h"

namespace mochow::datanode {

DECLARE_int32(datanode_metric_refresh_interval_s);

DataNodeMetricRefresher* g_metric_refresher = new DataNodeMetricRefresher();

void DataNodeMetricRefresher::run() {
    auto target_time = base::gettimeofday_s();

    while (!_is_stopped) {
        std::this_thread::sleep_for(std::chrono::seconds(1));
        if (base::gettimeofday_s() < target_time) { continue; }
        target_time = base::gettimeofday_s() + FLAGS_datanode_metric_refresh_interval_s;

        zq::g_table_metrics.refresh();
        g_rpc_metric_recorder->refresh_metrics();
    }
}

}
