/**
 * MOCHOW - A Vector Database System.
 * Copyright (c) 2024 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON> (ca<PERSON><PERSON>@baidu.com)
 * Date: 2024/11/20
 */

#include "core/src/datanode/scheduler/scalar_index_build_scheduler.h"

#include <algorithm>
#include <functional>

#include "core/src/datanode/scheduler/tablet_index_builder.h"
#include "core/src/datanode/flags.h"
#include "core/src/schema/schema_cache.h"
#include "core/src/zhuque/tablet.h"

namespace mochow::datanode {

void ScalarIndexBuildScheduler::run() {
    while (!_is_stopped) {
        std::this_thread::sleep_for(std::chrono::seconds(FLAGS_async_index_scheduler_check_interval_s));

        // scan all tablet and find scalar index to build
        std::vector<TabletControlRef> tablet_controls = g_tablet_manager->list_tablet_controls();
        for (const auto& tablet_control : tablet_controls) {
            zq::TabletRef tablet = tablet_control->get_tablet();
            TBLID table_id = tablet->get_table_id();

            // skip this tablet since table already deleted
            auto schema_hierarchy = g_schema_cache->get_schema_hierarchy(table_id);
            if (schema_hierarchy == nullptr) {
                continue;
            }
            auto table_schema = schema_hierarchy->internal_schema;
            if (table_schema == nullptr) {
                continue;
            }

            if (tablet->is_recovering()) {
                LOG(NOTICE) << "Tablet is recovering and skip auto build scalar index,"
                            << " table_id:" << tablet->get_table_id()
                            << " tp_id:" << tablet->get_tp_id();
                continue;
            }

            if (tablet->loading_snapshot()) {
                LOG(NOTICE) << "Tablet is loading snapshot and skip auto build scalar index,"
                            << " table_id:" << tablet->get_table_id()
                            << " tp_id:" << tablet->get_tp_id();
                continue;
            }

            std::map<IDXID, zq::ScalarIndexRef> local_indexes = tablet->get_scalar_indexes();
            std::vector<schema::ScalarIndexSchemaRef> index_schemas = table_schema->get_scalar_indexes();

            // release dropped scalar index
            for (const auto& [index_id, index] : local_indexes) {
                bool skip_drop = false;
                for (const auto& index_schema : index_schemas) {
                    if (index_id == index_schema->get_index_id()) {
                        skip_drop = true;
                    }
                }
                if (!skip_drop) {
                    tablet->drop_index(index->get_index_id());
                }
            }

            // find scalar indexes need to build and trigger building process
            for (const auto& index_schema : index_schemas) {
                // only support filtering index currently
                if (index_schema->get_index_param().index_type != schema::ScalarIndexType::FILTERING) {
                    continue;
                }
                bool skip_build = false;
                for (const auto& [index_id, index] : local_indexes) {
                    if (index_schema->get_index_id() == index_id 
                            && index->get_stage() != common::ScalarIndexStage::FAILED) {
                        skip_build = true;
                    }
                }
                if (!skip_build) {
                    auto new_scalar_index = tablet->add_scalar_index(index_schema);

                    // add scalar index build task
                    common::Priority priority = common::Priority::LOW;
                    common::BucketedThreadpool::TaskOption task_options(tablet->get_tablet_id(), priority);
                    std::function<void(const bool)> task = std::bind(
                            build_scalar_index, schema_hierarchy, tablet_control, new_scalar_index);
                    const auto ret = g_tablet_index_builder->submit_task(task, &task_options);
                    if (ret != OK) {
                        LOG(WARNING) << "Fail to add build_scalar_index task to threadpool,"
                                     << " table_id:" << tablet->get_table_id() << " tp_id:" << tablet->get_tp_id()
                                     << " index_schema:" << index_schema->to_string()
                                     << " priority:" << static_cast<int>(priority);
                    } else {
                        LOG(NOTICE) << "Succeed to add build_scalar_index task to threadpool,"
                                    << " table_id:" << tablet->get_table_id() << " tp_id:" << tablet->get_tp_id()
                                    << " index_schema:" << index_schema->to_string()
                                    << " priority:" << static_cast<int>(priority);
                    }
                }
            }
        }
    }
}

void ScalarIndexBuildScheduler::build_scalar_index(const schema::SchemaHierarchyRef schema_hierarchy,
                                                   TabletControlRef tablet_control,
                                                   zq::ScalarIndexRef index) {
    Status st;
    auto tablet = tablet_control->get_tablet();
    const auto table_id = tablet->get_table_id();
    const auto tp_id = tablet->get_tp_id();

    LOG(NOTICE) << "Begin to build scalar index on Tablet,"
                << " table_id:" << table_id
                << " tp_id:"<< tp_id
                << " index_id:" << index->get_index_id();

    // create scalar iterator
    auto scalar_iterator = tablet->create_scalar_iterator_from_filter(schema_hierarchy, zq::pass_through_filter(), nullptr);
    if (scalar_iterator == nullptr) {
        LOG(WARNING) << "Fail to create scalar iterator for building scalar index"
                     << " table_id:" << table_id
                     << " tp_id:"<< tp_id;
        index->set_stage(common::ScalarIndexStage::FAILED);
        return;
    }

    st = scalar_iterator->seek_to_first();
    while (st.ok()) {
        zq::RowRef row;
        st = scalar_iterator->get(row);
        if (!st.ok()) {
            LOG(WARNING) << "Fail to build scalar index due to scalar iterator get() failed,"
                        << " status:" << st.to_string()
                        << " table_id:" << tablet->get_table_id()
                        << " tp_id:" << tablet->get_tp_id()
                        << " index_id:" << index->get_index_id();
            index->set_stage(common::ScalarIndexStage::FAILED);
            return;
        }

        // insert into scalar index
        st = insert_into_index(schema_hierarchy, tablet_control, index, row);
        if (!st.ok()) {
            LOG(WARNING) << "Fail to build scalar index due to insert index failed,"
                        << " status:" << st.to_string()
                        << " table_id:" << tablet->get_table_id()
                        << " tp_id:" << tablet->get_tp_id()
                        << " index_id:" << index->get_index_id();
            index->set_stage(common::ScalarIndexStage::FAILED);
            return;
        }

        st = scalar_iterator->next();
    }
    if (!st.ok() && !st.is_iterator_end()) {
        LOG(WARNING) << "Fail to build scalar index due to scalar iterator error"
                     << " status:" << st.to_string()
                     << " table_id:" << tablet->get_table_id()
                     << " tp_id:" << tablet->get_tp_id()
                     << " index_id:" << index->get_index_id();
        index->set_stage(common::ScalarIndexStage::FAILED);
        return;
    }
    index->set_stage(common::ScalarIndexStage::AVAILABLE);

    LOG(NOTICE) << "Succeed to build scalar index on Tablet,"
                << " table_id:" << table_id
                << " tp_id:"<< tp_id
                << " index_id:" << index->get_index_id();

    return;
}

Status ScalarIndexBuildScheduler::insert_into_index(const schema::SchemaHierarchyRef schema_hierarchy,
                                                    TabletControlRef tablet_control,
                                                    zq::ScalarIndexRef index,
                                                    const zq::RowRef row) {
    auto tablet = tablet_control->get_tablet();

    // insert row into scalar index
    if (index->type() == schema::ScalarIndexType::FILTERING) {
        auto filtering_index = std::dynamic_pointer_cast<zq::FilteringIndex>(index);
        LOG_AND_ASSERT(filtering_index != nullptr);
        const INCID vid = row->get_vid(schema_hierarchy);
        filtering_index->insert(vid, row, schema_hierarchy);
    } else {
        LOG(WARNING) << "Fail to build scalar index due to unsupported index type,"
                     << " index_id:" << index->get_index_id()
                     << " index_type:" << index->type();
        return Status(ERR_FAIL, "unsupported scalar index type");
    }
    return Status();
}

} // namespace mochow::datanode
