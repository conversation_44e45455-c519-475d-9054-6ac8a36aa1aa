#pragma once

#include <thread>
#include <memory>
#include <functional>
#include "core/src/common/logging.h"
#include "core/src/datanode/scheduler/iostats_collector.h"

namespace mochow::datanode {

class IOStatsScheduler {
public:
    bool init(const std::string& disk_path, const std::string& dev_name,
              const dev_t dev_no, uint64_t collect_window_second) {
        _iostats_collector = std::make_unique<IOStatsCollector>(
              disk_path, dev_name, dev_no, collect_window_second);
        return _iostats_collector->init();
    }

    bool start() {
        if (!_is_stopped || _iostats_collector == nullptr) {
            return false;
        }
        LOG(NOTICE) << "Start iostats scheduler";
        _is_stopped = false;
        _thread = std::thread{std::bind(&IOStatsScheduler::run, this)};
        return true;
    }

    void stop() {
        LOG(NOTICE) << "Stop iostats scheduler";
        _is_stopped = true;
    }

    void join() {
        if (_thread.joinable()) {
            LOG(NOTICE) << "Join iostats scheduler";
            _thread.join();
        }
    }

    uint64_t get_current_ioutil() const { return _current_ioutil; }
    uint64_t get_current_svct() const { return _current_svct; }
    uint64_t get_current_read_iops() const { return _current_read_iops; }
    uint64_t get_current_read_throughput() const { return _current_read_throughput; }
    uint64_t get_current_write_iops() const { return _current_write_iops; }
    uint64_t get_current_write_throughput() const { return _current_write_throughput; }

private:
    void run();
    void calc_iostats();

private:
    std::thread _thread;
    std::atomic<bool> _is_stopped = true;
    std::unique_ptr<IOStatsCollector> _iostats_collector;

    std::atomic<uint64_t> _current_ioutil; // [0, 100]
    std::atomic<uint64_t> _current_svct;
    std::atomic<uint64_t> _current_read_iops;
    std::atomic<uint64_t> _current_read_throughput;
    std::atomic<uint64_t> _current_write_iops;
    std::atomic<uint64_t> _current_write_throughput;
};

using IOStatsSchedulerRef = std::shared_ptr<IOStatsScheduler>;

}
