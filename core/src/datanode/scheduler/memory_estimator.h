/***************************************************************************
 *
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

#pragma once

#include <memory>
#include <atomic>

#include "core/src/common/butex.h"

namespace mochow::datanode {

// ---------------------
// Explainations of some specialized words is provided here:
// * total memory:      the total memory size of this datanode.
// * system memory:     the memory memory we get from linux system. it only updates if there're estimation tasks.
// * memory cost:       the memory cost of a certain build task.
// * estimation memory: the sum of estimation memories of all running build tasks.
// * estimated memory:  the sum of system memory, estimation memory and memory cost.
//                      it represents the estimated memory cost if we accept this certain build task.
// 
// * estimation task count: how many building tasks are running now.

class MemoryEstimator final {
public:
    MemoryEstimator() = default;
    ~MemoryEstimator() = default;

    bool try_refresh_system_memory();

    bool try_add(size_t memory_cost);

    void remove(size_t memory_cost);

    void clear();

    std::string to_string() const;

    size_t total_memory() const;

private:
    size_t high_watermark() const;
    size_t reserved_memory(size_t estimated_memory) const;

    // add memory cost would fail if estimated total memory reaches high watermark.
    bool reach_high_watermark(size_t estimated_memory) const;

    // add memory cost would fail if reserved memory is less than minimun reserved memory.
    bool reserve_enough_memory(size_t estimated_memory) const;

    mutable common::Butex _memory_lock;

    size_t _estimation_task_count = 0;

    uint64_t _estimation_memory = 0;
    uint64_t _system_memory = 0;
};

extern std::unique_ptr<MemoryEstimator> g_memory_estimator;

} // namespace mochow::datanode