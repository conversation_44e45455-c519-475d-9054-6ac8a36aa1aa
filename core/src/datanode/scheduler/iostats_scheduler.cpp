#include "core/src/common/bvar_define.h"
#include "core/src/datanode/flags.h"
#include "core/src/datanode/scheduler/iostats_scheduler.h"

namespace mochow::datanode {

common::BvarStatusWithTag<int32_t> g_bvar_disk_ioutil("datanode", "disk_ioutil");
common::BvarStatusWithTag<int32_t> g_bvar_disk_read_iops("datanode", "disk_read_iops");
common::BvarStatusWithTag<int32_t> g_bvar_disk_write_iops("datanode", "disk_write_iops");
common::BvarStatusWithTag<int32_t> g_bvar_disk_read_throughput("datanode", "disk_read_throughput");
common::BvarStatusWithTag<int32_t> g_bvar_disk_write_throughput("datanode", "disk_write_throughput");

void IOStatsScheduler::run() {
    LOG(WARNING) << "Start running iostats scheduler for disk "
                 << _iostats_collector->path();
    while (!_is_stopped) {
        _iostats_collector->collect_total_ticks();

        calc_iostats();

        std::this_thread::sleep_for(
                std::chrono::seconds(FLAGS_iostats_collect_interval_s));
    }
    LOG(WARNING) << "Stop running iostats scheduler for disk "
                 << _iostats_collector->path();
}

void IOStatsScheduler::calc_iostats() {
    std::string dev_name = _iostats_collector->dev_name();

    // IOUtil
    double current_ioutil_rate = 0;
    _iostats_collector->get_ioutil_rate(current_ioutil_rate);
    _current_ioutil = int(current_ioutil_rate * 100);

    // Read IOPS
    uint64_t current_read_iops = 0;
    _iostats_collector->get_current_read_iops(current_read_iops, 1);
    _current_read_iops = current_read_iops;

    // Write IOPS
    uint64_t current_write_iops = 0;
    _iostats_collector->get_current_write_iops(current_write_iops, 1);
    _current_write_iops = current_write_iops;

    // Read Throughput
    uint64_t current_read_throughput = 0;
    _iostats_collector->get_current_read_throughput(current_read_throughput, 1);
    _current_read_throughput = current_read_throughput;

    // Write Throughput
    uint64_t current_write_throughput = 0;
    _iostats_collector->get_current_write_throughput(current_write_throughput, 1);
    _current_write_throughput = current_write_throughput;

    // Update bvar metrics
    g_bvar_disk_ioutil.put(dev_name, _current_ioutil);
    g_bvar_disk_read_iops.put(dev_name, _current_read_iops);
    g_bvar_disk_read_throughput.put(dev_name, _current_read_throughput);
    g_bvar_disk_write_iops.put(dev_name, _current_write_iops);
    g_bvar_disk_write_throughput.put(dev_name, _current_write_throughput);
}

}
