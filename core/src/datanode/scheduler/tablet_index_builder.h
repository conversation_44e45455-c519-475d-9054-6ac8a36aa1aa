/***************************************************************************
 *
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

#pragma once

#include "core/src/common/task_scheduler.h"

namespace mochow::datanode {

class TabletIndexBuilderType final {
public:
    static constexpr const char* TABLET_INDEX_BUILDER = "TabletIndexBuilder";
};

class TabletIndexBuilder final {
public:
    static std::shared_ptr<common::TaskScheduler>& instance() {
        std::call_once(TabletIndexBuilder::_once_flag, []() {
            uint32_t thread_count = ::mochow::get_thread_count(
                FLAGS_datanode_total_thread_count,
                FLAGS_datanode_tablet_index_builder_thread_percentage);

            TabletIndexBuilder::_task_scheduler = std::make_shared<common::TaskScheduler>(
                TabletIndexBuilderType::TABLET_INDEX_BUILDER, thread_count, thread_count);
        });

        return TabletIndexBuilder::_task_scheduler;
    }

private:
    inline static std::once_flag _once_flag;
    inline static std::shared_ptr<common::TaskScheduler> _task_scheduler;

    TabletIndexBuilder() = default;
    ~TabletIndexBuilder() = default;
};

#define g_tablet_index_builder TabletIndexBuilder::instance()

}
