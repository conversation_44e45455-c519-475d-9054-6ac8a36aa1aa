#include <ctype.h>
#include <sys/statvfs.h>
#include <base/files/scoped_file.h>

#include "sfl/logging.h"
#include "core/src/common/logging.h"
#include "core/src/datanode/scheduler/iostats_collector.h"

namespace mochow::datanode {

constexpr const char* DISKSTATS = "/proc/diskstats";
constexpr const uint64_t BYTES_PER_SECTOR = 512;

bool IOStatsCollector::init() {
    _delta_total_ticks_sum.reset(new bvar::Window<bvar::Adder<uint64_t> >(
                                    &_delta_total_ticks, _iostats_window_second));
    LOG_AND_ASSERT(_delta_total_ticks_sum != nullptr);

    struct stat s;
    if (lstat(_path.c_str(), &s) != 0) {
        LOG(WARNING) << "lstat failed, path:" << _path
                     << " errno:" << errno << " errmsg:" << strerror(errno);
        return false;
    }

    // Double check
    LOG_AND_ASSERT(_major == major(s.st_dev));
    LOG_AND_ASSERT(_minor == minor(s.st_dev));
    LOG(NOTICE) << "IOStatsCollector init success, path:" << _path
                << " major:" << _major << " minor:" << _minor
                << " iostats_window_second:" << _iostats_window_second;
    return true;
}

// Need be called periodically
bool IOStatsCollector::collect_total_ticks() {
    base::ScopedFILE fp(fopen(DISKSTATS, "r"));
    if (fp == NULL) {
        LOG(WARNING) << "Fail to collect io stats due to fopen " << DISKSTATS << " failed,"
                     << " errno:" << errno << " errmsg:" << strerror(errno);
        return false;
    }
    std::map<std::string, uint64_t> dev_ticks;
    std::map<std::string, uint64_t> dev_rd_ios;
    std::map<std::string, uint64_t> dev_wr_ios;
    std::map<std::string, uint64_t> dev_rd_sec;
    std::map<std::string, uint64_t> dev_wr_sec;
    std::string target_dev;
    char line[1024];
    char dev_name[256];
    uint64_t major, minor, ios_pgr, total_ticks, rq_ticks, wr_ticks,
             rd_ios, rd_merges, rd_ticks, wr_ios, wr_merges, rd_sec, wr_sec;
    while (fgets(line, sizeof(line) - 1, fp) != NULL) {
        /* major minor name rio rmerge rsect ruse wio wmerge wsect wuse running use aveq */
        int n = sscanf(line, "%20lu %20lu %255s %20lu %20lu %20lu %20lu %20lu %20lu %20lu %20lu %20lu %20lu %20lu",
            &major, &minor, dev_name,
            &rd_ios, &rd_merges, &rd_sec, &rd_ticks,
            &wr_ios, &wr_merges, &wr_sec, &wr_ticks, &ios_pgr, &total_ticks, &rq_ticks);
        if (n != 14) {
            continue;
        }
        dev_ticks[dev_name] = total_ticks;
        dev_rd_ios[dev_name] = rd_ios;
        dev_wr_ios[dev_name] = wr_ios;
        dev_rd_sec[dev_name] = rd_sec;
        dev_wr_sec[dev_name] = wr_sec;
        if (major == _major && minor == _minor) {
            target_dev = dev_name;
        }
    }

    if (target_dev.empty()) {
        LOG(WARNING) << "Fail to collect io stats due to target dev not found,"
                     << " path:" << _path << " expected_dev_name:" << _dev_name;
        return false;
    }

    if (target_dev != _dev_name) {
        LOG(WARNING) << "Fail to collect io stats due to dev name is not expected,"
                     << " path:" << _path << " expected_dev_name:" << _dev_name
                     << " actual_dev_name:" << target_dev;
        return false;
    }

    // Collect total_ticks
    if (dev_ticks.find(_dev_name) != dev_ticks.end()) {
        if (_last_total_ticks != 0) {
            _delta_total_ticks << ((_last_total_ticks > dev_ticks[_dev_name]) ? 0 :
                (dev_ticks[_dev_name] - _last_total_ticks));
            _diff_total_ticks = ((_last_total_ticks > dev_ticks[_dev_name]) ? 0 :
                (dev_ticks[_dev_name] - _last_total_ticks));
        }
        _last_total_ticks = dev_ticks[_dev_name];
    } else {
        LOG(WARNING) << "Unable to find dev for path:" << _path << " dev_name:" << _dev_name
                     << " major:" << _major << " minor:" << _minor;
        _collect_succeeded = false;
        return false;
    }

    // Collect rd_ios
    if (dev_rd_ios.find(_dev_name) != dev_rd_ios.end()) {
        if (_last_rd_ios != 0) {
            _diff_rd_ios = ((_last_rd_ios > dev_rd_ios[_dev_name]) ? 0 :
                (dev_rd_ios[_dev_name] - _last_rd_ios));
        }
        _last_rd_ios = dev_rd_ios[_dev_name];
    } else {
        LOG(WARNING) << "Unable to find dev for path:" << _path << " dev_name:" << _dev_name
                     << " major:" << _major << " minor:" << _minor;
        _collect_succeeded = false;
        return false;
    }

    // Collect wr_ios
    if (dev_wr_ios.find(_dev_name) != dev_wr_ios.end()) {
        if (_last_wr_ios != 0) {
            _diff_wr_ios = ((_last_wr_ios > dev_wr_ios[_dev_name]) ? 0 :
                (dev_wr_ios[_dev_name] - _last_wr_ios));
        }
        _last_wr_ios = dev_wr_ios[_dev_name];
    } else {
        LOG(WARNING) << "Unable to find dev for path:" << _path << " dev_name:" << _dev_name
                     << " major:" << _major << " minor:" << _minor;
        _collect_succeeded = false;
        return false;
    }

    // Collect rd_sec
    if (dev_rd_sec.find(_dev_name) != dev_rd_sec.end()) {
        /*LOG(TRACE) << "path:" << _path << " in dev:" << _dev_name
                                << " current_rd_sec:" << dev_rd_sec[_dev_name]
                                << " last_rd_sec:" << _last_rd_sec;*/
        if (_last_rd_sec != 0) {
            _diff_rd_sec = ((_last_rd_sec > dev_rd_sec[_dev_name]) ? 0 :
                (dev_rd_sec[_dev_name] - _last_rd_sec));
        }
        _last_rd_sec = dev_rd_sec[_dev_name];
    } else {
        LOG(WARNING) << "Unable to find dev for path:" << _path << " dev_name:" << _dev_name
                     << " major:" << _major << " minor:" << _minor;
        _collect_succeeded = false;
        return false;
    }

    // Collect wr_sec
    if (dev_wr_sec.find(_dev_name) != dev_wr_sec.end()) {
        if (_last_wr_sec != 0) {
            _diff_wr_sec = ((_last_wr_sec > dev_wr_sec[_dev_name]) ? 0 :
                (dev_wr_sec[_dev_name] - _last_wr_sec));
        }
        _last_wr_sec = dev_wr_sec[_dev_name];
    } else {
        LOG(WARNING) << "Unable to find dev for path:" << _path << " dev_name:" << _dev_name
                     << " major:" << _major << " minor:" << _minor;
        _collect_succeeded = false;
        return false;
    }

    _collect_succeeded = true;
    return true;
}

bool IOStatsCollector::get_ioutil_rate(double& ioutil_rate) const {
    uint64_t delta_total_ticks_sum = _delta_total_ticks_sum->get_value();
    ioutil_rate = (delta_total_ticks_sum <= _iostats_window_second * 1000) ?
        (double)delta_total_ticks_sum / (double)(_iostats_window_second * 1000) : 1.0;
    return _collect_succeeded;
}

bool IOStatsCollector::get_current_svct(double& current_svct) const {
    if (_diff_total_ticks != 0) {
        if (_diff_rd_ios + _diff_wr_ios == 0) {
            // Long IO
            current_svct = (double)_diff_total_ticks;
        } else {
            // Normal IO
            current_svct = (double)_diff_total_ticks / (double)(_diff_rd_ios + _diff_wr_ios);
        }
    } else {
        // No IO
        current_svct = 0.0;
    }
    return _collect_succeeded;
}

bool IOStatsCollector::get_current_read_iops(uint64_t& current_read_iops, double diff_time) {
    current_read_iops = (uint64_t)(_diff_rd_ios / diff_time);
    return _collect_succeeded;
}

bool IOStatsCollector::get_current_write_iops(uint64_t& current_write_iops, double diff_time) {
    current_write_iops = (uint64_t)(_diff_wr_ios / diff_time);
    return _collect_succeeded;
}

bool IOStatsCollector::get_current_read_throughput(uint64_t& current_read_throughput, double diff_time) {
    auto current_read_sector = (uint64_t)(_diff_rd_sec / diff_time);
    current_read_throughput = current_read_sector * BYTES_PER_SECTOR;
    return _collect_succeeded;
}

bool IOStatsCollector::get_current_write_throughput(uint64_t& current_write_throughput, double diff_time) {
    auto current_write_sector = (uint64_t)(_diff_wr_sec / diff_time);
    current_write_throughput = current_write_sector * BYTES_PER_SECTOR;
    return _collect_succeeded;
}

}
