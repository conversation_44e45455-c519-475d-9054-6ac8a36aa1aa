#pragma once

#include <memory>
#include <string>
#include <vector>
#include <sys/sysmacros.h>
#include <base/macros.h>

#include "core/src/common/bvar_define.h"

namespace mochow::datanode {

struct IOStatsUnit {
    uint64_t read_io;
    uint64_t write_io;
    uint64_t read_throughput;
    uint64_t write_throughput;
    IOStatsUnit() = default;
    IOStatsUnit(uint64_t readio, uint64_t writeio, uint64_t readthroughput, uint64_t writethroughput) {
        read_io = readio;
        write_io = writeio;
        read_throughput = readthroughput;
        write_throughput = writethroughput;
    }
    void operator+=(const IOStatsUnit& other) {
        read_io += other.read_io;
        write_io += other.write_io;
        read_throughput += other.read_throughput;
        write_throughput += other.write_throughput;
    }
};

struct IOStatsUnitCollector {
    bvar::Adder<uint64_t> read_io;
    bvar::Adder<uint64_t> write_io;
    bvar::Adder<uint64_t> read_throughput;
    bvar::Adder<uint64_t> write_throughput;
    void reset() {
        read_io.reset();
        write_io.reset();
        read_throughput.reset();
        write_throughput.reset();
    }
    IOStatsUnit get_and_reset() {
        IOStatsUnit res{ read_io.get_value(),
                         write_io.get_value(),
                         read_throughput.get_value(),
                         write_throughput.get_value() };
        reset();
        return res;
    }
    void record_read(uint64_t bytes) {
        read_io << 1;
        read_throughput << bytes;
    }
    void record_write(uint64_t bytes) {
        write_io << 1;
        write_throughput << bytes;
    }
};

class IOStatsCollector {
public:
    IOStatsCollector(const std::string path, const std::string& dev_name,
                     const dev_t dev_no, uint64_t iostats_window_second) :
        _path(path), _dev_name(dev_name),
        _major(major(dev_no)), _minor(minor(dev_no)),
        _iostats_window_second(iostats_window_second) {}

    virtual ~IOStatsCollector() = default;

    virtual bool init();
    virtual bool collect_total_ticks();
    virtual bool get_ioutil_rate(double& ioutil_rate) const;
    virtual bool get_current_svct(double& current_svct) const;
    virtual bool get_current_read_iops(uint64_t& current_read_iops, double diff_time);
    virtual bool get_current_write_iops(uint64_t& current_write_iops, double diff_time);
    virtual bool get_current_read_throughput(uint64_t& current_read_throughput, double diff_time);
    virtual bool get_current_write_throughput(uint64_t& current_write_throughput, double diff_time);

    const std::string& path() const { return _path; }
    const std::string& dev_name() const { return _dev_name; }

#ifdef _UNIT_TEST
    uint64_t get_diff_total_ticks() const {
        return _diff_total_ticks;
    }
    uint64_t get_diff_rd_ios() const {
        return _diff_rd_ios;
    }
    uint64_t get_diff_wr_ios() const {
        return _diff_wr_ios;
    }
    uint64_t get_last_total_ticks() const {
        return _last_total_ticks;
    }
    uint64_t get_last_rd_ios() const {
        return _last_rd_ios;
    }
    uint64_t get_last_wr_ios() const {
        return _last_wr_ios;
    }
#endif

protected:
    std::string _path;
    std::string _dev_name;
    uint64_t _major = UINT64_MAX;
    uint64_t _minor = UINT64_MAX;
    uint64_t _iostats_window_second;

    uint64_t _last_total_ticks = 0;
    uint64_t _last_rd_ios = 0;
    uint64_t _last_wr_ios = 0;
    uint64_t _last_rd_sec = 0;
    uint64_t _last_wr_sec = 0;

    uint64_t _diff_total_ticks = 0;
    uint64_t _diff_rd_ios = 0;
    uint64_t _diff_wr_ios = 0;
    uint64_t _diff_rd_sec = 0;
    uint64_t _diff_wr_sec = 0;

    bool _collect_succeeded = false;
    bvar::Adder<uint64_t> _delta_total_ticks; // microseconds
    std::unique_ptr<bvar::Window<bvar::Adder<uint64_t>>> _delta_total_ticks_sum; // microseconds
};

}
