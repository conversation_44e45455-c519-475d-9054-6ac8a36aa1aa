/**
 * MOCHOW - A Vector Database System.
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON> (f<PERSON><PERSON><PERSON><PERSON>@baidu.com)
 * Date: 2023/10/09
 */

#pragma once
#include "index.h"
#include "vector_index.h"
#include "brute_force_vector_index.h"
#include "core/src/schema/index_schema.h"

namespace mochow::moss {

class IndexFactory {
public:
    IndexFactory() = default;
    IndexFactory(const IndexFactory&) = delete;
    IndexFactory operator=(const IndexFactory&) = delete;

public:
    static IndexFactory& get_instance() {
        static IndexFactory instance;
        return instance;
    }

    IndexBaseRef create_vector_index(uint64_t version,
                const schema::VectorIndexSchemaRef& schema,
                const std::string& index_dir) {
        return std::make_shared<VectorIndex>(version, schema, index_dir);
    }
};

} // mochow::moss
