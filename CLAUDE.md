# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Build System

Mochow uses **BCLOUD** (Baidu's internal build system) with Python-based build configuration:

### Common Build Commands
```bash
# Build all release binaries
bcloud output release

# Build specific components
bcloud output mochow-proxy
bcloud output mochow-master
bcloud output mochow-datanode
bcloud output mochow-standalone
bcloud output mochow-admin

# Build tools
bcloud output tools

# Run unit tests (when not in release mode)
bcloud test <module>_<test_name>
# Examples:
# bcloud test com_config_test     # common module config test
# bcloud test pr_handler_test     # proxy module handler test
```

### Test Execution
- Unit tests are automatically generated from `core/test/*/test_*.cpp` files
- Test naming convention: `<module_prefix>_<test_name>`
- Tests are disabled in release builds (`MOCHOW_RELEASE_BUILD`)
- Use `-mavx2 -mpclmul -mbmi` flags for certain modules (proxy, sql, moss, scalar, zhuque, datanode)

## Architecture Overview

Mochow is a distributed vector database system with a multi-component architecture:

### Core Components
1. **Proxy** (`core/src/proxy/`) - Query processing and client interface
2. **Master** (`core/src/master/`) - Cluster metadata and coordination
3. **DataNode** (`core/src/datanode/`) - Data storage and vector operations
4. **Standalone** - Single-node deployment combining all components

### Key Subsystems

**Storage Engine**:
- **Moss** (`core/src/moss/`) - LSM-tree based storage engine with memtables, generations, and SSTables
- **SSTable** (`core/src/sstable/`) - Structured table format with block-based storage, compression, and bloom filters
- **Scalar** (`core/src/scalar/`) - RocksDB wrapper for scalar data with MVCC support
- **I/O** (`core/src/io/`) - File system abstraction and vector index file management

**Vector Indexing**:
- **VIndex** (`core/src/vindex/`) - Vector index implementations with factory pattern
- **Third-party integrations**: HNSWlib, Puck (Baidu's proprietary vector index)
- SIMD optimizations (SSE, AVX, AVX512) for distance calculations
- Sparse vector support

**Query Processing**:
- **Zhuque** (`core/src/zhuque/`) - Query execution engine with tablet-based processing, filtering, and search optimization
- **SQL** (`core/src/sql/`) - SQL parser, planner, and optimizer with conjunction/partition clause rewriting
- **Inverted Index** (`core/src/inverted_index/`) - Text search with analyzers (Chinese, standard) and LSM-based storage

**Schema & Types**:
- **Schema** (`core/src/schema/`) - Database, table, and index schema management
- **Types** (`core/src/types/`) - Data type system including JSON, arrays, datetime, and sparse vectors

### Service Layer
Each component exposes multiple RPC services:
- **ControlService** - Administrative operations
- **QueryService** - Query execution
- **DataService** - Data operations  
- **MonitorService** - Health and metrics
- **ReportService** (Master only) - Node reporting

### Development Patterns

**Configuration**:
- Flags defined per module in `flags.cpp` files
- Configuration reloading supported via `ConfigReloader`
- Different config files for each component (proxy, master, datanode, standalone)

**Testing**:
- Mock utilities in `core/test/tools/`
- Module-specific test directories under `core/test/`
- Benchmarks in `benchmark/` directory for performance testing

**RPC & Networking**:
- Built on Baidu RPC (brpc) framework
- Protocol buffers for message serialization (`core/src/proto/`)
- Async execution patterns with thread pools

**Metrics & Monitoring**:
- bvar-based metrics collection
- Periodic schedulers for health checks and metric collection
- System metric readers for resource monitoring

## Development Environment

### Dependencies
- GCC 12 with C++17 support
- Baidu internal dependencies (brpc, raft, rocksdb, etc.)
- OpenMP for parallel processing
- SIMD instruction sets (SSE4.2, AVX2, AVX512)

### Special Build Configurations
- Release builds enable aggressive optimizations (`-Ofast -march=native`)
- Address sanitizer support via `BCLOUD_NO_TCMALLOC` macro
- Memory profiler support via `MOCHOW_ENABLE_MEMORY_PROFILER`
- Test builds use additional permissive flags for mocking

### Code Organization
- Modular design with clear separation between storage, indexing, and query processing
- Consistent use of namespace `mochow`
- Header-only utilities and extensive use of templates for performance
- Factory patterns for pluggable components (indices, storage engines)